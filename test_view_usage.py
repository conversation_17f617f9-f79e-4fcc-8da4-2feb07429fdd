#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 v_zhenxuan_queryselectapplydetail 视图的使用
"""

import sys
import os
from database.db_config import DatabaseManager, ZHENXUAN_DB_CONFIG

def test_view_usage():
    """测试视图的各种查询用法"""
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    if not db_manager.connect():
        print("❌ 数据库连接失败")
        return
    
    try:
        print("🧪 测试 v_zhenxuan_queryselectapplydetail 视图的使用")
        print("="*80)
        
        with db_manager.get_cursor() as cursor:
            
            # 测试1: 基本查询 - 获取所有目标字段
            print("📋 测试1: 基本查询 - 获取指定字段")
            sql1 = """
            SELECT 
                select_apply_id, project_name, customer_name, project_code, project_no,
                select_type, select_name, select_type_value, project_type_value,
                create_time, start_time, end_time,
                apply_status_value, apply_review_status_value,
                review_file_business_id, work_order_msg_id, score_order_msg_id, score_rule_id,
                is_need_verification, is_finish_verification, non_tax_select_budget,
                action_remark, push_notice,
                is_technical_review, bid_flag_desc, bid_opening_time, rating, is_pre_review,
                select_result_doc, result_input_type, result_title, result_content, 
                doc_number_sub, doc_number,
                select_result_meet, select_result_meet_list,
                select_msg_id, realEndTime, systemEndSelectTime, selectMsgId
            FROM v_zhenxuan_queryselectapplydetail 
            LIMIT 2
            """
            
            cursor.execute(sql1)
            results = cursor.fetchall()
            
            for i, row in enumerate(results, 1):
                print(f"\n记录 {i}:")
                print(f"  申请ID: {row['select_apply_id']}")
                print(f"  项目名称: {row['project_name']}")
                print(f"  客户名称: {row['customer_name']}")
                print(f"  项目编号: {row['project_code']}")
                print(f"  甄选类型: {row['select_type_value']}")
                print(f"  申请状态: {row['apply_status_value']}")
                print(f"  评级: {row['rating']}")
                print(f"  创建时间: {row['create_time']}")
                print(f"  实际结束时间: {row['realEndTime']}")
                print(f"  系统结束时间: {row['systemEndSelectTime']}")
                print(f"  工单ID: {row['work_order_msg_id']}")
                print(f"  评分工单ID: {row['score_order_msg_id']}")
                print(f"  评分规则ID: {row['score_rule_id']}")
                if row['action_remark']:
                    print(f"  操作备注: {row['action_remark'][:100]}...")
            
            print("\n" + "="*80)
            
            # 测试2: 条件查询 - 按状态和评级筛选
            print("📋 测试2: 条件查询 - 按状态和评级筛选")
            sql2 = """
            SELECT
                select_apply_id, project_name, apply_status_value, rating
            FROM v_zhenxuan_queryselectapplydetail
            WHERE apply_status_value = '已结束'
            AND rating IN ('A', 'B', 'C')
            LIMIT 5
            """

            cursor.execute(sql2)
            results = cursor.fetchall()

            print(f"找到 {len(results)} 条已结束且评级为A/B/C的记录:")
            for i, row in enumerate(results, 1):
                print(f"  {i}. {row['project_name'][:60]}... - 评级:{row['rating']}")

            print("\n" + "="*80)

            # 测试3: 聚合查询 - 按评级统计
            print("📋 测试3: 聚合查询 - 按评级统计")
            sql3 = """
            SELECT
                rating,
                COUNT(*) as count
            FROM v_zhenxuan_queryselectapplydetail
            WHERE rating IS NOT NULL
            GROUP BY rating
            """

            cursor.execute(sql3)
            results = cursor.fetchall()

            print("评级统计:")
            for row in results:
                print(f"  评级 {row['rating']}: {row['count']} 个项目")

            print("\n" + "="*80)

            # 测试4: 简单查询 - 最新的几个项目
            print("📋 测试4: 最新的几个项目")
            sql4 = """
            SELECT
                select_apply_id, project_name, apply_status_value, rating
            FROM v_zhenxuan_queryselectapplydetail
            LIMIT 5
            """

            cursor.execute(sql4)
            results = cursor.fetchall()

            print(f"最新的5个项目:")
            for i, row in enumerate(results, 1):
                print(f"  {i}. {row['project_name'][:50]}... - 状态:{row['apply_status_value']} - 评级:{row['rating']}")

            print("\n" + "="*80)

            # 测试5: 字段完整性检查
            print("📋 测试5: 字段完整性检查")
            sql5 = """
            SELECT
                COUNT(*) as total_records,
                COUNT(select_apply_id) as has_apply_id,
                COUNT(project_name) as has_project_name,
                COUNT(rating) as has_rating,
                COUNT(realEndTime) as has_real_end_time,
                COUNT(systemEndSelectTime) as has_system_end_time,
                COUNT(selectMsgId) as has_select_msg_id
            FROM v_zhenxuan_queryselectapplydetail
            """

            cursor.execute(sql5)
            result = cursor.fetchone()

            print("字段完整性统计:")
            print(f"  总记录数: {result['total_records']}")
            print(f"  有申请ID: {result['has_apply_id']}")
            print(f"  有项目名称: {result['has_project_name']}")
            print(f"  有评级: {result['has_rating']}")
            print(f"  有实际结束时间: {result['has_real_end_time']}")
            print(f"  有系统结束时间: {result['has_system_end_time']}")
            print(f"  有选择消息ID: {result['has_select_msg_id']}")
        
        print("✅ 所有测试完成！视图工作正常。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    test_view_usage()
