#!/usr/bin/env python3
"""
数据库表统计脚本
统计甄选数据库中所有表的数据量
"""

import logging
import sys
import os
from datetime import datetime
from typing import Dict, List, Tuple
import pymysql

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import get_db_manager, ZHENXUAN_DB_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('table_stats.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class TableStatsAnalyzer:
    """数据库表统计分析器"""
    
    def __init__(self):
        """初始化统计分析器"""
        self.db_manager = get_db_manager(use_zhenxuan=True)
        self.database_name = ZHENXUAN_DB_CONFIG['database']
        
    def get_all_tables(self) -> List[str]:
        """
        获取数据库中所有表名
        
        Returns:
            List[str]: 表名列表
        """
        try:
            sql = f"SHOW TABLES FROM `{self.database_name}`"
            result = self.db_manager.execute_query(sql)
            
            if result:
                table_key = f'Tables_in_{self.database_name}'
                return [row[table_key] for row in result]
            return []
            
        except Exception as e:
            logger.error(f"❌ 获取表列表失败: {e}")
            return []
    
    def get_table_row_count(self, table_name: str) -> int:
        """
        获取表的行数
        
        Args:
            table_name: 表名
            
        Returns:
            int: 行数
        """
        try:
            sql = f"SELECT COUNT(*) as count FROM `{table_name}`"
            result = self.db_manager.execute_query(sql)
            
            if result and len(result) > 0:
                return result[0]['count']
            return 0
            
        except Exception as e:
            logger.error(f"❌ 获取表 {table_name} 行数失败: {e}")
            return 0
    
    def get_table_size_info(self, table_name: str) -> Dict[str, any]:
        """
        获取表的大小信息

        Args:
            table_name: 表名

        Returns:
            Dict: 包含表大小信息的字典
        """
        try:
            sql = """
            SELECT
                table_name,
                table_rows,
                data_length,
                index_length,
                (data_length + index_length) as total_size,
                table_comment
            FROM information_schema.tables
            WHERE table_schema = %s AND table_name = %s
            """

            result = self.db_manager.execute_query(sql, (self.database_name, table_name))

            if result and len(result) > 0:
                row = result[0]
                return {
                    'table_name': row.get('table_name', table_name),
                    'estimated_rows': row.get('table_rows', 0) or 0,
                    'data_size': row.get('data_length', 0) or 0,
                    'index_size': row.get('index_length', 0) or 0,
                    'total_size': row.get('total_size', 0) or 0,
                    'comment': row.get('table_comment', '') or ''
                }
            return {
                'table_name': table_name,
                'estimated_rows': 0,
                'data_size': 0,
                'index_size': 0,
                'total_size': 0,
                'comment': ''
            }

        except Exception as e:
            logger.error(f"❌ 获取表 {table_name} 大小信息失败: {e}")
            return {
                'table_name': table_name,
                'estimated_rows': 0,
                'data_size': 0,
                'index_size': 0,
                'total_size': 0,
                'comment': ''
            }
    
    def format_size(self, size_bytes: int) -> str:
        """
        格式化字节大小为可读格式
        
        Args:
            size_bytes: 字节数
            
        Returns:
            str: 格式化后的大小
        """
        if size_bytes == 0:
            return "0 B"
        
        units = ['B', 'KB', 'MB', 'GB', 'TB']
        unit_index = 0
        size = float(size_bytes)
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        return f"{size:.2f} {units[unit_index]}"
    
    def analyze_all_tables(self) -> List[Dict[str, any]]:
        """
        分析所有表的统计信息
        
        Returns:
            List[Dict]: 所有表的统计信息
        """
        logger.info("🔍 开始分析数据库表统计信息...")
        
        if not self.db_manager.connect():
            logger.error("❌ 数据库连接失败")
            return []
        
        try:
            tables = self.get_all_tables()
            if not tables:
                logger.warning("⚠️ 未找到任何表")
                return []
            
            logger.info(f"📊 发现 {len(tables)} 个表，开始统计...")
            
            table_stats = []
            
            for table_name in tables:
                logger.info(f"📋 正在分析表: {table_name}")
                
                # 获取精确行数
                actual_rows = self.get_table_row_count(table_name)
                
                # 获取表大小信息
                size_info = self.get_table_size_info(table_name)
                
                stats = {
                    'table_name': table_name,
                    'actual_rows': actual_rows,
                    'estimated_rows': size_info.get('estimated_rows', 0),
                    'data_size': size_info.get('data_size', 0),
                    'index_size': size_info.get('index_size', 0),
                    'total_size': size_info.get('total_size', 0),
                    'data_size_formatted': self.format_size(size_info.get('data_size', 0)),
                    'index_size_formatted': self.format_size(size_info.get('index_size', 0)),
                    'total_size_formatted': self.format_size(size_info.get('total_size', 0)),
                    'comment': size_info.get('comment', '')
                }
                
                table_stats.append(stats)
                
                logger.info(f"✅ {table_name}: {actual_rows} 行, {stats['total_size_formatted']}")
            
            return table_stats
            
        except Exception as e:
            logger.error(f"❌ 分析表统计信息失败: {e}")
            return []
        finally:
            self.db_manager.disconnect()
    
    def print_summary_report(self, table_stats: List[Dict[str, any]]):
        """
        打印汇总报告
        
        Args:
            table_stats: 表统计信息列表
        """
        if not table_stats:
            print("❌ 没有统计数据")
            return
        
        # 计算总计
        total_rows = sum(stats['actual_rows'] for stats in table_stats)
        total_data_size = sum(stats['data_size'] for stats in table_stats)
        total_index_size = sum(stats['index_size'] for stats in table_stats)
        total_size = sum(stats['total_size'] for stats in table_stats)
        
        print("\n" + "="*80)
        print(f"📊 甄选数据库 ({self.database_name}) 表统计报告")
        print("="*80)
        print(f"📅 统计时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📋 表总数: {len(table_stats)}")
        print(f"📊 总行数: {total_rows:,}")
        print(f"💾 数据大小: {self.format_size(total_data_size)}")
        print(f"🔍 索引大小: {self.format_size(total_index_size)}")
        print(f"📦 总大小: {self.format_size(total_size)}")
        print("="*80)
        
        # 按行数排序
        sorted_stats = sorted(table_stats, key=lambda x: x['actual_rows'], reverse=True)
        
        print(f"\n{'表名':<40} {'行数':<12} {'数据大小':<12} {'索引大小':<12} {'总大小':<12}")
        print("-" * 88)
        
        for stats in sorted_stats:
            print(f"{stats['table_name']:<40} "
                  f"{stats['actual_rows']:>11,} "
                  f"{stats['data_size_formatted']:>11} "
                  f"{stats['index_size_formatted']:>11} "
                  f"{stats['total_size_formatted']:>11}")
        
        print("-" * 88)
        print(f"{'总计':<40} "
              f"{total_rows:>11,} "
              f"{self.format_size(total_data_size):>11} "
              f"{self.format_size(total_index_size):>11} "
              f"{self.format_size(total_size):>11}")
        
        # 显示非空表
        non_empty_tables = [stats for stats in table_stats if stats['actual_rows'] > 0]
        if non_empty_tables:
            print(f"\n📈 非空表统计 ({len(non_empty_tables)} 个):")
            for stats in sorted(non_empty_tables, key=lambda x: x['actual_rows'], reverse=True):
                comment = f" - {stats['comment']}" if stats['comment'] else ""
                print(f"  • {stats['table_name']}: {stats['actual_rows']:,} 行{comment}")
        
        # 显示空表
        empty_tables = [stats for stats in table_stats if stats['actual_rows'] == 0]
        if empty_tables:
            print(f"\n📭 空表列表 ({len(empty_tables)} 个):")
            for stats in empty_tables:
                comment = f" - {stats['comment']}" if stats['comment'] else ""
                print(f"  • {stats['table_name']}{comment}")

def main():
    """主函数"""
    print("🚀 开始统计甄选数据库表数据量...")
    
    analyzer = TableStatsAnalyzer()
    table_stats = analyzer.analyze_all_tables()
    
    if table_stats:
        analyzer.print_summary_report(table_stats)
        print(f"\n✅ 统计完成！详细日志已保存到 table_stats.log")
    else:
        print("❌ 统计失败，请检查数据库连接和配置")

if __name__ == "__main__":
    main()
