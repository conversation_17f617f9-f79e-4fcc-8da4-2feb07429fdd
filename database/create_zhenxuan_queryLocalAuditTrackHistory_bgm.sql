-- 创建本地审核跟踪历史数据表 (BGM - 合作伙伴甄选结果反馈审核)
-- 根据 queryLocalAuditTrackHistory_bgm 接口返回的JSON数据结构创建

USE zhenxuandb;

-- 删除表（如果存在）
DROP TABLE IF EXISTS `zhenxuan_queryLocalAuditTrackHistory_bgm`;

-- 创建本地审核跟踪历史表 (BGM)
-- 严格按照JSON数据结构映射所有字段
CREATE TABLE `zhenxuan_queryLocalAuditTrackHistory_bgm` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',

  -- API请求参数（扩展字段）
  `business_id` VARCHAR(50) NOT NULL COMMENT '业务ID（入参）- 来源于zhenxuan_querySelectApplyDetail.select_apply_id',
  `work_order_msg_id` VARCHAR(100) NOT NULL COMMENT '工单消息ID（入参）- 来源于zhenxuan_querySelectApplyDetail.score_order_msg_id',
  `step_name_filter` VARCHAR(100) DEFAULT NULL COMMENT '步骤名称过滤（入参）- 固定为空字符串',
  `select_apply_id` VARCHAR(50) DEFAULT NULL COMMENT '甄选申请ID - 来源于zhenxuan_querySelectApplyDetail.select_apply_id',
  `score_order_msg_id` VARCHAR(50) DEFAULT NULL COMMENT '评分工单消息ID - 来源于zhenxuan_querySelectApplyDetail.score_order_msg_id',
  `request_params` JSON DEFAULT NULL COMMENT '请求参数JSON',

  -- 响应基础信息（根级字段）
  `busi_date` DATETIME DEFAULT NULL COMMENT '业务日期 - busiDate',
  `code` VARCHAR(20) DEFAULT NULL COMMENT '响应代码 - code',
  `message` TEXT DEFAULT NULL COMMENT '响应消息 - message',

  -- resultBody中的审核跟踪历史信息
  `audit_process_track_id` VARCHAR(50) DEFAULT NULL COMMENT '审核流程跟踪ID - auditProcessTrackId',
  `step_name` VARCHAR(200) DEFAULT NULL COMMENT '步骤名称 - stepName',
  `create_time` DATETIME DEFAULT NULL COMMENT '创建时间 - createTime',
  `finish_time` DATETIME DEFAULT NULL COMMENT '完成时间 - finishTime',
  `status` VARCHAR(50) DEFAULT NULL COMMENT '状态 - status',
  `audit_handler` VARCHAR(100) DEFAULT NULL COMMENT '审核处理人 - auditHandler',
  `audit_remark` TEXT DEFAULT NULL COMMENT '审核备注 - auditRemark',

  -- 原始数据
  `raw_data` JSON DEFAULT NULL COMMENT '原始JSON数据',

  -- 系统字段
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',

  -- 索引设计
  INDEX `idx_business_id` (`business_id`) COMMENT '业务ID索引',
  INDEX `idx_work_order_msg_id` (`work_order_msg_id`) COMMENT '工单消息ID索引',
  INDEX `idx_select_apply_id` (`select_apply_id`) COMMENT '甄选申请ID索引',
  INDEX `idx_score_order_msg_id` (`score_order_msg_id`) COMMENT '评分工单消息ID索引',
  INDEX `idx_audit_process_track_id` (`audit_process_track_id`) COMMENT '审核流程跟踪ID索引',
  INDEX `idx_step_name` (`step_name`) COMMENT '步骤名称索引',
  INDEX `idx_create_time` (`create_time`) COMMENT '创建时间索引',
  INDEX `idx_finish_time` (`finish_time`) COMMENT '完成时间索引',
  INDEX `idx_status` (`status`) COMMENT '状态索引',
  INDEX `idx_audit_handler` (`audit_handler`) COMMENT '审核处理人索引',
  INDEX `idx_created_at` (`created_at`) COMMENT '记录创建时间索引',

  -- 复合索引
  INDEX `idx_business_work_order` (`business_id`, `work_order_msg_id`) COMMENT '业务ID和工单消息ID复合索引',
  INDEX `idx_business_step` (`business_id`, `step_name`) COMMENT '业务ID和步骤名称复合索引',
  INDEX `idx_apply_step` (`select_apply_id`, `step_name`) COMMENT '申请ID和步骤名称复合索引',

  -- 唯一约束：防止重复数据
  UNIQUE KEY `uk_audit_track_bgm_unique` (`business_id`, `work_order_msg_id`, `audit_process_track_id`) COMMENT 'BGM审核跟踪唯一约束'

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='本地审核跟踪历史数据表(BGM-合作伙伴甄选结果反馈审核)';

-- 创建视图：简化查询
CREATE OR REPLACE VIEW `v_zhenxuan_audit_track_bgm_summary` AS
SELECT
    id,
    business_id,
    work_order_msg_id,
    select_apply_id,
    step_name,
    status,
    audit_handler,
    create_time,
    finish_time,
    audit_remark,
    created_at
FROM `zhenxuan_queryLocalAuditTrackHistory_bgm`
ORDER BY business_id, create_time;

-- 创建关联查询视图：与甄选申请详情表关联
CREATE OR REPLACE VIEW `v_zhenxuan_apply_audit_bgm_relation` AS
SELECT
    a.id as apply_id,
    a.select_apply_id,
    a.project_name,
    a.customer_name,
    a.project_code,
    a.rating,
    a.apply_status_value,
    b.id as audit_id,
    b.business_id,
    b.work_order_msg_id,
    b.step_name,
    b.status as audit_status,
    b.audit_handler,
    b.create_time as audit_create_time,
    b.finish_time as audit_finish_time,
    b.audit_remark
FROM `zhenxuan_querySelectApplyDetail` a
LEFT JOIN `zhenxuan_queryLocalAuditTrackHistory_bgm` b
    ON a.select_apply_id = b.business_id
    AND a.score_order_msg_id = b.work_order_msg_id
ORDER BY a.select_apply_id, b.create_time;

-- 验证表创建
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_COLLATION
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'zhenxuandb' 
AND TABLE_NAME = 'zhenxuan_queryLocalAuditTrackHistory_bgm';

-- 验证字段结构
DESCRIBE `zhenxuan_queryLocalAuditTrackHistory_bgm`;

-- 验证索引创建
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    INDEX_COMMENT
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'zhenxuandb' 
AND TABLE_NAME = 'zhenxuan_queryLocalAuditTrackHistory_bgm'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- 验证视图创建
SELECT 
    TABLE_NAME,
    TABLE_TYPE
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'zhenxuandb' 
AND TABLE_NAME LIKE '%audit%bgm%'
ORDER BY TABLE_NAME;
