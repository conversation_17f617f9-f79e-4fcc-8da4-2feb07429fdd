-- 创建甄选项目详情查询数据表
-- 根据 querySelectProjectDetail 接口返回的JSON数据结构创建

USE zhenxuandb;

-- 删除表（如果存在）
DROP TABLE IF EXISTS `zhenxuan_querySelectProjectDetail`;

-- 创建甄选项目详情查询表
-- 严格按照JSON数据结构映射所有字段
CREATE TABLE `zhenxuan_querySelectProjectDetail` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',

  -- API请求参数（扩展字段）
  `project_msg_id` VARCHAR(50) NOT NULL COMMENT '项目消息ID（入参）- 来源于zhenxuan_queryPartnerSelectDetail.projectMsgId',
  `request_params` JSON DEFAULT NULL COMMENT '请求参数JSON',

  -- 响应基础信息（根级字段）
  `busi_date` DATETIME DEFAULT NULL COMMENT '业务日期 - busiDate',
  `code` VARCHAR(20) DEFAULT NULL COMMENT '响应代码 - code',
  `message` TEXT DEFAULT NULL COMMENT '响应消息 - message',

  -- resultBody核心项目信息
  `work_order_msg_id` VARCHAR(50) DEFAULT NULL COMMENT '工单消息ID - workOrderMsgId',
  `shut_order_msg_id` VARCHAR(50) DEFAULT NULL COMMENT '关闭工单消息ID - shutOrderMsgId',
  `project_name` VARCHAR(200) DEFAULT NULL COMMENT '项目名称 - projectName',
  `project_code` VARCHAR(50) DEFAULT NULL COMMENT '项目代码 - projectCode',
  `group_project_code` VARCHAR(50) DEFAULT NULL COMMENT '集团项目代码 - groupProjectCode',
  `project_type` VARCHAR(20) DEFAULT NULL COMMENT '项目类型 - projectType',
  `project_type_value` VARCHAR(50) DEFAULT NULL COMMENT '项目类型值 - projectTypeValue',
  `industry` VARCHAR(20) DEFAULT NULL COMMENT '行业 - industry',
  `industry_value` VARCHAR(50) DEFAULT NULL COMMENT '行业值 - industryValue',
  `is_pmo_manage` VARCHAR(10) DEFAULT NULL COMMENT '是否PMO管理 - isPmoManage',
  `is_pmo_manage_value` VARCHAR(20) DEFAULT NULL COMMENT '是否PMO管理值 - isPmoManageValue',
  `is_sub_sign` VARCHAR(20) DEFAULT NULL COMMENT '是否分包签约 - isSubSign',
  `is_sub_sign_value` VARCHAR(50) DEFAULT NULL COMMENT '是否分包签约值 - isSubSignValue',
  `is_investment_project` VARCHAR(10) DEFAULT NULL COMMENT '是否投资项目 - isInvestmentProject',
  `is_investment_project_value` VARCHAR(20) DEFAULT NULL COMMENT '是否投资项目值 - isInvestmentProjectValue',
  `engineering_project_code` VARCHAR(50) DEFAULT NULL COMMENT '工程项目代码 - engineeringProjectCode',
  `project_label` VARCHAR(20) DEFAULT NULL COMMENT '项目标签 - projectLabel',
  `project_label_value` VARCHAR(50) DEFAULT NULL COMMENT '项目标签值 - projectLabelValue',

  -- 甄选相关信息
  `select_name` VARCHAR(200) DEFAULT NULL COMMENT '甄选名称 - selectName',
  `select_status` VARCHAR(20) DEFAULT NULL COMMENT '甄选状态 - selectStatus',
  `select_status_value` VARCHAR(50) DEFAULT NULL COMMENT '甄选状态值 - selectStatusValue',
  `select_type` VARCHAR(20) DEFAULT NULL COMMENT '甄选类型 - selectType',
  `select_type_value` VARCHAR(50) DEFAULT NULL COMMENT '甄选类型值 - selectTypeValue',
  `start_time` DATE DEFAULT NULL COMMENT '开始时间 - startTime',
  `end_time` DATE DEFAULT NULL COMMENT '结束时间 - endTime',
  `select_demand_content` TEXT DEFAULT NULL COMMENT '甄选需求内容 - selectDemandContent',
  `select_budget` DECIMAL(15,2) DEFAULT NULL COMMENT '甄选预算 - selectBudget',
  `select_budget_value` VARCHAR(50) DEFAULT NULL COMMENT '甄选预算值 - selectBudgetValue',
  `non_tax_select_budget` DECIMAL(15,2) DEFAULT NULL COMMENT '不含税甄选预算 - nonTaxSelectBudget',
  `non_tax_select_budget_value` VARCHAR(50) DEFAULT NULL COMMENT '不含税甄选预算值 - nonTaxSelectBudgetValue',
  `is_fixed_softness` VARCHAR(10) DEFAULT NULL COMMENT '是否固定软性 - isFixedSoftness',
  `is_fixed_softness_value` VARCHAR(20) DEFAULT NULL COMMENT '是否固定软性值 - isFixedSoftnessValue',

  -- 甄选依据相关
  `select_basis` VARCHAR(50) DEFAULT NULL COMMENT '甄选依据 - selectBasis',
  `select_basis2` VARCHAR(50) DEFAULT NULL COMMENT '甄选依据2 - selectBasis2',
  `select_basis3` VARCHAR(50) DEFAULT NULL COMMENT '甄选依据3 - selectBasis3',
  `select_basis4` VARCHAR(50) DEFAULT NULL COMMENT '甄选依据4 - selectBasis4',
  `select_notice_type` VARCHAR(20) DEFAULT NULL COMMENT '甄选通知类型 - selectNoticeType',
  `select_notice_type_value` VARCHAR(50) DEFAULT NULL COMMENT '甄选通知类型值 - selectNoticeTypeValue',
  `city_decide_file` VARCHAR(100) DEFAULT NULL COMMENT '城市决定文件 - cityDecideFile',

  -- 业务区域和创建信息
  `business_area` VARCHAR(20) DEFAULT NULL COMMENT '业务区域 - businessArea',
  `business_area_value` VARCHAR(50) DEFAULT NULL COMMENT '业务区域值 - businessAreaValue',
  `create_time` DATETIME DEFAULT NULL COMMENT '创建时间 - createTime',
  `create_staff` VARCHAR(50) DEFAULT NULL COMMENT '创建人员 - createStaff',
  `initiate_department` VARCHAR(100) DEFAULT NULL COMMENT '发起部门 - initiateDepartment',

  -- 客户和场景信息
  `customer_name` VARCHAR(200) DEFAULT NULL COMMENT '客户名称 - customerName',
  `first_scene` VARCHAR(50) DEFAULT NULL COMMENT '一级场景 - firstScene',
  `first_scene_value` VARCHAR(100) DEFAULT NULL COMMENT '一级场景值 - firstSceneValue',
  `second_scene` VARCHAR(50) DEFAULT NULL COMMENT '二级场景 - secondScene',
  `second_scene_value` VARCHAR(100) DEFAULT NULL COMMENT '二级场景值 - secondSceneValue',
  `project_no` VARCHAR(50) DEFAULT NULL COMMENT '项目编号 - projectNo',
  `iproject_id` VARCHAR(50) DEFAULT NULL COMMENT 'I项目ID - iprojectId',

  -- 终止相关信息
  `termination_reason` VARCHAR(50) DEFAULT NULL COMMENT '终止原因 - terminationReason',
  `termination_reason_value` VARCHAR(100) DEFAULT NULL COMMENT '终止原因值 - terminationReasonValue',
  `other_explain` TEXT DEFAULT NULL COMMENT '其他说明 - otherExplain',
  `termination_explain` TEXT DEFAULT NULL COMMENT '终止说明 - terminationExplain',

  -- 联系信息
  `phone` VARCHAR(20) DEFAULT NULL COMMENT '电话 - phone',
  `employee_name` VARCHAR(50) DEFAULT NULL COMMENT '员工姓名 - employeeName',
  `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱 - email',

  -- 处理人信息
  `next_todo_handler` VARCHAR(50) DEFAULT NULL COMMENT '下一待办处理人 - nextTodoHandler',
  `next_todo_handler_value` VARCHAR(50) DEFAULT NULL COMMENT '下一待办处理人值 - nextTodoHandlerValue',
  `next_audit_handler` VARCHAR(50) DEFAULT NULL COMMENT '下一审核处理人 - nextAuditHandler',
  `next_shutdown_handler` VARCHAR(50) DEFAULT NULL COMMENT '下一关闭处理人 - nextShutdownHandler',

  -- 关闭相关
  `shutdown_basis` VARCHAR(50) DEFAULT NULL COMMENT '关闭依据 - shutdownBasis',
  `current_audit_step` VARCHAR(10) DEFAULT NULL COMMENT '当前审核步骤 - currentAuditStep',
  `current_audit_step1` VARCHAR(10) DEFAULT NULL COMMENT '当前审核步骤1 - currentAuditStep1',

  -- 甄选分类和需求类型
  `select_category` VARCHAR(10) DEFAULT NULL COMMENT '甄选分类 - selectCategory',
  `select_category_value` VARCHAR(50) DEFAULT NULL COMMENT '甄选分类值 - selectCategoryValue',
  `select_demand_type` VARCHAR(10) DEFAULT NULL COMMENT '甄选需求类型 - selectDemandType',
  `select_demand_type_value` VARCHAR(50) DEFAULT NULL COMMENT '甄选需求类型值 - selectDemandTypeValue',

  -- 解决方案相关
  `is_t_solution` VARCHAR(10) DEFAULT NULL COMMENT '是否T解决方案 - isTSolution',
  `is_non_presale` VARCHAR(10) DEFAULT NULL COMMENT '是否非售前 - isNonPresale',
  `non_presale_reason` TEXT DEFAULT NULL COMMENT '非售前原因 - nonPresaleReason',
  `non_presale_file` VARCHAR(200) DEFAULT NULL COMMENT '非售前文件 - nonPresaleFile',

  -- 项目进度相关
  `project_stage` VARCHAR(50) DEFAULT NULL COMMENT '项目阶段 - projectStage',
  `project_progress` VARCHAR(50) DEFAULT NULL COMMENT '项目进度 - projectProgress',
  `project_scope` VARCHAR(20) DEFAULT NULL COMMENT '项目范围 - projectScope',
  `doc_number_sub` VARCHAR(100) DEFAULT NULL COMMENT '文档编号子 - docNumberSub',

  -- 原始数据和文件信息
  `select_basis_vo` JSON DEFAULT NULL COMMENT '甄选依据VO - selectBasisVo',
  `select_basis2_vo` JSON DEFAULT NULL COMMENT '甄选依据2VO - selectBasis2Vo',
  `select_basis3_vo` JSON DEFAULT NULL COMMENT '甄选依据3VO - selectBasis3Vo',
  `select_basis4_vo` JSON DEFAULT NULL COMMENT '甄选依据4VO - selectBasis4Vo',
  `city_decide_file_vo` JSON DEFAULT NULL COMMENT '城市决定文件VO - cityDecideFileVo',
  `shutdown_basis_vo` JSON DEFAULT NULL COMMENT '关闭依据VO - shutdownBasisVo',
  `non_presale_file_list` JSON DEFAULT NULL COMMENT '非售前文件列表 - nonPresaleFileList',
  `raw_data` JSON DEFAULT NULL COMMENT '原始JSON数据',

  -- 系统字段
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',

  -- 索引
  UNIQUE KEY `uk_project_msg_id` (`project_msg_id`),
  KEY `idx_project_code` (`project_code`),
  KEY `idx_project_name` (`project_name`),
  KEY `idx_select_status` (`select_status`),
  KEY `idx_business_area` (`business_area`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_customer_name` (`customer_name`),
  KEY `idx_project_no` (`project_no`),
  KEY `idx_create_staff` (`create_staff`),
  KEY `idx_composite_status_area` (`select_status`, `business_area`),
  KEY `idx_composite_time_status` (`create_time`, `select_status`)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='甄选项目详情查询数据表';

-- 创建视图：简化查询
CREATE OR REPLACE VIEW `v_zhenxuan_project_detail_summary` AS
SELECT 
    id,
    project_msg_id,
    project_name,
    project_code,
    select_name,
    select_status_value,
    business_area_value,
    customer_name,
    create_staff,
    create_time,
    start_time,
    end_time,
    select_budget_value,
    project_type_value
FROM `zhenxuan_querySelectProjectDetail`
ORDER BY create_time DESC;

-- 验证表创建
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_COLLATION
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'zhenxuandb' 
AND TABLE_NAME = 'zhenxuan_querySelectProjectDetail';

-- 验证字段结构
DESCRIBE `zhenxuan_querySelectProjectDetail`;
