-- 甄选数据库创建脚本
-- 数据库名称: zhenxuandb
-- 创建时间: 2025-07-08
-- 基于API接口分析的字段结构设计

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `zhenxuandb` 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_unicode_ci;

USE `zhenxuandb`;

-- 1. 甄选项目主表 (基于querySelectProjectList接口)
CREATE TABLE `selection_projects` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
  `project_msg_id` VARCHAR(50) NOT NULL COMMENT '项目消息ID',
  `work_order_msg_id` VARCHAR(50) DEFAULT NULL COMMENT '工单消息ID',
  `shut_order_msg_id` VARCHAR(50) DEFAULT NULL COMMENT '关闭工单消息ID',
  `select_msg_id` VARCHAR(50) DEFAULT NULL COMMENT '甄选消息ID',
  `select_apply_id` VARCHAR(50) DEFAULT NULL COMMENT '甄选申请ID',
  `project_name` VARCHAR(200) NOT NULL COMMENT '项目名称',
  `select_name` VARCHAR(200) NOT NULL COMMENT '甄选需求名称',
  `count` INT DEFAULT 0 COMMENT '甄选方案数量',
  `project_no` VARCHAR(50) NOT NULL COMMENT '项目编码',
  `select_type` VARCHAR(20) DEFAULT NULL COMMENT '甄选类型',
  `select_type_value` VARCHAR(50) DEFAULT NULL COMMENT '甄选类型值',
  `project_type` VARCHAR(20) DEFAULT NULL COMMENT '项目类型',
  `project_label` VARCHAR(20) DEFAULT NULL COMMENT '项目标签',
  `business_area` VARCHAR(20) DEFAULT NULL COMMENT '归属地市编码',
  `business_area_value` VARCHAR(50) DEFAULT NULL COMMENT '归属地市名称',
  `start_time` DATETIME DEFAULT NULL COMMENT '开始时间',
  `select_status` VARCHAR(20) DEFAULT NULL COMMENT '甄选需求状态编码',
  `select_status_value` VARCHAR(50) DEFAULT NULL COMMENT '甄选需求状态值',
  `initiate_department` VARCHAR(100) DEFAULT NULL COMMENT '发起部门',
  `create_time` DATETIME NOT NULL COMMENT '创建时间',
  `is_fixed_softness` TINYINT DEFAULT 0 COMMENT '是否固定软件',
  `create_staff` VARCHAR(50) DEFAULT NULL COMMENT '创建人员',
  `create_staff_value` VARCHAR(50) DEFAULT NULL COMMENT '创建人员姓名',
  `next_todo_handler` VARCHAR(50) DEFAULT NULL COMMENT '下一步处理人',
  `next_todo_handler_value` VARCHAR(50) DEFAULT NULL COMMENT '下一步处理人姓名',
  `is_operable` TINYINT DEFAULT 0 COMMENT '是否可操作',
  `change_type1` VARCHAR(20) DEFAULT NULL COMMENT '变更类型1',
  `change_type2` VARCHAR(20) DEFAULT NULL COMMENT '变更类型2',
  `is_terminable` TINYINT DEFAULT 0 COMMENT '是否可终止',
  `is_allow_second` TINYINT DEFAULT NULL COMMENT '是否允许二次',
  `select_category` VARCHAR(20) DEFAULT NULL COMMENT '甄选类别编码',
  `select_category_value` VARCHAR(50) DEFAULT NULL COMMENT '甄选类别值',
  `dpcs_select_second_negotiate` VARCHAR(100) DEFAULT NULL COMMENT 'DPCS甄选二次协商',
  `data_source` VARCHAR(50) DEFAULT 'api' COMMENT '数据来源',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  
  -- 索引
  UNIQUE KEY `uk_project_msg_id` (`project_msg_id`),
  KEY `idx_project_no` (`project_no`),
  KEY `idx_project_name` (`project_name`),
  KEY `idx_select_name` (`select_name`),
  KEY `idx_business_area` (`business_area`),
  KEY `idx_select_status` (`select_status`),
  KEY `idx_select_category` (`select_category`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='甄选项目主表';

-- 2. 数据字典表 (基于getDictListByGroupId2接口)
CREATE TABLE `dict_data` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
  `dict_id` VARCHAR(50) NOT NULL COMMENT '字典ID',
  `dict_name` VARCHAR(100) NOT NULL COMMENT '字典名称',
  `group_id` VARCHAR(50) NOT NULL COMMENT '分组ID',
  `descrp` TEXT DEFAULT NULL COMMENT '描述',
  `other_info` TEXT DEFAULT NULL COMMENT '其他信息',
  `sort_order` INT DEFAULT 0 COMMENT '排序',
  `is_active` TINYINT DEFAULT 1 COMMENT '是否激活',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  -- 索引
  UNIQUE KEY `uk_dict_id_group` (`dict_id`, `group_id`),
  KEY `idx_group_id` (`group_id`),
  KEY `idx_dict_name` (`dict_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据字典表';

-- 3. 城市地区表 (基于getCityListByType接口)
CREATE TABLE `city_areas` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
  `dict_id` VARCHAR(50) NOT NULL COMMENT '城市ID',
  `dict_name` VARCHAR(100) NOT NULL COMMENT '城市名称',
  `group_id` VARCHAR(50) DEFAULT NULL COMMENT '分组ID',
  `descrp` TEXT DEFAULT NULL COMMENT '描述',
  `op_type` TINYINT DEFAULT 1 COMMENT '操作类型',
  `parent_id` VARCHAR(50) DEFAULT NULL COMMENT '父级城市ID',
  `level` TINYINT DEFAULT 1 COMMENT '级别(1:省 2:市 3:区)',
  `is_active` TINYINT DEFAULT 1 COMMENT '是否激活',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  -- 索引
  UNIQUE KEY `uk_dict_id` (`dict_id`),
  KEY `idx_dict_name` (`dict_name`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='城市地区表';

-- 4. API请求日志表
CREATE TABLE `api_request_logs` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
  `request_url` VARCHAR(500) NOT NULL COMMENT '请求URL',
  `request_method` VARCHAR(10) NOT NULL COMMENT '请求方法',
  `request_params` TEXT DEFAULT NULL COMMENT '请求参数',
  `response_status` INT DEFAULT NULL COMMENT '响应状态码',
  `response_data` LONGTEXT DEFAULT NULL COMMENT '响应数据',
  `request_time` DATETIME NOT NULL COMMENT '请求时间',
  `response_time` DATETIME DEFAULT NULL COMMENT '响应时间',
  `duration_ms` INT DEFAULT NULL COMMENT '耗时(毫秒)',
  `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  -- 索引
  KEY `idx_request_url` (`request_url`(255)),
  KEY `idx_request_time` (`request_time`),
  KEY `idx_response_status` (`response_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API请求日志表';

-- 5. 数据同步状态表
CREATE TABLE `sync_status` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
  `sync_type` VARCHAR(50) NOT NULL COMMENT '同步类型',
  `last_sync_time` DATETIME DEFAULT NULL COMMENT '最后同步时间',
  `sync_count` INT DEFAULT 0 COMMENT '同步数量',
  `sync_status` TINYINT DEFAULT 0 COMMENT '同步状态(0:失败 1:成功 2:进行中)',
  `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  -- 索引
  UNIQUE KEY `uk_sync_type` (`sync_type`),
  KEY `idx_last_sync_time` (`last_sync_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据同步状态表';
