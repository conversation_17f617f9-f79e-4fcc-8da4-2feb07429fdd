-- 创建甄选项目查询列表数据表
-- 严格根据 querySelectProjectList 接口返回的JSON数据结构创建

USE zhenxuandb;

-- 删除表（如果存在）
DROP TABLE IF EXISTS `zhenxuan_querySelectProjectList`;

-- 创建甄选项目查询列表表
-- 严格按照JSON数据结构映射所有字段
CREATE TABLE `zhenxuan_querySelectProjectList` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',

  -- API请求参数（扩展字段）
  `select_rev_id` VARCHAR(100) DEFAULT NULL COMMENT '甄选版本ID（入参）',
  `request_params` JSON DEFAULT NULL COMMENT '请求参数JSON',

  -- 响应基础信息（根级字段）
  `busi_date` DATETIME DEFAULT NULL COMMENT '业务日期 - busiDate',
  `code` VARCHAR(20) DEFAULT NULL COMMENT '响应代码 - code',
  `message` TEXT DEFAULT NULL COMMENT '响应消息 - message',

  -- resultBody分页信息
  `total` INT DEFAULT 0 COMMENT '总记录数 - resultBody.total',
  `size` INT DEFAULT 0 COMMENT '每页大小 - resultBody.size',
  `current` INT DEFAULT 0 COMMENT '当前页码 - resultBody.current',
  `pages` INT DEFAULT 0 COMMENT '总页数 - resultBody.pages',

  -- records数组中的项目核心信息
  `project_msg_id` VARCHAR(50) NOT NULL COMMENT '项目消息ID - projectMsgId',
  `work_order_msg_id` VARCHAR(50) DEFAULT NULL COMMENT '工单消息ID - workOrderMsgId',
  `shut_order_msg_id` VARCHAR(50) DEFAULT NULL COMMENT '关闭工单消息ID - shutOrderMsgId',
  `select_msg_id` VARCHAR(50) DEFAULT NULL COMMENT '甄选消息ID - selectMsgId',
  `select_apply_id` VARCHAR(50) DEFAULT NULL COMMENT '甄选申请ID - selectApplyId',

  -- 项目基本信息
  `project_name` VARCHAR(200) NOT NULL COMMENT '项目名称 - projectName',
  `select_name` VARCHAR(200) NOT NULL COMMENT '甄选需求名称 - selectName',
  `count` VARCHAR(10) DEFAULT '0' COMMENT '甄选方案数量 - count',
  `project_no` VARCHAR(50) DEFAULT NULL COMMENT '项目编码 - projectNo',

  -- 甄选类型
  `select_type` VARCHAR(20) DEFAULT NULL COMMENT '甄选类型 - selectType',
  `select_type_value` VARCHAR(50) DEFAULT NULL COMMENT '甄选类型值 - selectTypeValue',

  -- 项目分类
  `project_type` VARCHAR(20) DEFAULT NULL COMMENT '项目类型 - projectType',
  `project_label` VARCHAR(20) DEFAULT NULL COMMENT '项目标签 - projectLabel',

  -- 业务区域
  `business_area` VARCHAR(20) DEFAULT NULL COMMENT '业务区域代码 - businessArea',
  `business_area_value` VARCHAR(50) DEFAULT NULL COMMENT '业务区域名称 - businessAreaValue',

  -- 时间信息
  `start_time` DATETIME DEFAULT NULL COMMENT '开始时间 - startTime',
  `create_time` DATETIME DEFAULT NULL COMMENT '创建时间 - createTime',

  -- 状态信息
  `select_status` VARCHAR(20) DEFAULT NULL COMMENT '甄选状态代码 - selectStatus',
  `select_status_value` VARCHAR(50) DEFAULT NULL COMMENT '甄选状态值 - selectStatusValue',

  -- 部门和人员
  `initiate_department` VARCHAR(100) DEFAULT NULL COMMENT '发起部门 - initiateDepartment',
  `create_staff` VARCHAR(50) DEFAULT NULL COMMENT '创建人员代码 - createStaff',
  `create_staff_value` VARCHAR(50) DEFAULT NULL COMMENT '创建人员姓名 - createStaffValue',
  `next_todo_handler` VARCHAR(50) DEFAULT NULL COMMENT '下一步处理人代码 - nextTodoHandler',
  `next_todo_handler_value` VARCHAR(50) DEFAULT NULL COMMENT '下一步处理人姓名 - nextTodoHandlerValue',

  -- 操作标识
  `is_fixed_softness` VARCHAR(10) DEFAULT '0' COMMENT '是否固定软件 - isFixedSoftness',
  `is_operable` VARCHAR(10) DEFAULT '0' COMMENT '是否可操作 - isOperable',
  `is_terminable` VARCHAR(10) DEFAULT '0' COMMENT '是否可终止 - isTerminable',
  `is_allow_second` VARCHAR(10) DEFAULT NULL COMMENT '是否允许二次 - isAllowSecond',

  -- 变更类型
  `change_type1` VARCHAR(50) DEFAULT NULL COMMENT '变更类型1 - changeType1',
  `change_type2` VARCHAR(50) DEFAULT NULL COMMENT '变更类型2 - changeType2',

  -- 甄选分类
  `select_category` VARCHAR(20) DEFAULT NULL COMMENT '甄选分类代码 - selectCategory',
  `select_category_value` VARCHAR(50) DEFAULT NULL COMMENT '甄选分类值 - selectCategoryValue',

  -- 二次谈判
  `dpcs_select_second_negotiate` VARCHAR(50) DEFAULT NULL COMMENT '二次谈判标识 - dpcsSelectSecondNegotiate',
  
  -- 原始数据
  `raw_data` JSON DEFAULT NULL COMMENT '原始JSON数据',
  
  -- 系统字段
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  
  -- 索引
  UNIQUE KEY `uk_project_msg_id` (`project_msg_id`),
  KEY `idx_project_no` (`project_no`),
  KEY `idx_select_status` (`select_status`),
  KEY `idx_business_area` (`business_area`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_create_staff` (`create_staff`),
  KEY `idx_select_category` (`select_category`),
  KEY `idx_composite_status_area` (`select_status`, `business_area`),
  KEY `idx_composite_time_status` (`create_time`, `select_status`)
  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='甄选项目查询列表数据表';

-- 创建视图：简化查询
CREATE OR REPLACE VIEW `v_zhenxuan_project_summary` AS
SELECT 
    id,
    project_msg_id,
    project_name,
    select_name,
    project_no,
    business_area_value,
    select_status_value,
    create_staff_value,
    create_time,
    start_time
FROM `zhenxuan_querySelectProjectList`
ORDER BY create_time DESC;

-- 插入测试说明
INSERT INTO `zhenxuan_querySelectProjectList` 
(`project_msg_id`, `project_name`, `select_name`, `project_no`, `select_status`, `select_status_value`, `raw_data`) 
VALUES 
('test_001', '测试项目', '测试甄选', 'TEST001', '1001', '审核通过', '{"test": true}');

-- 验证表创建
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_COLLATION
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'zhenxuandb' 
AND TABLE_NAME = 'zhenxuan_querySelectProjectList';

-- 验证字段结构
DESCRIBE `zhenxuan_querySelectProjectList`;
