# 甄选数据库表统计汇总报告

## 📊 总体概况

| 项目 | 数值 |
|------|------|
| 数据库名称 | zhenxuandb |
| 表总数 | 27 个 |
| 总数据行数 | **23,485 行** |
| 总存储大小 | **58.00 MB** |
| 统计时间 | 2025-07-09 19:10:54 |

## 📈 数据分布概览

### 按数据量排序 (Top 10)

| 排名 | 表名 | 行数 | 存储大小 | 占比 |
|------|------|------|----------|------|
| 1 | zhenxuan_querylocalaudittrackhistory_ksm | 4,359 | 96.00 KB | 18.6% |
| 2 | v_zhenxuan_audit_track_summary | 3,704 | 0 B | 15.8% |
| 3 | zhenxuan_queryselectaudittrackhistory | 3,704 | 144.00 KB | 15.8% |
| 4 | v_zhenxuan_project_audit_relation | 1,535 | 0 B | 6.5% |
| 5 | zhenxuan_queryselectprojectlist | 1,533 | 1.83 MB | 6.5% |
| 6 | v_zhenxuan_stage_audit_relation | 1,438 | 0 B | 6.1% |
| 7 | v_zhenxuan_stage_summary | 1,438 | 0 B | 6.1% |
| 8 | zhenxuan_queryselectprojectlist_copy1 | 1,438 | 176.00 KB | 6.1% |
| 9 | zhenxuan_queryselectstage | 1,438 | 128.00 KB | 6.1% |
| 10 | zhenxuan_querypartnerselectdetail | 1,177 | 208.00 KB | 5.0% |

### 按存储大小排序 (Top 5)

| 排名 | 表名 | 存储大小 | 行数 | 平均行大小 |
|------|------|----------|------|------------|
| 1 | zhenxuan_queryselectapplydetail | **53.70 MB** | 206 | 267.5 KB/行 |
| 2 | zhenxuan_queryselectprojectlist | 1.83 MB | 1,533 | 1.2 KB/行 |
| 3 | zhenxuan_queryselectprojectdetail | 256.00 KB | 11 | 23.3 KB/行 |
| 4 | zhenxuan_querynoticehistorybyselectid | 256.00 KB | 65 | 3.9 KB/行 |
| 5 | zhenxuan_querylocalaudittrackhistory_bgm | 240.00 KB | 12 | 20.0 KB/行 |

## 📂 按功能分类统计

### 🏷️ 核心业务表 (5个表)
> 甄选业务核心数据表，存储项目、合作伙伴、申请等主要业务信息

| 表名 | 行数 | 存储大小 | 说明 |
|------|------|----------|------|
| zhenxuan_queryselectprojectlist | 1,533 | 1.83 MB | 甄选项目列表 |
| zhenxuan_queryselectstage | 1,438 | 128.00 KB | 甄选阶段信息 |
| zhenxuan_querypartnerselectdetail | 1,177 | 208.00 KB | 合作伙伴甄选详情 |
| zhenxuan_queryselectapplydetail | 206 | **53.70 MB** | 甄选申请详情 ⭐ |
| zhenxuan_queryselectprojectdetail | 11 | 256.00 KB | 甄选项目详情 |
| **小计** | **4,365** | **56.11 MB** | **96.7%存储占比** |

### 🏷️ 审计跟踪表 (6个表)
> 审计流程跟踪表，记录各个环节的审批历史和状态变更

| 表名 | 行数 | 存储大小 | 说明 |
|------|------|----------|------|
| zhenxuan_querylocalaudittrackhistory_ksm | 4,359 | 96.00 KB | 本地审计跟踪历史(KSM) |
| zhenxuan_queryselectaudittrackhistory | 3,704 | 144.00 KB | 甄选审计跟踪历史 |
| zhenxuan_querylocalaudittrackhistory_xqxx | 874 | 160.00 KB | 本地审计跟踪历史(需求信息) |
| zhenxuan_querylocalaudittrackhistory_ps | 24 | 224.00 KB | 本地审计跟踪历史(PS) |
| zhenxuan_querylocalaudittrackhistory | 15 | 192.00 KB | 本地审计跟踪历史 |
| zhenxuan_querylocalaudittrackhistory_bgm | 12 | 240.00 KB | 本地审计跟踪历史(BGM) |
| **小计** | **8,988** | **1.03 MB** | **1.8%存储占比** |

### 🏷️ 业务视图 (9个表)
> 业务数据汇总视图，用于数据分析和报表展示

| 表名 | 行数 | 存储大小 | 说明 |
|------|------|----------|------|
| v_zhenxuan_audit_track_summary | 3,704 | 0 B | 审计跟踪汇总视图 |
| v_zhenxuan_project_audit_relation | 1,535 | 0 B | 项目审计关系视图 |
| v_zhenxuan_stage_audit_relation | 1,438 | 0 B | 阶段审计关系视图 |
| v_zhenxuan_stage_summary | 1,438 | 0 B | 阶段汇总视图 |
| v_zhenxuan_apply_audit_bgm_relation | 217 | 0 B | 申请审计BGM关系视图 |
| v_zhenxuan_apply_detail_summary | 206 | 0 B | 申请详情汇总视图 |
| v_zhenxuan_notice_history_summary | 65 | 0 B | 通知历史汇总视图 |
| v_zhenxuan_audit_track_bgm_summary | 12 | 0 B | 审计跟踪BGM汇总视图 |
| v_zhenxuan_project_detail_summary | 11 | 0 B | 项目详情汇总视图 |
| **小计** | **8,626** | **0 B** | **视图表** |

### 🏷️ 其他表 (7个表)

| 表名 | 行数 | 存储大小 | 说明 |
|------|------|----------|------|
| zhenxuan_queryselectprojectlist_copy1 | 1,438 | 176.00 KB | 甄选项目列表备份 |
| zhenxuan_querynoticehistorybyselectid | 65 | 256.00 KB | 甄选通知历史 |
| sync_status | 3 | 48.00 KB | 数据同步状态 |
| api_request_logs | 0 | 80.00 KB | API请求日志 (空表) |
| city_areas | 0 | 80.00 KB | 城市地区数据 (空表) |
| dict_data | 0 | 64.00 KB | 字典数据 (空表) |
| selection_projects | 0 | 176.00 KB | 甄选项目 (空表) |
| **小计** | **1,506** | **880 KB** | **1.5%存储占比** |

## 🔍 关键发现

### ✅ 数据完整性
- **有数据的表**: 23个表包含数据
- **空表**: 4个表为空 (api_request_logs, city_areas, dict_data, selection_projects)
- **数据集中度**: `zhenxuan_queryselectapplydetail` 表占用了92.6%的存储空间

### 📊 存储特点
- **大数据表**: `zhenxuan_queryselectapplydetail` 单表53.70MB，平均每行267.5KB
- **视图表**: 9个视图表不占用物理存储空间，性能良好
- **审计完整**: 审计跟踪表记录了8,988条历史记录

### 🎯 优化建议

#### 🚨 紧急关注
1. **大表优化**: `zhenxuan_queryselectapplydetail` 表需要重点关注
   - 206行数据占用53.70MB，可能存在大字段或BLOB数据
   - 建议检查是否有不必要的大文本或二进制数据
   - 考虑数据压缩或分离存储策略

#### 📈 性能优化
2. **索引检查**: 确保高频查询表有合适的索引
3. **数据归档**: 审计跟踪表可考虑定期归档历史数据
4. **空表激活**: 评估是否需要激活4个空表的数据同步

#### 🔧 维护建议
5. **定期统计**: 建议每周运行统计脚本监控数据增长
6. **备份策略**: 核心业务表需要重点备份保护
7. **清理策略**: 制定过期数据清理规则

---
*报告生成时间: 2025-07-09 19:10:54*  
*数据库版本: MySQL 8.0*  
*字符集: utf8mb4_unicode_ci*  
*工具: Python 3.10 + PyMySQL*
