"""
数据库配置文件
甄选数据库连接配置和工具类
"""

import pymysql
import logging
from contextlib import contextmanager
from typing import Dict, Any, Optional, List
import json
from datetime import datetime

# 甄选数据库连接配置
ZHENXUAN_DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'cmcc12345',
    'database': 'zhenxuandb',
    'charset': 'utf8mb4',
    'autocommit': True,
    'connect_timeout': 30,
    'read_timeout': 30,
    'write_timeout': 30
}

# 默认数据库连接信息 (原有配置保留)
DEFAULT_DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'cmcc12345',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化数据库管理器
        
        Args:
            config: 数据库配置，默认使用甄选数据库配置
        """
        self.config = config or ZHENXUAN_DB_CONFIG
        self.connection = None
        self.logger = logging.getLogger(__name__)
        
    def connect(self) -> bool:
        """
        连接数据库
        
        Returns:
            bool: 连接是否成功
        """
        try:
            self.connection = pymysql.connect(**self.config)
            self.logger.info(f"✅ 成功连接到数据库: {self.config['database']}")
            return True
        except Exception as e:
            self.logger.error(f"❌ 数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
            self.logger.info("🔌 数据库连接已断开")

    @contextmanager
    def get_connection(self):
        """
        获取数据库连接的上下文管理器

        Yields:
            connection: 数据库连接
        """
        if not self.connection:
            if not self.connect():
                raise Exception("无法连接到数据库")

        try:
            yield self.connection
        except Exception as e:
            self.connection.rollback()
            raise e
    
    @contextmanager
    def get_cursor(self):
        """
        获取数据库游标的上下文管理器
        
        Yields:
            cursor: 数据库游标
        """
        if not self.connection:
            if not self.connect():
                raise Exception("无法连接到数据库")
        
        cursor = self.connection.cursor(pymysql.cursors.DictCursor)
        try:
            yield cursor
        finally:
            cursor.close()
    
    def execute_query(self, sql: str, params: tuple = None) -> List[Dict]:
        """
        执行查询SQL
        
        Args:
            sql: SQL语句
            params: 参数
            
        Returns:
            List[Dict]: 查询结果
        """
        with self.get_cursor() as cursor:
            cursor.execute(sql, params)
            return cursor.fetchall()
    
    def execute_update(self, sql: str, params: tuple = None) -> int:
        """
        执行更新SQL
        
        Args:
            sql: SQL语句
            params: 参数
            
        Returns:
            int: 影响的行数
        """
        with self.get_cursor() as cursor:
            affected_rows = cursor.execute(sql, params)
            self.connection.commit()
            return affected_rows
    
    def execute_many(self, sql: str, params_list: List[tuple]) -> int:
        """
        批量执行SQL
        
        Args:
            sql: SQL语句
            params_list: 参数列表
            
        Returns:
            int: 影响的行数
        """
        with self.get_cursor() as cursor:
            affected_rows = cursor.executemany(sql, params_list)
            self.connection.commit()
            return affected_rows
    
    def insert_selection_project(self, project_data: Dict[str, Any]) -> bool:
        """
        插入甄选项目数据
        
        Args:
            project_data: 项目数据字典
            
        Returns:
            bool: 插入是否成功
        """
        sql = """
        INSERT INTO selection_projects (
            project_msg_id, work_order_msg_id, shut_order_msg_id, select_msg_id,
            select_apply_id, project_name, select_name, count, project_no,
            select_type, select_type_value, project_type, project_label,
            business_area, business_area_value, start_time, select_status,
            select_status_value, initiate_department, create_time, is_fixed_softness,
            create_staff, create_staff_value, next_todo_handler, next_todo_handler_value,
            is_operable, change_type1, change_type2, is_terminable, is_allow_second,
            select_category, select_category_value, dpcs_select_second_negotiate
        ) VALUES (
            %(project_msg_id)s, %(work_order_msg_id)s, %(shut_order_msg_id)s, %(select_msg_id)s,
            %(select_apply_id)s, %(project_name)s, %(select_name)s, %(count)s, %(project_no)s,
            %(select_type)s, %(select_type_value)s, %(project_type)s, %(project_label)s,
            %(business_area)s, %(business_area_value)s, %(start_time)s, %(select_status)s,
            %(select_status_value)s, %(initiate_department)s, %(create_time)s, %(is_fixed_softness)s,
            %(create_staff)s, %(create_staff_value)s, %(next_todo_handler)s, %(next_todo_handler_value)s,
            %(is_operable)s, %(change_type1)s, %(change_type2)s, %(is_terminable)s, %(is_allow_second)s,
            %(select_category)s, %(select_category_value)s, %(dpcs_select_second_negotiate)s
        ) ON DUPLICATE KEY UPDATE
            work_order_msg_id = VALUES(work_order_msg_id),
            shut_order_msg_id = VALUES(shut_order_msg_id),
            select_msg_id = VALUES(select_msg_id),
            select_apply_id = VALUES(select_apply_id),
            project_name = VALUES(project_name),
            select_name = VALUES(select_name),
            count = VALUES(count),
            select_status = VALUES(select_status),
            select_status_value = VALUES(select_status_value),
            updated_at = CURRENT_TIMESTAMP
        """
        
        try:
            with self.get_cursor() as cursor:
                cursor.execute(sql, project_data)
                self.connection.commit()
                self.logger.info(f"✅ 成功插入/更新项目: {project_data.get('project_name', 'Unknown')}")
                return True
        except Exception as e:
            self.logger.error(f"❌ 插入项目数据失败: {e}")
            return False
    
    def insert_dict_data(self, dict_data: Dict[str, Any]) -> bool:
        """
        插入字典数据
        
        Args:
            dict_data: 字典数据
            
        Returns:
            bool: 插入是否成功
        """
        sql = """
        INSERT INTO dict_data (dict_id, dict_name, group_id, descrp, other_info)
        VALUES (%(dict_id)s, %(dict_name)s, %(group_id)s, %(descrp)s, %(other_info)s)
        ON DUPLICATE KEY UPDATE
            dict_name = VALUES(dict_name),
            descrp = VALUES(descrp),
            other_info = VALUES(other_info),
            updated_at = CURRENT_TIMESTAMP
        """
        
        try:
            with self.get_cursor() as cursor:
                cursor.execute(sql, dict_data)
                self.connection.commit()
                return True
        except Exception as e:
            self.logger.error(f"❌ 插入字典数据失败: {e}")
            return False
    
    def log_api_request(self, url: str, method: str, params: str = None, 
                       status: int = None, response: str = None, 
                       duration: int = None, error: str = None) -> bool:
        """
        记录API请求日志
        
        Args:
            url: 请求URL
            method: 请求方法
            params: 请求参数
            status: 响应状态码
            response: 响应数据
            duration: 耗时(毫秒)
            error: 错误信息
            
        Returns:
            bool: 记录是否成功
        """
        sql = """
        INSERT INTO api_request_logs (
            request_url, request_method, request_params, response_status,
            response_data, request_time, response_time, duration_ms, error_message
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        """
        
        now = datetime.now()
        response_time = now if status else None
        
        try:
            with self.get_cursor() as cursor:
                cursor.execute(sql, (
                    url, method, params, status, response, 
                    now, response_time, duration, error
                ))
                self.connection.commit()
                return True
        except Exception as e:
            self.logger.error(f"❌ 记录API日志失败: {e}")
            return False

def get_db_manager(use_zhenxuan: bool = True) -> DatabaseManager:
    """
    获取数据库管理器实例
    
    Args:
        use_zhenxuan: 是否使用甄选数据库，默认True
        
    Returns:
        DatabaseManager: 数据库管理器实例
    """
    config = ZHENXUAN_DB_CONFIG if use_zhenxuan else DEFAULT_DB_CONFIG
    return DatabaseManager(config)

def test_connection(config: Dict[str, Any] = None) -> bool:
    """
    测试数据库连接
    
    Args:
        config: 数据库配置
        
    Returns:
        bool: 连接是否成功
    """
    db_manager = DatabaseManager(config)
    return db_manager.connect()
