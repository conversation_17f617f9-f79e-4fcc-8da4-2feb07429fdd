"""
数据库测试脚本
测试数据库连接、表结构和基本功能
"""

import os
import sys
import logging
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import DatabaseManager, ZHENXUAN_DB_CONFIG, test_connection

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseTester:
    """数据库测试器"""
    
    def __init__(self):
        self.db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
        self.test_results = []
    
    def add_test_result(self, test_name: str, success: bool, message: str = ""):
        """添加测试结果"""
        self.test_results.append({
            'test_name': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now()
        })
        
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} - {test_name}: {message}")
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            success = self.db_manager.connect()
            if success:
                self.add_test_result("数据库连接", True, "连接成功")
                return True
            else:
                self.add_test_result("数据库连接", False, "连接失败")
                return False
        except Exception as e:
            self.add_test_result("数据库连接", False, f"连接异常: {e}")
            return False
    
    def test_table_structure(self) -> bool:
        """测试表结构"""
        try:
            expected_tables = [
                'selection_projects',
                'dict_data',
                'city_areas', 
                'api_request_logs',
                'sync_status'
            ]
            
            # 获取所有表
            tables = self.db_manager.execute_query("SHOW TABLES")
            table_names = [list(table.values())[0] for table in tables]
            
            missing_tables = []
            for table in expected_tables:
                if table not in table_names:
                    missing_tables.append(table)
            
            if missing_tables:
                self.add_test_result("表结构检查", False, f"缺少表: {missing_tables}")
                return False
            else:
                self.add_test_result("表结构检查", True, f"所有表存在: {len(expected_tables)}个")
                return True
                
        except Exception as e:
            self.add_test_result("表结构检查", False, f"检查异常: {e}")
            return False
    
    def test_table_columns(self) -> bool:
        """测试表字段"""
        try:
            # 测试主表字段
            columns = self.db_manager.execute_query("DESCRIBE selection_projects")
            column_names = [col['Field'] for col in columns]
            
            required_columns = [
                'id', 'project_msg_id', 'project_name', 'select_name',
                'project_no', 'business_area', 'select_status', 'create_time'
            ]
            
            missing_columns = []
            for col in required_columns:
                if col not in column_names:
                    missing_columns.append(col)
            
            if missing_columns:
                self.add_test_result("表字段检查", False, f"selection_projects缺少字段: {missing_columns}")
                return False
            else:
                self.add_test_result("表字段检查", True, f"selection_projects字段完整: {len(column_names)}个")
                return True
                
        except Exception as e:
            self.add_test_result("表字段检查", False, f"检查异常: {e}")
            return False
    
    def test_indexes(self) -> bool:
        """测试索引"""
        try:
            indexes = self.db_manager.execute_query("SHOW INDEX FROM selection_projects")
            index_names = [idx['Key_name'] for idx in indexes]
            
            required_indexes = ['PRIMARY', 'uk_project_msg_id', 'idx_project_no']
            
            missing_indexes = []
            for idx in required_indexes:
                if idx not in index_names:
                    missing_indexes.append(idx)
            
            if missing_indexes:
                self.add_test_result("索引检查", False, f"缺少索引: {missing_indexes}")
                return False
            else:
                self.add_test_result("索引检查", True, f"关键索引存在: {len(required_indexes)}个")
                return True
                
        except Exception as e:
            self.add_test_result("索引检查", False, f"检查异常: {e}")
            return False
    
    def test_insert_data(self) -> bool:
        """测试数据插入"""
        try:
            # 测试数据
            test_project = {
                'project_msg_id': 'TEST_' + datetime.now().strftime('%Y%m%d%H%M%S'),
                'work_order_msg_id': 'TEST_WORK_ORDER',
                'shut_order_msg_id': None,
                'select_msg_id': 'TEST_SELECT',
                'select_apply_id': None,
                'project_name': '测试项目',
                'select_name': '测试甄选需求',
                'count': 1,
                'project_no': 'TEST_PROJECT_NO',
                'select_type': None,
                'select_type_value': None,
                'project_type': '10',
                'project_label': '54',
                'business_area': '760',
                'business_area_value': '测试地市',
                'start_time': datetime.now(),
                'select_status': '1001',
                'select_status_value': '测试状态',
                'initiate_department': None,
                'create_time': datetime.now(),
                'is_fixed_softness': 0,
                'create_staff': 'test_user',
                'create_staff_value': '测试用户',
                'next_todo_handler': 'test_user',
                'next_todo_handler_value': '测试用户',
                'is_operable': 0,
                'change_type1': None,
                'change_type2': None,
                'is_terminable': 0,
                'is_allow_second': None,
                'select_category': '1',
                'select_category_value': '项目甄选',
                'dpcs_select_second_negotiate': None
            }
            
            success = self.db_manager.insert_selection_project(test_project)
            if success:
                self.add_test_result("数据插入", True, "测试数据插入成功")
                return True
            else:
                self.add_test_result("数据插入", False, "测试数据插入失败")
                return False
                
        except Exception as e:
            self.add_test_result("数据插入", False, f"插入异常: {e}")
            return False
    
    def test_query_data(self) -> bool:
        """测试数据查询"""
        try:
            # 查询测试数据
            projects = self.db_manager.execute_query(
                "SELECT * FROM selection_projects WHERE project_name = %s",
                ('测试项目',)
            )
            
            if projects:
                self.add_test_result("数据查询", True, f"查询到 {len(projects)} 条记录")
                return True
            else:
                self.add_test_result("数据查询", False, "未查询到测试数据")
                return False
                
        except Exception as e:
            self.add_test_result("数据查询", False, f"查询异常: {e}")
            return False
    
    def test_update_data(self) -> bool:
        """测试数据更新"""
        try:
            # 更新测试数据
            affected_rows = self.db_manager.execute_update(
                "UPDATE selection_projects SET select_name = %s WHERE project_name = %s",
                ('更新后的甄选需求', '测试项目')
            )
            
            if affected_rows > 0:
                self.add_test_result("数据更新", True, f"更新了 {affected_rows} 条记录")
                return True
            else:
                self.add_test_result("数据更新", False, "没有记录被更新")
                return False
                
        except Exception as e:
            self.add_test_result("数据更新", False, f"更新异常: {e}")
            return False
    
    def test_api_log(self) -> bool:
        """测试API日志记录"""
        try:
            success = self.db_manager.log_api_request(
                url="http://test.example.com/api",
                method="GET",
                params='{"test": "data"}',
                status=200,
                response='{"result": "success"}',
                duration=100
            )
            
            if success:
                self.add_test_result("API日志记录", True, "日志记录成功")
                return True
            else:
                self.add_test_result("API日志记录", False, "日志记录失败")
                return False
                
        except Exception as e:
            self.add_test_result("API日志记录", False, f"记录异常: {e}")
            return False
    
    def cleanup_test_data(self) -> bool:
        """清理测试数据"""
        try:
            # 删除测试数据
            self.db_manager.execute_update(
                "DELETE FROM selection_projects WHERE project_name = %s",
                ('测试项目',)
            )
            
            self.db_manager.execute_update(
                "DELETE FROM api_request_logs WHERE request_url = %s",
                ('http://test.example.com/api',)
            )
            
            self.add_test_result("清理测试数据", True, "测试数据清理完成")
            return True
            
        except Exception as e:
            self.add_test_result("清理测试数据", False, f"清理异常: {e}")
            return False
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        logger.info("🧪 开始数据库测试...")
        
        # 测试列表
        tests = [
            self.test_connection,
            self.test_table_structure,
            self.test_table_columns,
            self.test_indexes,
            self.test_insert_data,
            self.test_query_data,
            self.test_update_data,
            self.test_api_log,
            self.cleanup_test_data
        ]
        
        # 执行测试
        for test in tests:
            try:
                test()
            except Exception as e:
                logger.error(f"测试执行异常: {e}")
        
        # 断开连接
        self.db_manager.disconnect()
        
        # 统计结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        summary = {
            'total': total_tests,
            'passed': passed_tests,
            'failed': failed_tests,
            'success_rate': round(passed_tests / total_tests * 100, 2) if total_tests > 0 else 0,
            'results': self.test_results
        }
        
        return summary
    
    def print_summary(self, summary: Dict[str, Any]):
        """打印测试摘要"""
        print("\n" + "=" * 60)
        print("🧪 数据库测试报告")
        print("=" * 60)
        print(f"📊 总测试数: {summary['total']}")
        print(f"✅ 通过数: {summary['passed']}")
        print(f"❌ 失败数: {summary['failed']}")
        print(f"📈 成功率: {summary['success_rate']}%")
        print()
        
        if summary['failed'] > 0:
            print("❌ 失败的测试:")
            for result in summary['results']:
                if not result['success']:
                    print(f"   - {result['test_name']}: {result['message']}")
            print()
        
        if summary['success_rate'] == 100:
            print("🎉 所有测试通过！数据库工作正常。")
        else:
            print("⚠️ 部分测试失败，请检查数据库配置。")
        
        print("=" * 60)

def main():
    """主函数"""
    tester = DatabaseTester()
    summary = tester.run_all_tests()
    tester.print_summary(summary)
    
    return summary['success_rate'] == 100

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
