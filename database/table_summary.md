# 甄选数据库表统计汇总

## 📊 总体概况

| 项目 | 数值 |
|------|------|
| 数据库名称 | zhenxuandb |
| 表总数 | 27 个 |
| 总数据行数 | 23,209 行 |
| 总存储大小 | 58.00 MB |
| 统计时间 | 2025-07-09 19:09:11 |

## 📂 按分类统计

### 🏷️ 核心业务表 (5个表) - 4,365行 - 56.11MB
> 甄选业务核心数据表，存储项目、合作伙伴、申请等主要业务信息

| 表名 | 行数 | 列数 | 说明 |
|------|------|------|------|
| zhenxuan_queryselectprojectlist | 1,533 | 46 | 甄选项目列表 |
| zhenxuan_queryselectstage | 1,438 | 24 | 甄选阶段信息 |
| zhenxuan_querypartnerselectdetail | 1,177 | 102 | 合作伙伴甄选详情 |
| zhenxuan_queryselectapplydetail | 206 | 54 | 甄选申请详情 |
| zhenxuan_queryselectprojectdetail | 11 | 93 | 甄选项目详情 |

### 🏷️ 审计跟踪表 (6个表) - 8,712行 - 1.03MB
> 审计流程跟踪表，记录各个环节的审批历史和状态变更

| 表名 | 行数 | 列数 | 说明 |
|------|------|------|------|
| zhenxuan_querylocalaudittrackhistory_ksm | 4,359 | 19 | 本地审计跟踪历史(KSM) |
| zhenxuan_queryselectaudittrackhistory | 3,704 | 33 | 甄选审计跟踪历史 |
| zhenxuan_querylocalaudittrackhistory_xqxx | 829 | 19 | 本地审计跟踪历史(需求信息) |
| zhenxuan_querylocalaudittrackhistory_ps | 24 | 20 | 本地审计跟踪历史(PS) |
| zhenxuan_querylocalaudittrackhistory | 15 | 21 | 本地审计跟踪历史 |
| zhenxuan_querylocalaudittrackhistory_bgm | 12 | 19 | 本地审计跟踪历史(BGM) |

### 🏷️ 业务视图 (9个表) - 8,626行 - 0B
> 业务数据汇总视图，用于数据分析和报表展示

| 表名 | 行数 | 列数 | 说明 |
|------|------|------|------|
| v_zhenxuan_audit_track_summary | 3,704 | 11 | 审计跟踪汇总视图 |
| v_zhenxuan_project_audit_relation | 1,535 | 13 | 项目审计关系视图 |
| v_zhenxuan_stage_audit_relation | 1,438 | 12 | 阶段审计关系视图 |
| v_zhenxuan_stage_summary | 1,438 | 7 | 阶段汇总视图 |
| v_zhenxuan_apply_audit_bgm_relation | 217 | 16 | 申请审计BGM关系视图 |
| v_zhenxuan_apply_detail_summary | 206 | 14 | 申请详情汇总视图 |
| v_zhenxuan_notice_history_summary | 65 | 12 | 通知历史汇总视图 |
| v_zhenxuan_audit_track_bgm_summary | 12 | 11 | 审计跟踪BGM汇总视图 |
| v_zhenxuan_project_detail_summary | 11 | 14 | 项目详情汇总视图 |

### 🏷️ 备份表 (1个表) - 1,438行 - 176KB
> 数据备份表

| 表名 | 行数 | 列数 | 说明 |
|------|------|------|------|
| zhenxuan_queryselectprojectlist_copy1 | 1,438 | 46 | 甄选项目列表备份 |

### 🏷️ 通知历史表 (1个表) - 65行 - 256KB
> 甄选通知发布历史记录表

| 表名 | 行数 | 列数 | 说明 |
|------|------|------|------|
| zhenxuan_querynoticehistorybyselectid | 65 | 25 | 甄选通知历史 |

### 🏷️ 系统管理表 (5个表) - 3行 - 448KB
> 系统管理和配置表，包括数据同步状态、字典数据等

| 表名 | 行数 | 列数 | 说明 |
|------|------|------|------|
| sync_status | 3 | 8 | 数据同步状态 |
| api_request_logs | 0 | 11 | API请求日志 (空表) |
| city_areas | 0 | 11 | 城市地区数据 (空表) |
| dict_data | 0 | 10 | 字典数据 (空表) |
| selection_projects | 0 | 37 | 甄选项目 (空表) |

## 📈 数据分布分析

### 按数据量排序 (Top 10)

| 排名 | 表名 | 行数 | 占比 |
|------|------|------|------|
| 1 | zhenxuan_querylocalaudittrackhistory_ksm | 4,359 | 18.8% |
| 2 | v_zhenxuan_audit_track_summary | 3,704 | 16.0% |
| 3 | zhenxuan_queryselectaudittrackhistory | 3,704 | 16.0% |
| 4 | v_zhenxuan_project_audit_relation | 1,535 | 6.6% |
| 5 | zhenxuan_queryselectprojectlist | 1,533 | 6.6% |
| 6 | v_zhenxuan_stage_audit_relation | 1,438 | 6.2% |
| 7 | v_zhenxuan_stage_summary | 1,438 | 6.2% |
| 8 | zhenxuan_queryselectprojectlist_copy1 | 1,438 | 6.2% |
| 9 | zhenxuan_queryselectstage | 1,438 | 6.2% |
| 10 | zhenxuan_querypartnerselectdetail | 1,177 | 5.1% |

### 存储空间分析

| 分类 | 表数量 | 数据行数 | 存储大小 | 占比 |
|------|--------|----------|----------|------|
| 核心业务表 | 5 | 4,365 | 56.11 MB | 96.7% |
| 审计跟踪表 | 6 | 8,712 | 1.03 MB | 1.8% |
| 系统管理表 | 5 | 3 | 448 KB | 0.8% |
| 通知历史表 | 1 | 65 | 256 KB | 0.4% |
| 备份表 | 1 | 1,438 | 176 KB | 0.3% |
| 业务视图 | 9 | 8,626 | 0 B | 0.0% |

## 🔍 关键发现

### ✅ 数据完整性
- **有数据的表**: 23个表包含数据
- **空表**: 4个表为空 (api_request_logs, city_areas, dict_data, selection_projects)
- **数据集中度**: 核心业务表占用了96.7%的存储空间

### 📊 业务特点
- **审计跟踪完善**: 多个审计跟踪表记录了详细的流程历史
- **视图丰富**: 9个业务视图提供了多维度的数据分析
- **数据备份**: 有专门的备份表保证数据安全

### 🎯 优化建议
1. **空表处理**: 考虑是否需要激活空表的数据同步
2. **视图优化**: 业务视图占用0存储空间，说明是计算视图，性能良好
3. **数据归档**: 审计跟踪表数据量较大，可考虑定期归档历史数据
4. **索引优化**: 核心业务表数据量大，需要确保索引配置合理

---
*报告生成时间: 2025-07-09 19:09:11*  
*数据库版本: MySQL 8.0*  
*字符集: utf8mb4_unicode_ci*
