-- 创建甄选申请详情数据表
-- 严格根据 querySelectApplyDetail 接口返回的JSON数据结构创建

USE zhenxuandb;

-- 删除表（如果存在）
DROP TABLE IF EXISTS `zhenxuan_querySelectApplyDetail`;

-- 创建甄选申请详情表
-- 严格按照JSON数据结构映射所有字段
CREATE TABLE `zhenxuan_querySelectApplyDetail` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',

  -- API请求参数（扩展字段）
  `select_apply_id` VARCHAR(50) NOT NULL COMMENT '甄选申请ID（入参）',
  `request_params` JSON DEFAULT NULL COMMENT '请求参数JSON',

  -- 响应基础信息（根级字段）
  `busi_date` DATETIME DEFAULT NULL COMMENT '业务日期 - busiDate',
  `code` VARCHAR(20) DEFAULT NULL COMMENT '响应代码 - code',
  `message` TEXT DEFAULT NULL COMMENT '响应消息 - message',

  -- resultBody核心信息
  `select_rev_id` VARCHAR(50) DEFAULT NULL COMMENT '甄选版本ID - selectRevId',
  `select_rev_name` VARCHAR(200) DEFAULT NULL COMMENT '甄选版本名称 - selectRevName',
  `project_name` VARCHAR(200) DEFAULT NULL COMMENT '项目名称 - projectName',
  `customer_name` VARCHAR(200) DEFAULT NULL COMMENT '客户名称 - customerName',
  `project_code` VARCHAR(50) DEFAULT NULL COMMENT '项目代码 - projectCode',
  `project_no` VARCHAR(50) DEFAULT NULL COMMENT '项目编号 - projectNo',

  -- 甄选信息
  `select_type` VARCHAR(20) DEFAULT NULL COMMENT '甄选类型 - selectType',
  `select_name` VARCHAR(200) DEFAULT NULL COMMENT '甄选名称 - selectName',
  `select_type_value` VARCHAR(50) DEFAULT NULL COMMENT '甄选类型值 - selectTypeValue',

  -- 业务区域和项目类型
  `business_area` VARCHAR(20) DEFAULT NULL COMMENT '业务区域代码 - businessArea',
  `business_area_value` VARCHAR(50) DEFAULT NULL COMMENT '业务区域名称 - businessAreaValue',
  `initiate_department` VARCHAR(100) DEFAULT NULL COMMENT '发起部门 - initiateDepartment',
  `project_type` VARCHAR(20) DEFAULT NULL COMMENT '项目类型 - projectType',
  `project_type_value` VARCHAR(50) DEFAULT NULL COMMENT '项目类型值 - projectTypeValue',

  -- 时间信息
  `create_time` DATETIME DEFAULT NULL COMMENT '创建时间 - createTime',
  `start_time` DATE DEFAULT NULL COMMENT '开始时间 - startTime',
  `end_time` DATE DEFAULT NULL COMMENT '结束时间 - endTime',

  -- 状态信息
  `apply_status_value` VARCHAR(50) DEFAULT NULL COMMENT '申请状态值 - applyStatusValue',
  `apply_review_status_value` VARCHAR(50) DEFAULT NULL COMMENT '申请审核状态值 - applyReviewStatusValue',

  -- 工单和评分信息
  `review_file_business_id` VARCHAR(50) DEFAULT NULL COMMENT '审核文件业务ID - reviewFileBusinessId',
  `work_order_msg_id` VARCHAR(50) DEFAULT NULL COMMENT '工单消息ID - workOrderMsgId',
  `score_order_msg_id` VARCHAR(50) DEFAULT NULL COMMENT '评分工单消息ID - scoreOrderMsgId',
  `score_rule_id` VARCHAR(50) DEFAULT NULL COMMENT '评分规则ID - scoreRuleId',
  `rule_item_ave_score` DECIMAL(5,2) DEFAULT NULL COMMENT '规则项平均分 - ruleItemAveScore',

  -- 验证和预算信息
  `is_need_verification` VARCHAR(10) DEFAULT '0' COMMENT '是否需要验证 - isNeedVerification',
  `is_finish_verification` VARCHAR(10) DEFAULT NULL COMMENT '是否完成验证 - isFinishVerification',
  `non_tax_select_budget` DECIMAL(15,2) DEFAULT NULL COMMENT '非税甄选预算 - nonTaxSelectBudget',

  -- 操作备注和推送信息
  `action_remark` TEXT DEFAULT NULL COMMENT '操作备注 - actionRemark',
  `push_notice` VARCHAR(100) DEFAULT NULL COMMENT '推送通知 - pushNotice',

  -- 技术审核和投标信息
  `is_technical_review` VARCHAR(10) DEFAULT NULL COMMENT '是否技术审核 - isTechnicalReview',
  `bid_flag_desc` VARCHAR(20) DEFAULT NULL COMMENT '投标标识描述 - bidFlagDesc',
  `bid_opening_time` DATETIME DEFAULT NULL COMMENT '开标时间 - bidOpeningTime',
  `rating` VARCHAR(10) DEFAULT NULL COMMENT '评级 - rating',
  `is_pre_review` VARCHAR(10) DEFAULT NULL COMMENT '是否预审 - isPreReview',

  -- 结果文档信息
  `select_result_doc` VARCHAR(200) DEFAULT NULL COMMENT '甄选结果文档 - selectResultDoc',
  `result_input_type` VARCHAR(50) DEFAULT NULL COMMENT '结果输入类型 - resultInputType',
  `result_title` VARCHAR(200) DEFAULT NULL COMMENT '结果标题 - resultTitle',
  `result_content` TEXT DEFAULT NULL COMMENT '结果内容 - resultContent',
  `doc_number_sub` VARCHAR(50) DEFAULT NULL COMMENT '文档编号子 - docNumberSub',
  `doc_number` VARCHAR(50) DEFAULT NULL COMMENT '文档编号 - docNumber',

  -- 会议信息
  `select_result_meet` VARCHAR(200) DEFAULT NULL COMMENT '甄选结果会议 - selectResultMeet',
  `select_result_meet_list` JSON DEFAULT NULL COMMENT '甄选结果会议列表 - selectResultMeetList',

  -- 关联信息
  `select_msg_id` VARCHAR(50) DEFAULT NULL COMMENT '甄选消息ID - selectMsgId',
  `dpcs_select_second_negotiate` VARCHAR(50) DEFAULT NULL COMMENT '二次谈判标识 - dpcsSelectSecondNegotiate',

  -- 项目需求详情（嵌套对象）
  `select_project_demand_detail_vo` JSON DEFAULT NULL COMMENT '甄选项目需求详情 - selectProjectDemandDetailVo',
  
  -- 原始数据
  `raw_data` JSON DEFAULT NULL COMMENT '原始JSON数据',
  
  -- 系统字段
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  
  -- 索引
  UNIQUE KEY `uk_select_apply_id` (`select_apply_id`),
  KEY `idx_select_rev_id` (`select_rev_id`),
  KEY `idx_project_code` (`project_code`),
  KEY `idx_project_no` (`project_no`),
  KEY `idx_business_area` (`business_area`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_work_order_msg_id` (`work_order_msg_id`),
  KEY `idx_score_order_msg_id` (`score_order_msg_id`),
  KEY `idx_rating` (`rating`),
  KEY `idx_composite_status_area` (`apply_status_value`, `business_area`),
  KEY `idx_composite_time_rating` (`create_time`, `rating`)
  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='甄选申请详情数据表';

-- 创建视图：简化查询
CREATE OR REPLACE VIEW `v_zhenxuan_apply_detail_summary` AS
SELECT
    id,
    select_apply_id,
    select_rev_id,
    project_name,
    customer_name,
    project_code,
    project_no,
    business_area_value,
    apply_status_value,
    apply_review_status_value,
    rating,
    create_time,
    start_time,
    end_time
FROM `zhenxuan_querySelectApplyDetail`
ORDER BY create_time DESC;

-- 验证表创建
SELECT
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_COLLATION
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = 'zhenxuandb'
AND TABLE_NAME = 'zhenxuan_querySelectApplyDetail';

-- 验证字段结构
DESCRIBE `zhenxuan_querySelectApplyDetail`;
