-- 创建甄选阶段查询数据表
-- 根据 querySelectStage 接口返回的JSON数据结构创建

USE zhenxuandb;

-- 删除表（如果存在）
DROP TABLE IF EXISTS `zhenxuan_querySelectStage`;

-- 创建甄选阶段查询表
-- 严格按照JSON数据结构映射所有字段
CREATE TABLE `zhenxuan_querySelectStage` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',

  -- API请求参数（扩展字段）
  `project_msg_id` VARCHAR(50) NOT NULL COMMENT '项目消息ID（入参）',
  `select_rev_id` VARCHAR(100) DEFAULT NULL COMMENT '甄选版本ID（入参）',
  `request_params` JSON DEFAULT NULL COMMENT '请求参数JSON',

  -- 响应基础信息（根级字段）
  `busi_date` DATETIME DEFAULT NULL COMMENT '业务日期 - busiDate',
  `code` VARCHAR(20) DEFAULT NULL COMMENT '响应代码 - code',
  `message` TEXT DEFAULT NULL COMMENT '响应消息 - message',

  -- resultBody中的甄选阶段信息
  `select_stage` VARCHAR(20) DEFAULT NULL COMMENT '甄选阶段代码 - selectStage',
  `select_stage_value` VARCHAR(50) DEFAULT NULL COMMENT '甄选阶段名称 - selectStageValue',
  `select_apply_id` VARCHAR(50) DEFAULT NULL COMMENT '甄选申请ID - selectApplyId',
  `select_apply_status` VARCHAR(20) DEFAULT NULL COMMENT '甄选申请状态 - selectApplyStatus',
  `select_msg_id` VARCHAR(50) DEFAULT NULL COMMENT '甄选消息ID - selectMsgId',
  `select_plan_status` VARCHAR(20) DEFAULT NULL COMMENT '甄选计划状态 - selectPlanStatus',
  `select_demand_status` VARCHAR(20) DEFAULT NULL COMMENT '甄选需求状态 - selectDemandStatus',
  `notice_id` VARCHAR(50) DEFAULT NULL COMMENT '通知ID - noticeId',
  `select_clarify_id` VARCHAR(50) DEFAULT NULL COMMENT '甄选澄清ID - selectClarifyId',
  `review_team_msg_id` VARCHAR(50) DEFAULT NULL COMMENT '评审团队消息ID - reviewTeamMsgId',
  `clarify_work_order_msg_id` VARCHAR(50) DEFAULT NULL COMMENT '澄清工单消息ID - clarifyWorkOrderMsgId',
  `second_negotiate_id` VARCHAR(50) DEFAULT NULL COMMENT '二次谈判ID - secondNegotiateId',
  `work_order_msg_id` VARCHAR(50) DEFAULT NULL COMMENT '工单消息ID - workOrderMsgId',
  `shut_order_msg_id` VARCHAR(50) DEFAULT NULL COMMENT '关闭工单消息ID - shutOrderMsgId',

  -- 原始数据
  `raw_data` JSON DEFAULT NULL COMMENT '原始JSON数据',

  -- 系统字段
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',

  -- 索引
  UNIQUE KEY `uk_project_msg_id_select_rev_id` (`project_msg_id`, `select_rev_id`),
  KEY `idx_project_msg_id` (`project_msg_id`),
  KEY `idx_select_stage` (`select_stage`),
  KEY `idx_select_apply_id` (`select_apply_id`),
  KEY `idx_select_msg_id` (`select_msg_id`),
  KEY `idx_work_order_msg_id` (`work_order_msg_id`),
  KEY `idx_created_at` (`created_at`)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='甄选阶段查询数据表';

-- 创建视图：简化查询
CREATE OR REPLACE VIEW `v_zhenxuan_stage_summary` AS
SELECT 
    id,
    project_msg_id,
    select_stage_value,
    select_apply_id,
    select_msg_id,
    work_order_msg_id,
    created_at
FROM `zhenxuan_querySelectStage`
ORDER BY created_at DESC;

-- 验证表创建
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_COLLATION
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'zhenxuandb' 
AND TABLE_NAME = 'zhenxuan_querySelectStage';

-- 验证字段结构
DESCRIBE `zhenxuan_querySelectStage`;
