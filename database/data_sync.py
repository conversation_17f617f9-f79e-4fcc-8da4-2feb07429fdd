"""
数据同步脚本
从API接口同步数据到甄选数据库
"""

import os
import sys
import json
import logging
import asyncio
import aiohttp
from datetime import datetime
from typing import Dict, Any, List, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import DatabaseManager, ZHENXUAN_DB_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class DataSynchronizer:
    """数据同步器"""
    
    def __init__(self):
        self.db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
        self.cookie_dir = "cookies"
        self.cookies = None
        self.base_url = "https://dict.gmcc.net:30722"
        
    def load_cookies(self) -> bool:
        """加载cookies"""
        try:
            latest_cookie_file = os.path.join(self.cookie_dir, "cookies_dict_zhenxuan.json")
            
            if os.path.exists(latest_cookie_file):
                with open(latest_cookie_file, 'r', encoding='utf-8') as f:
                    self.cookies = json.load(f)
                logger.info(f"✅ 成功加载 {len(self.cookies)} 个cookie")
                return True
            else:
                logger.error("❌ 未找到cookies文件")
                return False
                
        except Exception as e:
            logger.error(f"❌ 加载cookies失败: {e}")
            return False
    
    async def fetch_api_data(self, url: str, method: str = 'GET', 
                           data: Dict = None) -> Optional[Dict]:
        """
        获取API数据
        
        Args:
            url: API URL
            method: 请求方法
            data: 请求数据
            
        Returns:
            Dict: API响应数据
        """
        start_time = datetime.now()
        
        try:
            # 准备cookies
            cookie_str = '; '.join([f"{cookie['name']}={cookie['value']}" 
                                  for cookie in self.cookies])
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Cookie': cookie_str,
                'Content-Type': 'application/json;charset=UTF-8'
            }
            
            async with aiohttp.ClientSession() as session:
                if method.upper() == 'POST':
                    async with session.post(url, json=data, headers=headers, 
                                          ssl=False) as response:
                        response_data = await response.json()
                        status = response.status
                else:
                    async with session.get(url, headers=headers, ssl=False) as response:
                        response_data = await response.json()
                        status = response.status
                
                # 记录API请求日志
                duration = int((datetime.now() - start_time).total_seconds() * 1000)
                self.db_manager.log_api_request(
                    url=url,
                    method=method,
                    params=json.dumps(data) if data else None,
                    status=status,
                    response=json.dumps(response_data, ensure_ascii=False)[:5000],  # 限制长度
                    duration=duration
                )
                
                return response_data
                
        except Exception as e:
            logger.error(f"❌ API请求失败: {e}")
            # 记录错误日志
            self.db_manager.log_api_request(
                url=url,
                method=method,
                params=json.dumps(data) if data else None,
                error=str(e)
            )
            return None
    
    def transform_project_data(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """
        转换项目数据格式
        
        Args:
            record: API返回的原始记录
            
        Returns:
            Dict: 转换后的数据
        """
        # 处理时间字段
        def parse_datetime(date_str):
            if date_str:
                try:
                    return datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                except:
                    return None
            return None
        
        return {
            'project_msg_id': record.get('projectMsgId'),
            'work_order_msg_id': record.get('workOrderMsgId'),
            'shut_order_msg_id': record.get('shutOrderMsgId'),
            'select_msg_id': record.get('selectMsgId'),
            'select_apply_id': record.get('selectApplyId'),
            'project_name': record.get('projectName'),
            'select_name': record.get('selectName'),
            'count': int(record.get('count', 0)),
            'project_no': record.get('projectNo'),
            'select_type': record.get('selectType'),
            'select_type_value': record.get('selectTypeValue'),
            'project_type': record.get('projectType'),
            'project_label': record.get('projectLabel'),
            'business_area': record.get('businessArea'),
            'business_area_value': record.get('businessAreaValue'),
            'start_time': parse_datetime(record.get('startTime')),
            'select_status': record.get('selectStatus'),
            'select_status_value': record.get('selectStatusValue'),
            'initiate_department': record.get('initiateDepartment'),
            'create_time': parse_datetime(record.get('createTime')),
            'is_fixed_softness': int(record.get('isFixedSoftness', 0)),
            'create_staff': record.get('createStaff'),
            'create_staff_value': record.get('createStaffValue'),
            'next_todo_handler': record.get('nextTodoHandler'),
            'next_todo_handler_value': record.get('nextTodoHandlerValue'),
            'is_operable': int(record.get('isOperable', 0)),
            'change_type1': record.get('changeType1'),
            'change_type2': record.get('changeType2'),
            'is_terminable': int(record.get('isTerminable', 0)),
            'is_allow_second': record.get('isAllowSecond'),
            'select_category': record.get('selectCategory'),
            'select_category_value': record.get('selectCategoryValue'),
            'dpcs_select_second_negotiate': record.get('dpcsSelectSecondNegotiate')
        }
    
    async def sync_selection_projects(self, page_size: int = 50) -> int:
        """
        同步甄选项目数据
        
        Args:
            page_size: 每页数据量
            
        Returns:
            int: 同步的记录数
        """
        logger.info("🔄 开始同步甄选项目数据...")
        
        url = f"{self.base_url}/partner/materialManage/pnrSelectProject/querySelectProjectList"
        total_synced = 0
        current_page = 1
        
        while True:
            # 构造请求参数
            request_data = {
                "selectCategory": ["1", "3"],  # 项目甄选和算力项目甄选
                "projectName": "",
                "projectNo": "",
                "selectName": "",
                "businessArea": "",
                "selectStatus": "",
                "projectMsgId": "",
                "currentPage": current_page,
                "pageSize": page_size
            }
            
            # 获取数据
            response = await self.fetch_api_data(url, 'POST', request_data)
            if not response or response.get('code') != '000000':
                logger.error(f"❌ 获取第{current_page}页数据失败")
                break
            
            result_body = response.get('resultBody', {})
            records = result_body.get('records', [])
            total = result_body.get('total', 0)
            
            if not records:
                logger.info("✅ 没有更多数据")
                break
            
            # 转换并插入数据
            for record in records:
                try:
                    project_data = self.transform_project_data(record)
                    if self.db_manager.insert_selection_project(project_data):
                        total_synced += 1
                except Exception as e:
                    logger.error(f"❌ 处理记录失败: {e}")
            
            logger.info(f"✅ 第{current_page}页同步完成，本页{len(records)}条，累计{total_synced}条")
            
            # 检查是否还有下一页
            if current_page * page_size >= total:
                break
            
            current_page += 1
            
            # 添加延迟避免请求过快
            await asyncio.sleep(1)
        
        logger.info(f"🎉 甄选项目数据同步完成，共同步 {total_synced} 条记录")
        return total_synced
    
    async def sync_dict_data(self) -> int:
        """
        同步字典数据
        
        Returns:
            int: 同步的记录数
        """
        logger.info("🔄 开始同步字典数据...")
        
        # 字典分组ID列表
        group_ids = ['200025', '2000222']
        total_synced = 0
        
        for group_id in group_ids:
            url = f"{self.base_url}/partner/materialManage/sys/rCache/getDictListByGroupId2"
            params_url = f"{url}?groupId={group_id}"
            
            response = await self.fetch_api_data(params_url, 'GET')
            if not response:
                logger.error(f"❌ 获取字典数据失败，groupId: {group_id}")
                continue
            
            if isinstance(response, list):
                for item in response:
                    dict_data = {
                        'dict_id': item.get('dictId'),
                        'dict_name': item.get('dictName'),
                        'group_id': item.get('groupId'),
                        'descrp': item.get('descrp'),
                        'other_info': json.dumps(item.get('otherInfo')) if item.get('otherInfo') else None
                    }
                    
                    if self.db_manager.insert_dict_data(dict_data):
                        total_synced += 1
            
            logger.info(f"✅ 字典组 {group_id} 同步完成")
            await asyncio.sleep(0.5)
        
        logger.info(f"🎉 字典数据同步完成，共同步 {total_synced} 条记录")
        return total_synced
    
    def update_sync_status(self, sync_type: str, count: int, success: bool, error: str = None):
        """
        更新同步状态
        
        Args:
            sync_type: 同步类型
            count: 同步数量
            success: 是否成功
            error: 错误信息
        """
        sql = """
        UPDATE sync_status SET
            last_sync_time = %s,
            sync_count = %s,
            sync_status = %s,
            error_message = %s,
            updated_at = CURRENT_TIMESTAMP
        WHERE sync_type = %s
        """
        
        status = 1 if success else 0
        self.db_manager.execute_update(sql, (
            datetime.now(), count, status, error, sync_type
        ))
    
    async def full_sync(self) -> bool:
        """
        完整数据同步
        
        Returns:
            bool: 同步是否成功
        """
        logger.info("🚀 开始完整数据同步...")
        
        if not self.load_cookies():
            return False
        
        if not self.db_manager.connect():
            return False
        
        try:
            # 同步甄选项目数据
            project_count = await self.sync_selection_projects()
            self.update_sync_status('selection_projects', project_count, True)
            
            # 同步字典数据
            dict_count = await self.sync_dict_data()
            self.update_sync_status('dict_data', dict_count, True)
            
            logger.info("🎉 完整数据同步成功！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据同步失败: {e}")
            return False
        finally:
            self.db_manager.disconnect()

async def main():
    """主函数"""
    print("=" * 60)
    print("🔄 甄选数据同步工具")
    print("=" * 60)
    
    synchronizer = DataSynchronizer()
    success = await synchronizer.full_sync()
    
    if success:
        print("\n✅ 数据同步完成！")
    else:
        print("\n❌ 数据同步失败！")

if __name__ == "__main__":
    asyncio.run(main())
