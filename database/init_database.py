"""
数据库初始化脚本
创建甄选数据库并初始化基础数据
"""

import os
import sys
import logging
import pymysql
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import ZHENXUAN_DB_CONFIG, DatabaseManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('database_init.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

class DatabaseInitializer:
    """数据库初始化器"""
    
    def __init__(self):
        self.config = ZHENXUAN_DB_CONFIG.copy()
        # 初始连接时不指定数据库
        self.init_config = self.config.copy()
        self.init_config.pop('database', None)
        
    def create_database(self) -> bool:
        """创建数据库"""
        try:
            # 连接MySQL服务器（不指定数据库）
            connection = pymysql.connect(**self.init_config)
            cursor = connection.cursor()
            
            # 创建数据库
            database_name = self.config['database']
            cursor.execute(f"""
                CREATE DATABASE IF NOT EXISTS `{database_name}` 
                DEFAULT CHARACTER SET utf8mb4 
                DEFAULT COLLATE utf8mb4_unicode_ci
            """)
            
            logger.info(f"✅ 数据库 '{database_name}' 创建成功")
            
            cursor.close()
            connection.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建数据库失败: {e}")
            return False
    
    def execute_sql_file(self, sql_file_path: str) -> bool:
        """执行SQL文件"""
        try:
            # 读取SQL文件
            with open(sql_file_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()

            # 连接数据库
            connection = pymysql.connect(**self.config)
            cursor = connection.cursor()

            # 清理SQL内容，移除注释和空行
            lines = sql_content.split('\n')
            clean_lines = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith('--'):
                    clean_lines.append(line)

            clean_sql = ' '.join(clean_lines)

            # 分割SQL语句并执行
            sql_statements = [stmt.strip() for stmt in clean_sql.split(';') if stmt.strip()]

            for sql in sql_statements:
                if sql.upper().startswith(('CREATE', 'INSERT', 'UPDATE', 'DELETE', 'USE')):
                    try:
                        cursor.execute(sql)
                        logger.info(f"✅ 执行SQL: {sql[:50]}...")
                    except Exception as e:
                        logger.error(f"❌ 执行SQL失败: {sql[:50]}... 错误: {e}")
                        raise e

            connection.commit()
            cursor.close()
            connection.close()

            logger.info(f"✅ SQL文件 '{sql_file_path}' 执行成功")
            return True

        except Exception as e:
            logger.error(f"❌ 执行SQL文件失败: {e}")
            return False
    
    def insert_initial_data(self) -> bool:
        """插入初始数据"""
        try:
            db_manager = DatabaseManager(self.config)
            if not db_manager.connect():
                return False
            
            # 插入同步状态初始数据
            sync_data = [
                ('selection_projects', None, 0, 0, '初始化'),
                ('dict_data', None, 0, 0, '初始化'),
                ('city_areas', None, 0, 0, '初始化')
            ]
            
            sql = """
            INSERT INTO sync_status (sync_type, last_sync_time, sync_count, sync_status, error_message)
            VALUES (%s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
                sync_status = VALUES(sync_status),
                error_message = VALUES(error_message),
                updated_at = CURRENT_TIMESTAMP
            """
            
            with db_manager.get_cursor() as cursor:
                cursor.executemany(sql, sync_data)
                db_manager.connection.commit()
            
            logger.info("✅ 初始数据插入成功")
            db_manager.disconnect()
            return True
            
        except Exception as e:
            logger.error(f"❌ 插入初始数据失败: {e}")
            return False
    
    def create_indexes(self) -> bool:
        """创建额外索引"""
        try:
            db_manager = DatabaseManager(self.config)
            if not db_manager.connect():
                return False
            
            # 额外的索引SQL
            index_sqls = [
                "CREATE INDEX idx_selection_projects_composite ON selection_projects (business_area, select_status, create_time)",
                "CREATE INDEX idx_api_logs_time_status ON api_request_logs (request_time, response_status)",
                "CREATE INDEX idx_dict_data_active ON dict_data (group_id, is_active)"
            ]
            
            with db_manager.get_cursor() as cursor:
                for sql in index_sqls:
                    try:
                        cursor.execute(sql)
                        logger.info(f"✅ 创建索引: {sql[:50]}...")
                    except pymysql.Error as e:
                        if "Duplicate key name" in str(e):
                            logger.info(f"⚠️ 索引已存在，跳过: {sql[:50]}...")
                        else:
                            raise e
                
                db_manager.connection.commit()
            
            logger.info("✅ 索引创建完成")
            db_manager.disconnect()
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建索引失败: {e}")
            return False
    
    def verify_database(self) -> bool:
        """验证数据库创建是否成功"""
        try:
            db_manager = DatabaseManager(self.config)
            if not db_manager.connect():
                return False
            
            # 检查表是否存在
            tables_to_check = [
                'selection_projects',
                'dict_data', 
                'city_areas',
                'api_request_logs',
                'sync_status'
            ]
            
            with db_manager.get_cursor() as cursor:
                cursor.execute("SHOW TABLES")
                existing_tables = [row[f'Tables_in_{self.config["database"]}'] for row in cursor.fetchall()]
                
                for table in tables_to_check:
                    if table in existing_tables:
                        logger.info(f"✅ 表 '{table}' 存在")
                    else:
                        logger.error(f"❌ 表 '{table}' 不存在")
                        return False
                
                # 检查表结构
                for table in tables_to_check:
                    cursor.execute(f"DESCRIBE {table}")
                    columns = cursor.fetchall()
                    logger.info(f"📋 表 '{table}' 有 {len(columns)} 个字段")
            
            db_manager.disconnect()
            logger.info("✅ 数据库验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库验证失败: {e}")
            return False
    
    def initialize(self) -> bool:
        """完整初始化流程"""
        logger.info("🚀 开始初始化甄选数据库...")
        
        # 1. 创建数据库
        if not self.create_database():
            return False
        
        # 2. 执行建表SQL
        sql_file = os.path.join(os.path.dirname(__file__), 'create_zhenxuan_database.sql')
        if not self.execute_sql_file(sql_file):
            return False
        
        # 3. 插入初始数据
        if not self.insert_initial_data():
            return False
        
        # 4. 创建额外索引
        if not self.create_indexes():
            return False
        
        # 5. 验证数据库
        if not self.verify_database():
            return False
        
        logger.info("🎉 甄选数据库初始化完成！")
        return True

def main():
    """主函数"""
    print("=" * 60)
    print("🗄️  甄选数据库初始化工具")
    print("=" * 60)
    
    initializer = DatabaseInitializer()
    
    # 显示配置信息
    config = ZHENXUAN_DB_CONFIG
    print(f"📊 数据库配置:")
    print(f"   主机: {config['host']}:{config['port']}")
    print(f"   数据库: {config['database']}")
    print(f"   用户: {config['user']}")
    print(f"   字符集: {config['charset']}")
    print()
    
    # 确认执行
    confirm = input("是否继续初始化数据库? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("❌ 初始化已取消")
        return
    
    # 执行初始化
    success = initializer.initialize()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 数据库初始化成功！")
        print("=" * 60)
        print(f"✅ 数据库名称: {config['database']}")
        print("✅ 所有表已创建")
        print("✅ 索引已建立")
        print("✅ 初始数据已插入")
        print("\n📝 下一步:")
        print("   1. 运行数据同步脚本")
        print("   2. 测试API接口连接")
        print("   3. 开始数据采集")
    else:
        print("\n" + "=" * 60)
        print("❌ 数据库初始化失败！")
        print("=" * 60)
        print("请检查日志文件 'database_init.log' 获取详细错误信息")

if __name__ == "__main__":
    main()
