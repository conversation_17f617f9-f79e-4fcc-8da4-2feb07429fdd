-- 创建甄选邮件信息历史查询数据表
-- 根据 queryNoticeHistoryBySelectId 接口返回的JSON数据结构创建

USE zhenxuandb;

-- 删除表（如果存在）
DROP TABLE IF EXISTS `zhenxuan_queryNoticeHistoryBySelectId`;

-- 创建甄选邮件信息历史查询表
-- 严格按照JSON数据结构映射所有字段，增加源字段projectNo和入参字段
CREATE TABLE `zhenxuan_queryNoticeHistoryBySelectId` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',

  -- API请求参数（扩展字段）
  `select_msg_id` VARCHAR(50) NOT NULL COMMENT '甄选消息ID（入参）- 来源于zhenxuan_querySelectProjectList.selectMsgId',
  `project_code` VARCHAR(50) NOT NULL COMMENT '项目代码（入参）- 来源于zhenxuan_querySelectProjectList.projectNo',
  `project_no` VARCHAR(50) DEFAULT NULL COMMENT '源字段项目编号 - 来源于zhenxuan_querySelectProjectList.projectNo',
  `request_params` JSON DEFAULT NULL COMMENT '请求参数JSON',

  -- 响应基础信息（根级字段）
  `busi_date` DATETIME DEFAULT NULL COMMENT '业务日期 - busiDate',
  `code` VARCHAR(20) DEFAULT NULL COMMENT '响应代码 - code',
  `message` TEXT DEFAULT NULL COMMENT '响应消息 - message',

  -- resultBody数组中的邮件信息核心字段
  `select_notice_id` VARCHAR(50) DEFAULT NULL COMMENT '甄选通知ID - selectNoticeId',
  `notice_name` VARCHAR(200) DEFAULT NULL COMMENT '通知名称 - noticeName',
  `select_type` VARCHAR(20) DEFAULT NULL COMMENT '甄选类型 - selectType',
  `select_type_value` VARCHAR(50) DEFAULT NULL COMMENT '甄选类型值 - selectTypeValue',
  `select_time` DATETIME DEFAULT NULL COMMENT '甄选时间 - selectTime',
  `real_publish_time` DATETIME DEFAULT NULL COMMENT '实际发布时间 - realPublishTime',
  `select_industry` VARCHAR(100) DEFAULT NULL COMMENT '甄选行业 - selectIndustry',
  `notice_row_num` VARCHAR(10) DEFAULT NULL COMMENT '通知行号 - noticeRowNum',
  `notices_version` VARCHAR(100) DEFAULT NULL COMMENT '通知版本 - noticesVersion',
  `industry_value` VARCHAR(50) DEFAULT NULL COMMENT '行业值 - industryValue',
  `send_status` VARCHAR(10) DEFAULT NULL COMMENT '发送状态 - sendStatus',
  `send_status_value` VARCHAR(50) DEFAULT NULL COMMENT '发送状态值 - sendStatusValue',
  `email_partner_ids` TEXT DEFAULT NULL COMMENT '邮件合作伙伴IDs - emailPartnerIds',
  `is_clarify` BOOLEAN DEFAULT FALSE COMMENT '是否澄清 - isClarify',
  
  -- 原始数据
  `raw_data` JSON DEFAULT NULL COMMENT '原始JSON数据',
  
  -- 系统字段
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  
  -- 索引
  UNIQUE KEY `uk_select_notice_id` (`select_notice_id`),
  KEY `idx_select_msg_id` (`select_msg_id`),
  KEY `idx_project_code` (`project_code`),
  KEY `idx_project_no` (`project_no`),
  KEY `idx_select_type` (`select_type`),
  KEY `idx_real_publish_time` (`real_publish_time`),
  KEY `idx_send_status` (`send_status`),
  KEY `idx_select_industry` (`select_industry`),
  KEY `idx_composite_select_project` (`select_msg_id`, `project_code`),
  KEY `idx_composite_time_status` (`real_publish_time`, `send_status`)
  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='甄选邮件信息历史查询数据表';

-- 创建视图：简化查询
CREATE OR REPLACE VIEW `v_zhenxuan_notice_history_summary` AS
SELECT 
    id,
    select_msg_id,
    project_code,
    project_no,
    notice_name,
    select_type_value,
    real_publish_time,
    send_status_value,
    industry_value,
    notices_version,
    is_clarify,
    created_at
FROM `zhenxuan_queryNoticeHistoryBySelectId`
ORDER BY real_publish_time DESC;

-- 验证表创建
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_COLLATION
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'zhenxuandb' 
AND TABLE_NAME = 'zhenxuan_queryNoticeHistoryBySelectId';

-- 验证字段结构
DESCRIBE `zhenxuan_queryNoticeHistoryBySelectId`;
