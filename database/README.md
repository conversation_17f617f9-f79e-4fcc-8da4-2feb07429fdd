# 甄选数据库使用说明

## 📊 数据库概览

**数据库名称**: `zhenxuandb`  
**字符集**: `utf8mb4`  
**排序规则**: `utf8mb4_unicode_ci`

## 🗄️ 数据库配置

```python
ZHENXUAN_DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'cmcc12345',
    'database': 'zhenxuandb',
    'charset': 'utf8mb4'
}
```

## 📋 数据表结构

### 1. selection_projects (甄选项目主表)
存储从API接口获取的甄选项目数据

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | BIGINT | 自增主键 | PRIMARY |
| project_msg_id | VARCHAR(50) | 项目消息ID | UNIQUE |
| project_name | VARCHAR(200) | 项目名称 | INDEX |
| select_name | VARCHAR(200) | 甄选需求名称 | INDEX |
| project_no | VARCHAR(50) | 项目编码 | INDEX |
| business_area | VARCHAR(20) | 归属地市编码 | INDEX |
| select_status | VARCHAR(20) | 甄选需求状态编码 | INDEX |
| create_time | DATETIME | 创建时间 | INDEX |

### 2. dict_data (数据字典表)
存储系统字典数据

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | BIGINT | 自增主键 | PRIMARY |
| dict_id | VARCHAR(50) | 字典ID | UNIQUE(组合) |
| dict_name | VARCHAR(100) | 字典名称 | INDEX |
| group_id | VARCHAR(50) | 分组ID | INDEX |

### 3. city_areas (城市地区表)
存储城市地区信息

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | BIGINT | 自增主键 | PRIMARY |
| dict_id | VARCHAR(50) | 城市ID | UNIQUE |
| dict_name | VARCHAR(100) | 城市名称 | INDEX |
| level | TINYINT | 级别(1:省 2:市 3:区) | INDEX |

### 4. api_request_logs (API请求日志表)
记录所有API请求的详细日志

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 自增主键 |
| request_url | VARCHAR(500) | 请求URL |
| request_method | VARCHAR(10) | 请求方法 |
| response_status | INT | 响应状态码 |
| duration_ms | INT | 耗时(毫秒) |

### 5. sync_status (数据同步状态表)
跟踪数据同步状态

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 自增主键 |
| sync_type | VARCHAR(50) | 同步类型 |
| last_sync_time | DATETIME | 最后同步时间 |
| sync_count | INT | 同步数量 |
| sync_status | TINYINT | 同步状态(0:失败 1:成功 2:进行中) |

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install pymysql aiohttp
```

### 2. 初始化数据库
```bash
cd database
python init_database.py
```

### 3. 同步数据
```bash
python data_sync.py
```

### 4. 使用数据库管理器
```python
from database.db_config import get_db_manager

# 获取数据库管理器
db = get_db_manager()

# 连接数据库
if db.connect():
    # 查询数据
    projects = db.execute_query(
        "SELECT * FROM selection_projects LIMIT 10"
    )
    print(f"查询到 {len(projects)} 条项目记录")
    
    # 断开连接
    db.disconnect()
```

## 📊 常用查询示例

### 1. 查询项目统计
```sql
-- 按地市统计项目数量
SELECT 
    business_area_value as 地市,
    COUNT(*) as 项目数量
FROM selection_projects 
WHERE business_area_value IS NOT NULL
GROUP BY business_area_value
ORDER BY 项目数量 DESC;

-- 按状态统计项目
SELECT 
    select_status_value as 状态,
    COUNT(*) as 数量
FROM selection_projects 
GROUP BY select_status_value
ORDER BY 数量 DESC;
```

### 2. 查询最新项目
```sql
-- 查询最近创建的10个项目
SELECT 
    project_name as 项目名称,
    select_name as 甄选需求名称,
    business_area_value as 归属地市,
    select_status_value as 状态,
    create_time as 创建时间
FROM selection_projects 
ORDER BY create_time DESC 
LIMIT 10;
```

### 3. 查询API调用统计
```sql
-- API调用成功率统计
SELECT 
    DATE(request_time) as 日期,
    COUNT(*) as 总请求数,
    SUM(CASE WHEN response_status = 200 THEN 1 ELSE 0 END) as 成功请求数,
    ROUND(SUM(CASE WHEN response_status = 200 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as 成功率
FROM api_request_logs 
GROUP BY DATE(request_time)
ORDER BY 日期 DESC;
```

## 🔧 维护操作

### 1. 数据备份
```bash
mysqldump -u root -p zhenxuandb > zhenxuan_backup_$(date +%Y%m%d).sql
```

### 2. 数据恢复
```bash
mysql -u root -p zhenxuandb < zhenxuan_backup_20250708.sql
```

### 3. 清理日志数据
```sql
-- 清理30天前的API日志
DELETE FROM api_request_logs 
WHERE request_time < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

### 4. 重建索引
```sql
-- 重建主要索引
ALTER TABLE selection_projects DROP INDEX idx_project_name;
ALTER TABLE selection_projects ADD INDEX idx_project_name (project_name);
```

## 📈 性能优化建议

### 1. 索引优化
- 根据查询模式添加复合索引
- 定期分析慢查询日志
- 使用 `EXPLAIN` 分析查询计划

### 2. 数据分区
```sql
-- 按时间分区(适用于大数据量)
ALTER TABLE selection_projects 
PARTITION BY RANGE (YEAR(create_time)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 3. 定期维护
- 定期更新表统计信息
- 清理过期日志数据
- 监控数据库性能指标

## 🔒 安全建议

### 1. 用户权限
```sql
-- 创建只读用户
CREATE USER 'zhenxuan_read'@'localhost' IDENTIFIED BY 'read_password';
GRANT SELECT ON zhenxuandb.* TO 'zhenxuan_read'@'localhost';

-- 创建应用用户
CREATE USER 'zhenxuan_app'@'localhost' IDENTIFIED BY 'app_password';
GRANT SELECT, INSERT, UPDATE ON zhenxuandb.* TO 'zhenxuan_app'@'localhost';
```

### 2. 数据加密
- 敏感字段考虑加密存储
- 使用SSL连接
- 定期更新密码

## 📞 技术支持

如有问题，请查看：
1. 日志文件：`database_init.log`
2. 错误信息：检查 `api_request_logs` 表
3. 同步状态：查询 `sync_status` 表

## 📝 更新日志

### v1.0.0 (2025-07-08)
- ✅ 初始数据库设计
- ✅ 基础表结构创建
- ✅ 数据同步功能
- ✅ API日志记录
- ✅ 完整文档说明
