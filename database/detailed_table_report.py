#!/usr/bin/env python3
"""
详细数据库表统计报告
生成甄选数据库的详细分析报告，包含表分类、用途说明等
"""

import logging
import sys
import os
from datetime import datetime
from typing import Dict, List, Tuple
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import get_db_manager, ZHENXUAN_DB_CONFIG

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DetailedTableReporter:
    """详细表统计报告生成器"""
    
    def __init__(self):
        """初始化报告生成器"""
        self.db_manager = get_db_manager(use_zhenxuan=True)
        self.database_name = ZHENXUAN_DB_CONFIG['database']
        
        # 表分类定义
        self.table_categories = {
            '核心业务表': {
                'tables': [
                    'zhenxuan_queryselectprojectlist',
                    'zhenxuan_querypartnerselectdetail', 
                    'zhenxuan_queryselectapplydetail',
                    'zhenxuan_queryselectprojectdetail',
                    'zhenxuan_queryselectstage'
                ],
                'description': '甄选业务核心数据表，存储项目、合作伙伴、申请等主要业务信息'
            },
            '审计跟踪表': {
                'tables': [
                    'zhenxuan_queryselectaudittrackhistory',
                    'zhenxuan_querylocalaudittrackhistory',
                    'zhenxuan_querylocalaudittrackhistory_bgm',
                    'zhenxuan_querylocalaudittrackhistory_ksm',
                    'zhenxuan_querylocalaudittrackhistory_ps',
                    'zhenxuan_querylocalaudittrackhistory_xqxx'
                ],
                'description': '审计流程跟踪表，记录各个环节的审批历史和状态变更'
            },
            '通知历史表': {
                'tables': [
                    'zhenxuan_querynoticehistorybyselectid'
                ],
                'description': '甄选通知发布历史记录表'
            },
            '业务视图': {
                'tables': [
                    'v_zhenxuan_audit_track_summary',
                    'v_zhenxuan_project_audit_relation',
                    'v_zhenxuan_stage_audit_relation',
                    'v_zhenxuan_stage_summary',
                    'v_zhenxuan_apply_audit_bgm_relation',
                    'v_zhenxuan_apply_detail_summary',
                    'v_zhenxuan_notice_history_summary',
                    'v_zhenxuan_project_detail_summary',
                    'v_zhenxuan_audit_track_bgm_summary'
                ],
                'description': '业务数据汇总视图，用于数据分析和报表展示'
            },
            '系统管理表': {
                'tables': [
                    'selection_projects',
                    'dict_data',
                    'city_areas',
                    'api_request_logs',
                    'sync_status'
                ],
                'description': '系统管理和配置表，包括数据同步状态、字典数据等'
            },
            '备份表': {
                'tables': [
                    'zhenxuan_queryselectprojectlist_copy1'
                ],
                'description': '数据备份表'
            }
        }
    
    def get_table_stats(self, table_name: str) -> Dict[str, any]:
        """获取单个表的统计信息"""
        try:
            # 获取行数
            count_sql = f"SELECT COUNT(*) as count FROM `{table_name}`"
            count_result = self.db_manager.execute_query(count_sql)
            row_count = count_result[0]['count'] if count_result else 0
            
            # 获取表结构信息
            desc_sql = f"DESCRIBE `{table_name}`"
            desc_result = self.db_manager.execute_query(desc_sql)
            column_count = len(desc_result) if desc_result else 0
            
            # 获取表大小信息
            size_sql = """
            SELECT 
                table_rows,
                data_length,
                index_length,
                (data_length + index_length) as total_size,
                table_comment,
                create_time,
                update_time
            FROM information_schema.tables 
            WHERE table_schema = %s AND table_name = %s
            """
            size_result = self.db_manager.execute_query(size_sql, (self.database_name, table_name))
            
            size_info = {}
            if size_result and len(size_result) > 0:
                row = size_result[0]
                size_info = {
                    'estimated_rows': row.get('table_rows', 0) or 0,
                    'data_size': row.get('data_length', 0) or 0,
                    'index_size': row.get('index_length', 0) or 0,
                    'total_size': row.get('total_size', 0) or 0,
                    'comment': row.get('table_comment', '') or '',
                    'create_time': row.get('create_time'),
                    'update_time': row.get('update_time')
                }
            
            return {
                'table_name': table_name,
                'row_count': row_count,
                'column_count': column_count,
                **size_info
            }
            
        except Exception as e:
            logger.error(f"❌ 获取表 {table_name} 统计信息失败: {e}")
            return {
                'table_name': table_name,
                'row_count': 0,
                'column_count': 0,
                'estimated_rows': 0,
                'data_size': 0,
                'index_size': 0,
                'total_size': 0,
                'comment': '',
                'create_time': None,
                'update_time': None
            }
    
    def format_size(self, size_bytes: int) -> str:
        """格式化字节大小"""
        if size_bytes == 0:
            return "0 B"
        
        units = ['B', 'KB', 'MB', 'GB', 'TB']
        unit_index = 0
        size = float(size_bytes)
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        return f"{size:.2f} {units[unit_index]}"
    
    def get_all_tables(self) -> List[str]:
        """获取所有表名"""
        try:
            sql = f"SHOW TABLES FROM `{self.database_name}`"
            result = self.db_manager.execute_query(sql)
            
            if result:
                table_key = f'Tables_in_{self.database_name}'
                return [row[table_key] for row in result]
            return []
            
        except Exception as e:
            logger.error(f"❌ 获取表列表失败: {e}")
            return []
    
    def categorize_table(self, table_name: str) -> str:
        """根据表名确定表的分类"""
        for category, info in self.table_categories.items():
            if table_name in info['tables']:
                return category
        return '其他表'
    
    def generate_detailed_report(self) -> Dict[str, any]:
        """生成详细统计报告"""
        logger.info("🔍 开始生成详细统计报告...")
        
        if not self.db_manager.connect():
            logger.error("❌ 数据库连接失败")
            return {}
        
        try:
            tables = self.get_all_tables()
            if not tables:
                logger.warning("⚠️ 未找到任何表")
                return {}
            
            logger.info(f"📊 发现 {len(tables)} 个表，开始详细分析...")
            
            # 收集所有表的统计信息
            all_stats = []
            category_stats = {}
            
            for table_name in tables:
                logger.info(f"📋 正在分析表: {table_name}")
                
                stats = self.get_table_stats(table_name)
                category = self.categorize_table(table_name)
                stats['category'] = category
                
                all_stats.append(stats)
                
                # 按分类统计
                if category not in category_stats:
                    category_stats[category] = {
                        'table_count': 0,
                        'total_rows': 0,
                        'total_size': 0,
                        'tables': []
                    }
                
                category_stats[category]['table_count'] += 1
                category_stats[category]['total_rows'] += stats['row_count']
                category_stats[category]['total_size'] += stats.get('total_size', 0)
                category_stats[category]['tables'].append(stats)
            
            # 计算总计
            total_tables = len(all_stats)
            total_rows = sum(stats['row_count'] for stats in all_stats)
            total_size = sum(stats.get('total_size', 0) for stats in all_stats)
            
            report = {
                'database_name': self.database_name,
                'report_time': datetime.now().isoformat(),
                'summary': {
                    'total_tables': total_tables,
                    'total_rows': total_rows,
                    'total_size': total_size,
                    'total_size_formatted': self.format_size(total_size)
                },
                'category_stats': category_stats,
                'all_tables': all_stats
            }
            
            return report
            
        except Exception as e:
            logger.error(f"❌ 生成报告失败: {e}")
            return {}
        finally:
            self.db_manager.disconnect()
    
    def print_detailed_report(self, report: Dict[str, any]):
        """打印详细报告"""
        if not report:
            print("❌ 没有报告数据")
            return
        
        summary = report['summary']
        category_stats = report['category_stats']
        
        print("\n" + "="*100)
        print(f"📊 甄选数据库 ({report['database_name']}) 详细统计报告")
        print("="*100)
        print(f"📅 报告时间: {datetime.fromisoformat(report['report_time']).strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📋 表总数: {summary['total_tables']}")
        print(f"📊 总行数: {summary['total_rows']:,}")
        print(f"📦 总大小: {summary['total_size_formatted']}")
        print("="*100)
        
        # 按分类显示统计
        print("\n📂 按分类统计:")
        print("-" * 100)
        
        for category, stats in category_stats.items():
            description = self.table_categories.get(category, {}).get('description', '其他表类型')
            print(f"\n🏷️  {category} ({stats['table_count']} 个表)")
            print(f"   📝 说明: {description}")
            print(f"   📊 总行数: {stats['total_rows']:,}")
            print(f"   📦 总大小: {self.format_size(stats['total_size'])}")
            
            # 显示该分类下的表
            sorted_tables = sorted(stats['tables'], key=lambda x: x['row_count'], reverse=True)
            for table in sorted_tables:
                status = "📈" if table['row_count'] > 0 else "📭"
                print(f"   {status} {table['table_name']}: {table['row_count']:,} 行 "
                      f"({table['column_count']} 列)")

def main():
    """主函数"""
    print("🚀 开始生成甄选数据库详细统计报告...")
    
    reporter = DetailedTableReporter()
    report = reporter.generate_detailed_report()
    
    if report:
        reporter.print_detailed_report(report)
        
        # 保存报告到JSON文件
        report_file = f"database_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n✅ 详细报告生成完成！")
        print(f"📄 报告已保存到: {report_file}")
    else:
        print("❌ 报告生成失败，请检查数据库连接和配置")

if __name__ == "__main__":
    main()
