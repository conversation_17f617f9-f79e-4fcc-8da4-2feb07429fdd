-- 创建本地审核跟踪历史数据表
-- 根据 queryLocalAuditTrackHistory 接口返回的JSON数据结构创建

USE zhenxuandb;

-- 删除表（如果存在）
DROP TABLE IF EXISTS `zhenxuan_queryLocalAuditTrackHistory`;

-- 创建本地审核跟踪历史表
-- 严格按照JSON数据结构映射所有字段
CREATE TABLE `zhenxuan_queryLocalAuditTrackHistory` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',

  -- API请求参数（扩展字段）
  `project_msg_id` VARCHAR(50) NOT NULL COMMENT '项目消息ID - 来源于zhenxuan_querySelectProjectList.projectMsgId',
  `business_id` VARCHAR(50) NOT NULL COMMENT '业务ID（入参）- 使用projectMsgId的值',
  `work_order_msg_id` VARCHAR(100) DEFAULT NULL COMMENT '工单消息ID（入参）- 固定为null',
  `step_name_filter` VARCHAR(100) DEFAULT NULL COMMENT '步骤名称过滤（入参）- 固定为空字符串',
  `request_params` JSON DEFAULT NULL COMMENT '请求参数JSON',

  -- 响应基础信息（根级字段）
  `busi_date` DATETIME DEFAULT NULL COMMENT '业务日期 - busiDate',
  `code` VARCHAR(20) DEFAULT NULL COMMENT '响应代码 - code',
  `message` TEXT DEFAULT NULL COMMENT '响应消息 - message',

  -- resultBody中的审核跟踪历史信息
  `audit_process_track_id` VARCHAR(50) DEFAULT NULL COMMENT '审核流程跟踪ID - auditProcessTrackId',
  `step_name` VARCHAR(100) DEFAULT NULL COMMENT '步骤名称 - stepName',
  `create_time` DATETIME DEFAULT NULL COMMENT '创建时间 - createTime',
  `finish_time` DATETIME DEFAULT NULL COMMENT '完成时间 - finishTime',
  `status` VARCHAR(50) DEFAULT NULL COMMENT '状态 - status',
  `audit_handler` VARCHAR(100) DEFAULT NULL COMMENT '审核处理人 - auditHandler',
  `audit_remark` TEXT DEFAULT NULL COMMENT '审核备注 - auditRemark',

  -- 新增字段：来源于zhenxuan_querySelectApplyDetail表
  `score_rule_id` VARCHAR(50) DEFAULT NULL COMMENT '评分规则ID - 来源于zhenxuan_querySelectApplyDetail.scoreRuleId',
  `score_order_msg_id` VARCHAR(50) DEFAULT NULL COMMENT '评分工单消息ID - 来源于zhenxuan_querySelectApplyDetail.scoreOrderMsgId',

  -- 原始数据
  `raw_data` JSON DEFAULT NULL COMMENT '原始JSON数据',

  -- 系统字段
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',

  -- 索引设计
  INDEX `idx_project_msg_id` (`project_msg_id`) COMMENT '项目消息ID索引',
  INDEX `idx_business_id` (`business_id`) COMMENT '业务ID索引',
  INDEX `idx_work_order_msg_id` (`work_order_msg_id`) COMMENT '工单消息ID索引',
  INDEX `idx_audit_process_track_id` (`audit_process_track_id`) COMMENT '审核流程跟踪ID索引',
  INDEX `idx_step_name` (`step_name`) COMMENT '步骤名称索引',
  INDEX `idx_create_time` (`create_time`) COMMENT '创建时间索引',
  INDEX `idx_status` (`status`) COMMENT '状态索引',
  INDEX `idx_created_at` (`created_at`) COMMENT '记录创建时间索引',
  INDEX `idx_score_rule_id` (`score_rule_id`) COMMENT '评分规则ID索引',
  INDEX `idx_score_order_msg_id` (`score_order_msg_id`) COMMENT '评分工单消息ID索引',

  -- 复合索引
  INDEX `idx_project_business` (`project_msg_id`, `business_id`) COMMENT '项目ID和业务ID复合索引',
  INDEX `idx_business_step` (`business_id`, `step_name`) COMMENT '业务ID和步骤名称复合索引',

  -- 唯一约束：防止重复数据
  UNIQUE KEY `uk_audit_track_unique` (`project_msg_id`, `business_id`, `audit_process_track_id`) COMMENT '审核跟踪唯一约束'

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='本地审核跟踪历史数据表';

-- 创建视图：简化查询
CREATE OR REPLACE VIEW `v_zhenxuan_audit_track_summary` AS
SELECT
    id,
    project_msg_id,
    business_id,
    work_order_msg_id,
    step_name,
    status,
    audit_handler,
    create_time,
    finish_time,
    audit_remark,
    created_at
FROM `zhenxuan_queryLocalAuditTrackHistory`
ORDER BY project_msg_id, create_time;

-- 创建关联查询视图：与甄选项目列表表关联（通过project_msg_id）
CREATE OR REPLACE VIEW `v_zhenxuan_project_audit_relation` AS
SELECT
    p.id as project_id,
    p.project_msg_id,
    p.project_name,
    p.select_name,
    p.select_status_value,
    a.id as audit_id,
    a.business_id,
    a.step_name,
    a.status as audit_status,
    a.audit_handler,
    a.create_time as audit_create_time,
    a.finish_time as audit_finish_time,
    a.audit_remark
FROM `zhenxuan_querySelectProjectList` p
LEFT JOIN `zhenxuan_queryLocalAuditTrackHistory` a
    ON p.project_msg_id = a.project_msg_id
ORDER BY p.project_msg_id, a.create_time;

-- 验证表创建
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_COLLATION
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'zhenxuandb' 
AND TABLE_NAME = 'zhenxuan_queryLocalAuditTrackHistory';

-- 验证字段结构
DESCRIBE `zhenxuan_queryLocalAuditTrackHistory`;

-- 验证索引创建
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    INDEX_COMMENT
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'zhenxuandb' 
AND TABLE_NAME = 'zhenxuan_queryLocalAuditTrackHistory'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- 验证视图创建
SELECT 
    TABLE_NAME,
    TABLE_TYPE
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'zhenxuandb' 
AND TABLE_NAME LIKE '%audit%'
ORDER BY TABLE_NAME;
