#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建 t_zhenxuan_queryselectapplydetail_done 表并从视图导入数据
"""

import sys
import os
import time
from database.db_config import DatabaseManager, ZHENXUAN_DB_CONFIG

def create_and_import_table():
    """创建表并从视图导入数据"""
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    if not db_manager.connect():
        print("❌ 数据库连接失败")
        return False
    
    try:
        print("🚀 开始创建表并导入数据")
        print("="*80)
        
        with db_manager.get_cursor() as cursor:
            
            # 步骤1: 读取并执行建表SQL
            print("📋 步骤1: 创建表结构")
            with open('create_table_from_view.sql', 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # 分割SQL语句
            sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            for i, sql in enumerate(sql_statements, 1):
                if sql:
                    print(f"🔄 执行第 {i} 条SQL语句...")
                    cursor.execute(sql)
                    print(f"✅ 第 {i} 条SQL执行成功")
            
            # 提交建表操作
            db_manager.connection.commit()
            print("✅ 表结构创建完成")
            
            # 步骤2: 检查视图记录数
            print(f"\n📋 步骤2: 检查源视图数据")
            cursor.execute("SELECT COUNT(*) as count FROM v_zhenxuan_queryselectapplydetail_done")
            view_count = cursor.fetchone()['count']
            print(f"视图记录数: {view_count}")
            
            # 步骤3: 从视图导入数据到表
            print(f"\n📋 步骤3: 从视图导入数据到表")
            start_time = time.time()
            
            # 使用INSERT INTO ... SELECT 语句导入数据，处理DECIMAL字段转换
            insert_sql = """
            INSERT INTO t_zhenxuan_queryselectapplydetail_done (
                select_apply_id, request_params, select_rev_id, project_name, customer_name,
                project_code, project_no, select_type, select_name, select_type_value,
                project_type_value, create_time, start_time, end_time, apply_status_value,
                apply_review_status_value, review_file_business_id, work_order_msg_id,
                score_order_msg_id, score_rule_id, is_need_verification, is_finish_verification,
                non_tax_select_budget, action_remark, push_notice, is_technical_review,
                bid_flag_desc, bid_opening_time, rating, is_pre_review, select_result_doc,
                result_input_type, result_title, result_content, doc_number_sub, doc_number,
                select_result_meet, select_result_meet_list, select_msg_id, realEndTime,
                systemEndSelectTime, selectMsgId, business_area, business_area_value,
                project_type, select_budget, decide_opinion, end_select_time,
                selectApplyResultId, selectApplyId, partnerMsgId, partnerName,
                decideResultValue, bidMoneyValue, contactsName, contactsPhone,
                contactsEmail, partnerType, reviewScore, businessScore,
                technologyScore, selectResultNumber, raw_data
            )
            SELECT
                select_apply_id, request_params, select_rev_id, project_name, customer_name,
                project_code, project_no, select_type, select_name, select_type_value,
                project_type_value, create_time, start_time, end_time, apply_status_value,
                apply_review_status_value, review_file_business_id, work_order_msg_id,
                score_order_msg_id, score_rule_id, is_need_verification, is_finish_verification,
                non_tax_select_budget, action_remark, push_notice, is_technical_review,
                bid_flag_desc, bid_opening_time, rating, is_pre_review, select_result_doc,
                result_input_type, result_title, result_content, doc_number_sub, doc_number,
                select_result_meet, select_result_meet_list, select_msg_id, realEndTime,
                systemEndSelectTime, selectMsgId, business_area, business_area_value,
                project_type, select_budget, decide_opinion, end_select_time,
                selectApplyResultId, selectApplyId, partnerMsgId, partnerName,
                decideResultValue, bidMoneyValue, contactsName, contactsPhone,
                contactsEmail, partnerType, reviewScore, businessScore,
                technologyScore, selectResultNumber, raw_data
            FROM v_zhenxuan_queryselectapplydetail_done
            """
            
            print("🔄 开始导入数据...")
            cursor.execute(insert_sql)
            
            # 提交数据导入
            db_manager.connection.commit()
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✅ 数据导入完成，耗时: {duration:.2f} 秒")
            
            # 步骤4: 验证导入结果
            print(f"\n📋 步骤4: 验证导入结果")
            
            # 检查表记录数
            cursor.execute("SELECT COUNT(*) as count FROM t_zhenxuan_queryselectapplydetail_done")
            table_count = cursor.fetchone()['count']
            print(f"表记录数: {table_count}")
            
            # 比较记录数
            if table_count == view_count:
                print("✅ 记录数匹配，导入成功")
            else:
                print(f"⚠️ 记录数不匹配，视图:{view_count}, 表:{table_count}")
            
            # 查看表结构
            print(f"\n📋 表结构信息:")
            cursor.execute("DESCRIBE t_zhenxuan_queryselectapplydetail_done")
            columns = cursor.fetchall()
            print(f"字段总数: {len(columns)}")
            
            # 查看几条示例数据
            print(f"\n📋 示例数据（前3条）:")
            cursor.execute("""
            SELECT 
                id, select_apply_id, project_name, customer_name, 
                apply_status_value, rating, partnerName, decideResultValue,
                created_at, updated_at
            FROM t_zhenxuan_queryselectapplydetail_done 
            ORDER BY id 
            LIMIT 3
            """)
            
            sample_data = cursor.fetchall()
            for i, row in enumerate(sample_data, 1):
                print(f"\n记录 {i}:")
                print(f"  ID: {row['id']}")
                print(f"  申请ID: {row['select_apply_id']}")
                print(f"  项目名称: {row['project_name'][:50]}...")
                print(f"  客户名称: {row['customer_name'][:50]}...")
                print(f"  申请状态: {row['apply_status_value']}")
                print(f"  评级: {row['rating']}")
                print(f"  合作伙伴: {row['partnerName']}")
                print(f"  决策结果: {row['decideResultValue']}")
                print(f"  创建时间: {row['created_at']}")
                print(f"  更新时间: {row['updated_at']}")
            
            # 统计分析
            print(f"\n📊 数据统计分析:")
            
            # 按状态统计
            cursor.execute("""
            SELECT 
                apply_status_value,
                COUNT(*) as count
            FROM t_zhenxuan_queryselectapplydetail_done 
            GROUP BY apply_status_value
            ORDER BY count DESC
            """)
            status_stats = cursor.fetchall()
            print("按申请状态统计:")
            for stat in status_stats:
                print(f"  {stat['apply_status_value']}: {stat['count']} 条")
            
            # 按评级统计
            cursor.execute("""
            SELECT 
                rating,
                COUNT(*) as count
            FROM t_zhenxuan_queryselectapplydetail_done 
            WHERE rating IS NOT NULL
            GROUP BY rating
            ORDER BY rating
            """)
            rating_stats = cursor.fetchall()
            print("\n按评级统计:")
            for stat in rating_stats:
                print(f"  评级 {stat['rating']}: {stat['count']} 条")
            
            # 按决策结果统计
            cursor.execute("""
            SELECT 
                decideResultValue,
                COUNT(*) as count
            FROM t_zhenxuan_queryselectapplydetail_done 
            WHERE decideResultValue IS NOT NULL
            GROUP BY decideResultValue
            ORDER BY count DESC
            """)
            decide_stats = cursor.fetchall()
            print("\n按决策结果统计:")
            for stat in decide_stats:
                print(f"  {stat['decideResultValue']}: {stat['count']} 条")
        
        print(f"\n🎉 表创建和数据导入完成！")
        return True
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        return False
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    success = create_and_import_table()
    if success:
        print("\n✅ 所有操作成功完成！")
    else:
        print("\n❌ 操作失败！")
