#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试网络连接和Cookie有效性
"""

import sys
import os
import json
import requests
import urllib3
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_connection():
    """测试网络连接"""
    
    print("🔍 开始网络连接测试...")
    
    # 1. 测试基本网络连接
    try:
        print("\n1️⃣ 测试基本网络连接...")
        response = requests.get("https://www.baidu.com", timeout=10)
        print(f"✅ 基本网络连接正常 - 状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 基本网络连接失败: {e}")
        return
    
    # 2. 测试目标服务器连接
    try:
        print("\n2️⃣ 测试目标服务器连接...")
        response = requests.get("https://dict.gmcc.net:30722", verify=False, timeout=30)
        print(f"✅ 目标服务器连接正常 - 状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 目标服务器连接失败: {e}")
        print("   可能原因:")
        print("   - 服务器暂时不可用")
        print("   - 需要VPN连接")
        print("   - 防火墙阻止连接")
        print("   - Cookie已过期需要重新登录")
        return
    
    # 3. 测试Cookie有效性
    try:
        print("\n3️⃣ 测试Cookie有效性...")
        
        # 加载cookie
        cookie_file = "cookies/cookies_dict_zhenxuan.json"
        if not os.path.exists(cookie_file):
            print(f"❌ Cookie文件不存在: {cookie_file}")
            return
            
        with open(cookie_file, 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)
        
        # 转换cookie格式
        cookie_pairs = []
        for cookie in cookies_data:
            name = cookie.get('name', '')
            value = cookie.get('value', '')
            if name and value:
                cookie_pairs.append(f"{name}={value}")
        
        cookie_string = '; '.join(cookie_pairs)
        
        # 测试一个简单的API
        headers = {
            'Host': 'dict.gmcc.net:30722',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Cookie': cookie_string
        }
        
        # 尝试访问一个简单的页面或API
        test_url = "https://dict.gmcc.net:30722/partner/materialManage/pnrSelect/queryPartnerSelectDetail"
        response = requests.get(test_url, headers=headers, verify=False, timeout=30, 
                              params={'selectRevId': '1903979207947370496'})
        
        if response.status_code == 200:
            print(f"✅ Cookie有效 - 状态码: {response.status_code}")
            try:
                data = response.json()
                if data.get('code') == '000000':
                    print("✅ API响应正常")
                else:
                    print(f"⚠️ API返回错误: {data.get('message', '未知错误')}")
            except:
                print("⚠️ 响应不是有效的JSON格式")
        else:
            print(f"❌ Cookie可能已过期 - 状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Cookie测试失败: {e}")
    
    print(f"\n🕒 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n💡 建议:")
    print("   1. 如果目标服务器连接失败，请检查VPN连接")
    print("   2. 如果Cookie过期，请重新登录获取新的cookie")
    print("   3. 如果网络不稳定，可以增加超时时间")

if __name__ == '__main__':
    test_connection()
