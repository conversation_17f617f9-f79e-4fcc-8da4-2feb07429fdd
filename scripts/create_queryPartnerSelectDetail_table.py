#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建 zhenxuan_queryPartnerSelectDetail 数据表
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import get_db_manager
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_table():
    """创建数据表"""
    
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS zhenxuan_queryPartnerSelectDetail (
      id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
      select_rev_id VARCHAR(100) DEFAULT NULL COMMENT '甄选版本ID（入参）',
      request_params JSON DEFAULT NULL COMMENT '请求参数JSON',
      busi_date DATETIME DEFAULT NULL COMMENT '业务日期 - busiDate',
      code VARCHAR(20) DEFAULT NULL COMMENT '响应代码 - code',
      message TEXT DEFAULT NULL COMMENT '响应消息 - message',
      project_msg_id VARCHAR(50) NOT NULL COMMENT '项目消息ID - projectMsgId',
      project_name VARCHAR(200) NOT NULL COMMENT '项目名称 - projectName',
      project_code VARCHAR(50) DEFAULT NULL COMMENT '项目代码 - projectCode',
      project_no VARCHAR(50) DEFAULT NULL COMMENT '项目编号 - projectNo',
      iproject_id VARCHAR(50) DEFAULT NULL COMMENT 'I项目ID - iprojectId',
      group_project_code VARCHAR(50) DEFAULT NULL COMMENT '集团项目代码 - groupProjectCode',
      project_type VARCHAR(20) DEFAULT NULL COMMENT '项目类型 - projectType',
      project_type_value VARCHAR(50) DEFAULT NULL COMMENT '项目类型值 - projectTypeValue',
      project_label VARCHAR(20) DEFAULT NULL COMMENT '项目标签 - projectLabel',
      project_label_value VARCHAR(100) DEFAULT NULL COMMENT '项目标签值 - projectLabelValue',
      select_msg_id VARCHAR(50) DEFAULT NULL COMMENT '甄选消息ID - selectMsgId',
      select_name VARCHAR(200) DEFAULT NULL COMMENT '甄选名称 - selectName',
      select_status VARCHAR(20) DEFAULT NULL COMMENT '甄选状态 - selectStatus',
      select_status_value VARCHAR(50) DEFAULT NULL COMMENT '甄选状态值 - selectStatusValue',
      select_type VARCHAR(20) DEFAULT NULL COMMENT '甄选类型 - selectType',
      select_type_value VARCHAR(50) DEFAULT NULL COMMENT '甄选类型值 - selectTypeValue',
      select_category VARCHAR(20) DEFAULT NULL COMMENT '甄选分类 - selectCategory',
      select_category_value VARCHAR(50) DEFAULT NULL COMMENT '甄选分类值 - selectCategoryValue',
      select_notice_type VARCHAR(20) DEFAULT NULL COMMENT '甄选通知类型 - selectNoticeType',
      notice_id VARCHAR(50) DEFAULT NULL COMMENT '通知ID - noticeId',
      industry VARCHAR(20) DEFAULT NULL COMMENT '行业代码 - industry',
      industry_value VARCHAR(50) DEFAULT NULL COMMENT '行业值 - industryValue',
      select_industry VARCHAR(20) DEFAULT NULL COMMENT '甄选行业 - selectIndustry',
      select_industry_value VARCHAR(50) DEFAULT NULL COMMENT '甄选行业值 - selectIndustryValue',
      is_pmo_manage VARCHAR(10) DEFAULT '0' COMMENT '是否PMO管理 - isPmoManage',
      is_pmo_manage_value VARCHAR(20) DEFAULT NULL COMMENT 'PMO管理值 - isPmoManageValue',
      is_sub_sign VARCHAR(10) DEFAULT NULL COMMENT '是否子签约 - isSubSign',
      is_sub_sign_value VARCHAR(50) DEFAULT NULL COMMENT '子签约值 - isSubSignValue',
      is_investment_project VARCHAR(10) DEFAULT NULL COMMENT '是否投资项目 - isInvestmentProject',
      is_investment_project_value VARCHAR(50) DEFAULT NULL COMMENT '投资项目值 - isInvestmentProjectValue',
      is_fixed_softness VARCHAR(10) DEFAULT '0' COMMENT '是否固定软件 - isFixedSoftness',
      is_fixed_softness_value VARCHAR(20) DEFAULT NULL COMMENT '固定软件值 - isFixedSoftnessValue',
      engineering_project_code VARCHAR(50) DEFAULT NULL COMMENT '工程项目代码 - engineeringProjectCode',
      start_time DATE DEFAULT NULL COMMENT '开始时间 - startTime',
      end_time DATE DEFAULT NULL COMMENT '结束时间 - endTime',
      initiate_department VARCHAR(100) DEFAULT NULL COMMENT '发起部门 - initiateDepartment',
      create_staff VARCHAR(50) DEFAULT NULL COMMENT '创建人员 - createStaff',
      plan_create_staff VARCHAR(50) DEFAULT NULL COMMENT '计划创建人员 - planCreateStaff',
      next_todo_handler VARCHAR(50) DEFAULT NULL COMMENT '下一步处理人 - nextTodoHandler',
      next_todo_handler_value VARCHAR(50) DEFAULT NULL COMMENT '下一步处理人值 - nextTodoHandlerValue',
      customer_name VARCHAR(200) DEFAULT NULL COMMENT '客户名称 - customerName',
      first_scene VARCHAR(50) DEFAULT NULL COMMENT '一级场景 - firstScene',
      first_scene_value VARCHAR(100) DEFAULT NULL COMMENT '一级场景值 - firstSceneValue',
      second_scene VARCHAR(50) DEFAULT NULL COMMENT '二级场景 - secondScene',
      second_scene_value VARCHAR(100) DEFAULT NULL COMMENT '二级场景值 - secondSceneValue',
      select_demand_content TEXT DEFAULT NULL COMMENT '甄选需求内容 - selectDemandContent',
      select_budget DECIMAL(15,2) DEFAULT NULL COMMENT '甄选预算 - selectBudget',
      non_tax_select_budget DECIMAL(15,2) DEFAULT NULL COMMENT '非税甄选预算 - nonTaxSelectBudget',
      business_area VARCHAR(20) DEFAULT NULL COMMENT '业务区域代码 - businessArea',
      business_area_value VARCHAR(50) DEFAULT NULL COMMENT '业务区域值 - businessAreaValue',
      select_rev_id_detail VARCHAR(50) DEFAULT NULL COMMENT '甄选版本ID详情 - selectRevId',
      select_rev_name VARCHAR(200) DEFAULT NULL COMMENT '甄选版本名称 - selectRevName',
      select_rev_describe TEXT DEFAULT NULL COMMENT '甄选版本描述 - selectRevDescribe',
      operator_contact VARCHAR(50) DEFAULT NULL COMMENT '操作员联系人 - operatorContact',
      operator_contact_phone VARCHAR(20) DEFAULT NULL COMMENT '操作员联系电话 - operatorContactPhone',
      operator_contact_email VARCHAR(100) DEFAULT NULL COMMENT '操作员联系邮箱 - operatorContactEmail',
      file_id VARCHAR(50) DEFAULT NULL COMMENT '文件ID - fileId',
      file_id1 VARCHAR(50) DEFAULT NULL COMMENT '文件ID1 - fileId1',
      file_id2 VARCHAR(50) DEFAULT NULL COMMENT '文件ID2 - fileId2',
      file_id3 VARCHAR(50) DEFAULT NULL COMMENT '文件ID3 - fileId3',
      work_order_msg_id VARCHAR(50) DEFAULT NULL COMMENT '工单消息ID - workOrderMsgId',
      phone VARCHAR(20) DEFAULT NULL COMMENT '电话 - phone',
      employee_name VARCHAR(50) DEFAULT NULL COMMENT '员工姓名 - employeeName',
      email VARCHAR(100) DEFAULT NULL COMMENT '邮箱 - email',
      select_time DATETIME DEFAULT NULL COMMENT '甄选时间 - selectTime',
      select_address VARCHAR(200) DEFAULT NULL COMMENT '甄选地址 - selectAddress',
      termination_reason TEXT DEFAULT NULL COMMENT '终止原因 - terminationReason',
      other_explain TEXT DEFAULT NULL COMMENT '其他说明 - otherExplain',
      termination_explain TEXT DEFAULT NULL COMMENT '终止说明 - terminationExplain',
      shutdown_basis VARCHAR(50) DEFAULT NULL COMMENT '关闭依据 - shutdownBasis',
      next_audit_handler VARCHAR(50) DEFAULT NULL COMMENT '下一步审核处理人 - nextAuditHandler',
      current_audit_step VARCHAR(100) DEFAULT NULL COMMENT '当前审核步骤 - currentAuditStep',
      product_meeting_basis VARCHAR(50) DEFAULT NULL COMMENT '产品会议依据 - productMeetingBasis',
      product_meeting_basis2 VARCHAR(50) DEFAULT NULL COMMENT '产品会议依据2 - productMeetingBasis2',
      select_review_status VARCHAR(20) DEFAULT NULL COMMENT '甄选评审状态 - selectReviewStatus',
      pass_time DATETIME DEFAULT NULL COMMENT '通过时间 - passTime',
      select_demand_type VARCHAR(20) DEFAULT NULL COMMENT '甄选需求类型 - selectDemandType',
      select_demand_type_value VARCHAR(50) DEFAULT NULL COMMENT '甄选需求类型值 - selectDemandTypeValue',
      is_online_apply VARCHAR(10) DEFAULT '0' COMMENT '是否在线申请 - isOnlineApply',
      select_title VARCHAR(500) DEFAULT NULL COMMENT '甄选标题 - selectTitle',
      select_content LONGTEXT DEFAULT NULL COMMENT '甄选内容 - selectContent',
      audit_team_ids TEXT DEFAULT NULL COMMENT '审核团队ID列表 - auditTeamIds',
      audit_team_handler TEXT DEFAULT NULL COMMENT '审核团队处理人 - auditTeamHandler',
      document_no VARCHAR(50) DEFAULT NULL COMMENT '文档编号 - documentNo',
      doc_number_sub VARCHAR(100) DEFAULT NULL COMMENT '文档子编号 - docNumberSub',
      is_t_solution VARCHAR(10) DEFAULT NULL COMMENT '是否T解决方案 - isTSolution',
      select_level VARCHAR(50) DEFAULT NULL COMMENT '甄选级别 - selectLevel',
      push_notice VARCHAR(10) DEFAULT NULL COMMENT '推送通知 - pushNotice',
      create_year VARCHAR(10) DEFAULT NULL COMMENT '创建年份 - createYear',
      review_team_msg_id VARCHAR(50) DEFAULT NULL COMMENT '评审团队消息ID - reviewTeamMsgId',
      select_basis VARCHAR(50) DEFAULT NULL COMMENT '甄选依据 - selectBasis',
      select_basis2 VARCHAR(50) DEFAULT NULL COMMENT '甄选依据2 - selectBasis2',
      select_basis3 VARCHAR(50) DEFAULT NULL COMMENT '甄选依据3 - selectBasis3',
      raw_data JSON DEFAULT NULL COMMENT '原始JSON数据',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
      UNIQUE KEY uk_project_msg_id (project_msg_id),
      KEY idx_select_msg_id (select_msg_id),
      KEY idx_project_code (project_code),
      KEY idx_select_status (select_status),
      KEY idx_business_area (business_area),
      KEY idx_customer_name (customer_name),
      KEY idx_create_staff (create_staff),
      KEY idx_select_category (select_category),
      KEY idx_composite_status_area (select_status, business_area)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='甄选合作伙伴详情数据表'
    """

    try:
        db_manager = get_db_manager()
        db_manager.connect()
        db_manager.execute_update(create_table_sql)
        logger.info('✅ 数据库表创建成功!')
        
        # 验证表是否创建成功
        tables = db_manager.execute_query("SHOW TABLES LIKE 'zhenxuan_queryPartnerSelectDetail'")
        if tables:
            logger.info(f'✅ 表已存在: {list(tables[0].values())[0]}')
        else:
            logger.error('❌ 表创建失败')
            
    except Exception as e:
        logger.error(f'❌ 创建表失败: {e}')

if __name__ == '__main__':
    create_table()
