#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证 zhenxuan_queryPartnerSelectDetail 表数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import get_db_manager
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def verify_data():
    """验证数据"""
    try:
        db = get_db_manager()
        db.connect()

        # 查询保存的数据
        results = db.execute_query('''
        SELECT 
            id, project_name, select_msg_id, customer_name, 
            select_budget, business_area_value, created_at
        FROM zhenxuan_querypartnerselectdetail 
        ORDER BY created_at DESC 
        LIMIT 5
        ''')

        print('📊 已保存的合作伙伴详情数据:')
        for row in results:
            print(f'  ID: {row["id"]}')
            print(f'  项目名称: {row["project_name"]}')
            print(f'  甄选消息ID: {row["select_msg_id"]}')
            print(f'  客户名称: {row["customer_name"]}')
            print(f'  甄选预算: {row["select_budget"]}')
            print(f'  业务区域: {row["business_area_value"]}')
            print(f'  创建时间: {row["created_at"]}')
            print('  ---')
            
        # 统计信息
        count_result = db.execute_query('SELECT COUNT(*) as total FROM zhenxuan_querypartnerselectdetail')
        total_count = count_result[0]['total']
        print(f'\n📈 总记录数: {total_count}')
        
        # 检查selectMsgId字段
        select_msg_id_count = db.execute_query('''
        SELECT COUNT(*) as count 
        FROM zhenxuan_querypartnerselectdetail 
        WHERE select_msg_id IS NOT NULL AND select_msg_id != ""
        ''')
        print(f'📋 有效selectMsgId记录数: {select_msg_id_count[0]["count"]}')
        
    except Exception as e:
        logger.error(f'❌ 验证数据失败: {e}')

if __name__ == '__main__':
    verify_data()
