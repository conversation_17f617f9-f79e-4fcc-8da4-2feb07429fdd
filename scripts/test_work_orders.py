#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试工单数据
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import DatabaseManager, ZHENXUAN_DB_CONFIG

def test_work_orders():
    """测试工单数据"""
    db = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    if not db.connect():
        print("❌ 数据库连接失败")
        return
    
    try:
        with db.get_cursor() as cursor:
            # 检查工单数据总数
            cursor.execute('SELECT COUNT(*) as count FROM zhenxuan_querypartnerselectdetail WHERE work_order_msg_id IS NOT NULL AND work_order_msg_id != ""')
            result = cursor.fetchone()
            print(f"工单数据总数: {result['count']}")
            
            if result['count'] > 0:
                # 获取前5个工单ID
                cursor.execute('SELECT work_order_msg_id FROM zhenxuan_querypartnerselectdetail WHERE work_order_msg_id IS NOT NULL AND work_order_msg_id != "" LIMIT 5')
                results = cursor.fetchall()
                print("前5个工单ID:")
                for r in results:
                    print(f"  - {r['work_order_msg_id']}")
            else:
                print("❌ 没有找到工单数据")
                
                # 检查表中是否有任何数据
                cursor.execute('SELECT COUNT(*) as count FROM zhenxuan_querypartnerselectdetail')
                total_result = cursor.fetchone()
                print(f"表中总记录数: {total_result['count']}")
                
                if total_result['count'] > 0:
                    # 查看前几条记录的work_order_msg_id字段
                    cursor.execute('SELECT work_order_msg_id FROM zhenxuan_querypartnerselectdetail LIMIT 5')
                    sample_results = cursor.fetchall()
                    print("前5条记录的work_order_msg_id字段:")
                    for i, r in enumerate(sample_results, 1):
                        work_order_id = r['work_order_msg_id']
                        if work_order_id is None:
                            print(f"  {i}. NULL")
                        elif work_order_id == "":
                            print(f"  {i}. 空字符串")
                        else:
                            print(f"  {i}. {work_order_id}")
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    finally:
        db.disconnect()

if __name__ == "__main__":
    test_work_orders()
