#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
甄选项目详情数据获取程序
根据 querySelectProjectDetail 接口获取项目详情数据并入库

功能：
1. 从 zhenxuan_queryPartnerSelectDetail 表获取 projectMsgId
2. 调用 querySelectProjectDetail API 获取详情数据
3. 数据转换和入库处理
4. 支持Cookie认证和错误处理

作者: RIPER系统
创建时间: 2025-07-08
"""

import sys
import os
import json
import time
import logging
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional
from urllib.parse import urlencode

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import get_db_manager
from auth_loader import AuthLoader

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/fetch_querySelectProjectDetail.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class QuerySelectProjectDetailFetcher:
    """甄选项目详情数据获取器"""
    
    def __init__(self, cookie_file_path=None):
        """初始化获取器"""
        self.db_manager = get_db_manager(use_zhenxuan=True)
        self.base_url = "https://dict.gmcc.net:30722"
        self.api_endpoint = "/partner/materialManage/pnrSelectProject/querySelectProjectDetail"
        self.cookie_file = "cookies/cookies_dict_zhenxuan.json"
        self.session = requests.Session()
        # 使用 AuthLoader 加载认证信息
        auth_loader = AuthLoader(cookie_file_path)
        if auth_loader.load_auth_data():
            # 更新session的认证信息
            auth_loader.update_session(self.session)
            logger.info("✅ 认证信息加载成功")
        else:
            logger.error("❌ 认证信息加载失败")
            
        # 设置基本请求头（AuthLoader会自动添加认证相关的headers）
        self.session.headers.update({
            'Host': "dict.gmcc.net:30722",
            'Origin': "http://dict.gmcc.net:30722",
            'Referer': "http://dict.gmcc.net:30722/ptn/main/selectDemand",
        })
        self.session.verify = False  # 忽略SSL证书验证
        
        # 设置请求头
        self.session.headers.update({
            'Host': 'dict.gmcc.net:30722',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'sec-ch-ua-platform': '"Windows"',
            'Authorization': 'Bearer d25c514c-e026-4ddf-b455-9929dfcd3cfb',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://dict.gmcc.net:30722/ptn/main/selectDemand/detail',
            'Accept-Language': 'zh-CN,zh;q=0.9,ee;q=0.8'
        })
        
        # 加载Cookie
        self._load_cookies()
    
    def _load_cookies(self) -> bool:
        """
        加载Cookie文件
        
        Returns:
            bool: 加载是否成功
        """
        try:
            if not os.path.exists(self.cookie_file):
                logger.error(f"❌ Cookie文件不存在: {self.cookie_file}")
                return False
            
            with open(self.cookie_file, 'r', encoding='utf-8') as f:
                cookies_data = json.load(f)
            
            # 转换为Cookie字符串格式，包含所有同名cookie
            cookie_strings = []
            for cookie in cookies_data:
                if 'name' in cookie and 'value' in cookie:
                    cookie_strings.append(f"{cookie['name']}={cookie['value']}")
            
            cookie_header = '; '.join(cookie_strings)
            self.session.headers['Cookie'] = cookie_header
            
            logger.info(f"✅ 成功加载Cookie，共{len(cookies_data)}个cookie项")
            logger.debug(f"Cookie header: {cookie_header}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 加载Cookie失败: {e}")
            return False
    
    def get_project_msg_ids(self, get_all: bool = False) -> List[str]:
        """
        从数据库获取projectMsgId列表
        
        Args:
            get_all: 是否获取全部数据
            
        Returns:
            List[str]: projectMsgId列表
        """
        try:
            # 从zhenxuan_querySelectProjectList表获取projectMsgId
            if get_all:
                sql = "SELECT DISTINCT project_msg_id FROM zhenxuan_querySelectProjectList WHERE project_msg_id IS NOT NULL"
            else:
                sql = "SELECT DISTINCT project_msg_id FROM zhenxuan_querySelectProjectList WHERE project_msg_id IS NOT NULL LIMIT 10"
            
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql)
                    results = cursor.fetchall()
                    
                    if results:
                        project_msg_ids = [row[0] for row in results]
                        if get_all:
                            logger.info(f"✅ 从zhenxuan_querySelectProjectList表获取到{len(project_msg_ids)}个projectMsgId（全部数据）")
                        else:
                            logger.info(f"✅ 从zhenxuan_querySelectProjectList表获取到{len(project_msg_ids)}个projectMsgId")
                        return project_msg_ids
                    else:
                        logger.warning("⚠️ zhenxuan_querySelectProjectList表中没有找到projectMsgId")
                        return []
                            
        except Exception as e:
            logger.error(f"❌ 获取projectMsgId失败: {e}")
            return []
    
    def fetch_project_detail(self, project_msg_id: str) -> Optional[Dict[str, Any]]:
        """
        获取单个项目详情数据
        
        Args:
            project_msg_id: 项目消息ID
            
        Returns:
            Optional[Dict]: 项目详情数据
        """
        try:
            # 构建请求URL
            params = {'projectMsgId': project_msg_id}
            url = f"{self.base_url}{self.api_endpoint}?{urlencode(params)}"
            
            logger.info(f"🔄 正在获取项目详情: {project_msg_id}")
            logger.debug(f"请求URL: {url}")
            
            # 发送请求
            response = self.session.get(url, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                # 检查响应状态
                if data.get('code') == '000000':
                    logger.info(f"✅ 成功获取项目详情: {project_msg_id}")
                    return data
                else:
                    logger.error(f"❌ API返回错误: {data.get('message', 'Unknown error')}")
                    return None
            else:
                logger.error(f"❌ HTTP请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 获取项目详情失败 {project_msg_id}: {e}")
            return None
    
    def transform_data(self, raw_data: Dict[str, Any], project_msg_id: str) -> Dict[str, Any]:
        """
        转换API数据为数据库格式
        
        Args:
            raw_data: 原始API数据
            project_msg_id: 项目消息ID
            
        Returns:
            Dict: 转换后的数据
        """
        try:
            result_body = raw_data.get('resultBody', {})
            
            # 转换数据
            transformed_data = {
                # 请求参数
                'project_msg_id': project_msg_id,
                'request_params': json.dumps({'projectMsgId': project_msg_id}),
                
                # 响应基础信息
                'busi_date': self._parse_datetime(raw_data.get('busiDate')),
                'code': raw_data.get('code'),
                'message': raw_data.get('message'),
                
                # 项目核心信息
                'work_order_msg_id': result_body.get('workOrderMsgId'),
                'shut_order_msg_id': result_body.get('shutOrderMsgId'),
                'project_name': result_body.get('projectName'),
                'project_code': result_body.get('projectCode'),
                'group_project_code': result_body.get('groupProjectCode'),
                'project_type': result_body.get('projectType'),
                'project_type_value': result_body.get('projectTypeValue'),
                'industry': result_body.get('industry'),
                'industry_value': result_body.get('industryValue'),
                'is_pmo_manage': result_body.get('isPmoManage'),
                'is_pmo_manage_value': result_body.get('isPmoManageValue'),
                'is_sub_sign': result_body.get('isSubSign'),
                'is_sub_sign_value': result_body.get('isSubSignValue'),
                'is_investment_project': result_body.get('isInvestmentProject'),
                'is_investment_project_value': result_body.get('isInvestmentProjectValue'),
                'engineering_project_code': result_body.get('engineeringProjectCode'),
                'project_label': result_body.get('projectLabel'),
                'project_label_value': result_body.get('projectLabelValue'),
                
                # 甄选相关信息
                'select_name': result_body.get('selectName'),
                'select_status': result_body.get('selectStatus'),
                'select_status_value': result_body.get('selectStatusValue'),
                'select_type': result_body.get('selectType'),
                'select_type_value': result_body.get('selectTypeValue'),
                'start_time': self._parse_date(result_body.get('startTime')),
                'end_time': self._parse_date(result_body.get('endTime')),
                'select_demand_content': result_body.get('selectDemandContent'),
                'select_budget': self._parse_decimal(result_body.get('selectBudget')),
                'select_budget_value': result_body.get('selectBudgetValue'),
                'non_tax_select_budget': self._parse_decimal(result_body.get('nonTaxSelectBudget')),
                'non_tax_select_budget_value': result_body.get('nonTaxSelectBudgetValue'),
                'is_fixed_softness': result_body.get('isFixedSoftness'),
                'is_fixed_softness_value': result_body.get('isFixedSoftnessValue'),
                
                # 甄选依据相关
                'select_basis': result_body.get('selectBasis'),
                'select_basis2': result_body.get('selectBasis2'),
                'select_basis3': result_body.get('selectBasis3'),
                'select_basis4': result_body.get('selectBasis4'),
                'select_notice_type': result_body.get('selectNoticeType'),
                'select_notice_type_value': result_body.get('selectNoticeTypeValue'),
                'city_decide_file': result_body.get('cityDecideFile'),
                
                # 业务区域和创建信息
                'business_area': result_body.get('businessArea'),
                'business_area_value': result_body.get('businessAreaValue'),
                'create_time': self._parse_datetime(result_body.get('createTime')),
                'create_staff': result_body.get('createStaff'),
                'initiate_department': result_body.get('initiateDepartment'),
                
                # 客户和场景信息
                'customer_name': result_body.get('customerName'),
                'first_scene': result_body.get('firstScene'),
                'first_scene_value': result_body.get('firstSceneValue'),
                'second_scene': result_body.get('secondScene'),
                'second_scene_value': result_body.get('secondSceneValue'),
                'project_no': result_body.get('projectNo'),
                'iproject_id': result_body.get('iprojectId'),
                
                # 终止相关信息
                'termination_reason': result_body.get('terminationReason'),
                'termination_reason_value': result_body.get('terminationReasonValue'),
                'other_explain': result_body.get('otherExplain'),
                'termination_explain': result_body.get('terminationExplain'),
                
                # 联系信息
                'phone': result_body.get('phone'),
                'employee_name': result_body.get('employeeName'),
                'email': result_body.get('email'),
                
                # 处理人信息
                'next_todo_handler': result_body.get('nextTodoHandler'),
                'next_todo_handler_value': result_body.get('nextTodoHandlerValue'),
                'next_audit_handler': result_body.get('nextAuditHandler'),
                'next_shutdown_handler': result_body.get('nextShutdownHandler'),
                
                # 关闭相关
                'shutdown_basis': result_body.get('shutdownBasis'),
                'current_audit_step': result_body.get('currentAuditStep'),
                'current_audit_step1': result_body.get('currentAuditStep1'),
                
                # 甄选分类和需求类型
                'select_category': result_body.get('selectCategory'),
                'select_category_value': result_body.get('selectCategoryValue'),
                'select_demand_type': result_body.get('selectDemandType'),
                'select_demand_type_value': result_body.get('selectDemandTypeValue'),
                
                # 解决方案相关
                'is_t_solution': result_body.get('isTSolution'),
                'is_non_presale': result_body.get('isNonPresale'),
                'non_presale_reason': result_body.get('nonPresaleReason'),
                'non_presale_file': result_body.get('nonPresaleFile'),
                
                # 项目进度相关
                'project_stage': result_body.get('projectStage'),
                'project_progress': result_body.get('projectProgress'),
                'project_scope': result_body.get('projectScope'),
                'doc_number_sub': result_body.get('docNumberSub'),
                
                # JSON字段
                'select_basis_vo': json.dumps(result_body.get('selectBasisVo')) if result_body.get('selectBasisVo') else None,
                'select_basis2_vo': json.dumps(result_body.get('selectBasis2Vo')) if result_body.get('selectBasis2Vo') else None,
                'select_basis3_vo': json.dumps(result_body.get('selectBasis3Vo')) if result_body.get('selectBasis3Vo') else None,
                'select_basis4_vo': json.dumps(result_body.get('selectBasis4Vo')) if result_body.get('selectBasis4Vo') else None,
                'city_decide_file_vo': json.dumps(result_body.get('cityDecideFileVo')) if result_body.get('cityDecideFileVo') else None,
                'shutdown_basis_vo': json.dumps(result_body.get('shutdownBasisVo')) if result_body.get('shutdownBasisVo') else None,
                'non_presale_file_list': json.dumps(result_body.get('nonPresaleFileList')) if result_body.get('nonPresaleFileList') else None,
                'raw_data': json.dumps(raw_data)
            }
            
            return transformed_data
            
        except Exception as e:
            logger.error(f"❌ 数据转换失败: {e}")
            return {}
    
    def _parse_datetime(self, date_str: str) -> Optional[str]:
        """解析日期时间字符串"""
        if not date_str:
            return None
        try:
            # 处理格式：2025-01-10 17:24:13
            return date_str
        except:
            return None
    
    def _parse_date(self, date_str: str) -> Optional[str]:
        """解析日期字符串"""
        if not date_str:
            return None
        try:
            # 处理格式：2025-01-10
            return date_str
        except:
            return None
    
    def _parse_decimal(self, value: str) -> Optional[float]:
        """解析数字字符串"""
        if not value:
            return None
        try:
            return float(value)
        except:
            return None

    def save_project_detail(self, project_data: Dict[str, Any]) -> bool:
        """
        保存项目详情数据到数据库

        Args:
            project_data: 项目详情数据

        Returns:
            bool: 保存是否成功
        """
        try:
            sql = """
            INSERT INTO zhenxuan_querySelectProjectDetail (
                project_msg_id, request_params, busi_date, code, message,
                work_order_msg_id, shut_order_msg_id, project_name, project_code, group_project_code,
                project_type, project_type_value, industry, industry_value, is_pmo_manage,
                is_pmo_manage_value, is_sub_sign, is_sub_sign_value, is_investment_project, is_investment_project_value,
                engineering_project_code, project_label, project_label_value, select_name, select_status,
                select_status_value, select_type, select_type_value, start_time, end_time,
                select_demand_content, select_budget, select_budget_value, non_tax_select_budget, non_tax_select_budget_value,
                is_fixed_softness, is_fixed_softness_value, select_basis, select_basis2, select_basis3,
                select_basis4, select_notice_type, select_notice_type_value, city_decide_file, business_area,
                business_area_value, create_time, create_staff, initiate_department, customer_name,
                first_scene, first_scene_value, second_scene, second_scene_value, project_no,
                iproject_id, termination_reason, termination_reason_value, other_explain, termination_explain,
                phone, employee_name, email, next_todo_handler, next_todo_handler_value,
                next_audit_handler, next_shutdown_handler, shutdown_basis, current_audit_step, current_audit_step1,
                select_category, select_category_value, select_demand_type, select_demand_type_value, is_t_solution,
                is_non_presale, non_presale_reason, non_presale_file, project_stage, project_progress,
                project_scope, doc_number_sub, select_basis_vo, select_basis2_vo, select_basis3_vo,
                select_basis4_vo, city_decide_file_vo, shutdown_basis_vo, non_presale_file_list, raw_data
            ) VALUES (
                %(project_msg_id)s, %(request_params)s, %(busi_date)s, %(code)s, %(message)s,
                %(work_order_msg_id)s, %(shut_order_msg_id)s, %(project_name)s, %(project_code)s, %(group_project_code)s,
                %(project_type)s, %(project_type_value)s, %(industry)s, %(industry_value)s, %(is_pmo_manage)s,
                %(is_pmo_manage_value)s, %(is_sub_sign)s, %(is_sub_sign_value)s, %(is_investment_project)s, %(is_investment_project_value)s,
                %(engineering_project_code)s, %(project_label)s, %(project_label_value)s, %(select_name)s, %(select_status)s,
                %(select_status_value)s, %(select_type)s, %(select_type_value)s, %(start_time)s, %(end_time)s,
                %(select_demand_content)s, %(select_budget)s, %(select_budget_value)s, %(non_tax_select_budget)s, %(non_tax_select_budget_value)s,
                %(is_fixed_softness)s, %(is_fixed_softness_value)s, %(select_basis)s, %(select_basis2)s, %(select_basis3)s,
                %(select_basis4)s, %(select_notice_type)s, %(select_notice_type_value)s, %(city_decide_file)s, %(business_area)s,
                %(business_area_value)s, %(create_time)s, %(create_staff)s, %(initiate_department)s, %(customer_name)s,
                %(first_scene)s, %(first_scene_value)s, %(second_scene)s, %(second_scene_value)s, %(project_no)s,
                %(iproject_id)s, %(termination_reason)s, %(termination_reason_value)s, %(other_explain)s, %(termination_explain)s,
                %(phone)s, %(employee_name)s, %(email)s, %(next_todo_handler)s, %(next_todo_handler_value)s,
                %(next_audit_handler)s, %(next_shutdown_handler)s, %(shutdown_basis)s, %(current_audit_step)s, %(current_audit_step1)s,
                %(select_category)s, %(select_category_value)s, %(select_demand_type)s, %(select_demand_type_value)s, %(is_t_solution)s,
                %(is_non_presale)s, %(non_presale_reason)s, %(non_presale_file)s, %(project_stage)s, %(project_progress)s,
                %(project_scope)s, %(doc_number_sub)s, %(select_basis_vo)s, %(select_basis2_vo)s, %(select_basis3_vo)s,
                %(select_basis4_vo)s, %(city_decide_file_vo)s, %(shutdown_basis_vo)s, %(non_presale_file_list)s, %(raw_data)s
            ) ON DUPLICATE KEY UPDATE
                work_order_msg_id = VALUES(work_order_msg_id),
                shut_order_msg_id = VALUES(shut_order_msg_id),
                project_name = VALUES(project_name),
                select_status = VALUES(select_status),
                select_status_value = VALUES(select_status_value),
                raw_data = VALUES(raw_data),
                updated_at = CURRENT_TIMESTAMP
            """

            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, project_data)
                    conn.commit()

            logger.info(f"✅ 成功保存项目详情: {project_data.get('project_name', 'Unknown')}")
            return True

        except Exception as e:
            logger.error(f"❌ 保存项目详情失败: {e}")
            return False

    def fetch_and_save_all(self, get_all: bool = False) -> Dict[str, int]:
        """
        获取并保存所有项目详情数据

        Args:
            get_all: 是否获取全部数据

        Returns:
            Dict: 统计信息
        """
        stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0
        }

        try:
            # 获取projectMsgId列表
            project_msg_ids = self.get_project_msg_ids(get_all=get_all)

            if not project_msg_ids:
                logger.error("❌ 没有找到可处理的projectMsgId")
                return stats

            stats['total'] = len(project_msg_ids)
            logger.info(f"🚀 开始处理{stats['total']}个项目详情")

            for i, project_msg_id in enumerate(project_msg_ids, 1):
                try:
                    logger.info(f"📊 处理进度: {i}/{stats['total']} - {project_msg_id}")

                    # 获取项目详情数据
                    raw_data = self.fetch_project_detail(project_msg_id)

                    if raw_data:
                        # 转换数据格式
                        transformed_data = self.transform_data(raw_data, project_msg_id)

                        if transformed_data:
                            # 保存到数据库
                            if self.save_project_detail(transformed_data):
                                stats['success'] += 1
                            else:
                                stats['failed'] += 1
                        else:
                            logger.warning(f"⚠️ 数据转换失败: {project_msg_id}")
                            stats['failed'] += 1
                    else:
                        logger.warning(f"⚠️ 获取数据失败: {project_msg_id}")
                        stats['failed'] += 1

                    # 添加延迟避免请求过快
                    time.sleep(0.5)

                except Exception as e:
                    logger.error(f"❌ 处理项目失败 {project_msg_id}: {e}")
                    stats['failed'] += 1
                    continue

            # 输出统计信息
            logger.info(f"🎉 处理完成！总计: {stats['total']}, 成功: {stats['success']}, 失败: {stats['failed']}")

            return stats

        except Exception as e:
            logger.error(f"❌ 批量处理失败: {e}")
            return stats

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='甄选项目详情数据获取程序')
    parser.add_argument('--all', action='store_true', help='获取全部数据（默认只获取10条）')
    parser.add_argument('--project-id', type=str, help='指定单个projectMsgId')

    args = parser.parse_args()

    # 创建日志目录
    os.makedirs('logs', exist_ok=True)

    logger.info("🚀 启动甄选项目详情数据获取程序")

    try:
        fetcher = QuerySelectProjectDetailFetcher()

        if args.project_id:
            # 处理单个项目
            logger.info(f"🎯 处理单个项目: {args.project_id}")
            raw_data = fetcher.fetch_project_detail(args.project_id)

            if raw_data:
                transformed_data = fetcher.transform_data(raw_data, args.project_id)
                if transformed_data:
                    if fetcher.save_project_detail(transformed_data):
                        logger.info("✅ 单个项目处理成功")
                    else:
                        logger.error("❌ 单个项目保存失败")
                else:
                    logger.error("❌ 单个项目数据转换失败")
            else:
                logger.error("❌ 单个项目获取失败")
        else:
            # 批量处理
            stats = fetcher.fetch_and_save_all(get_all=args.all)

            if stats['success'] > 0:
                logger.info(f"✅ 批量处理完成，成功处理{stats['success']}个项目")
            else:
                logger.error("❌ 批量处理失败，没有成功处理任何项目")

    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
