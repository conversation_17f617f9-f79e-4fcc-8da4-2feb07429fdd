#!/usr/bin/env python3
"""
检查特定项目的数据情况
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from database.db_config import get_db_manager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_project_data():
    """检查特定项目数据"""
    
    project_no = 'CMGDZSICT20240715013'
    select_msg_id = '1903979207947370496'
    
    # 连接数据库
    db_manager = get_db_manager(use_zhenxuan=True)
    if not db_manager.connect():
        logger.error("❌ 数据库连接失败")
        return False
    
    try:
        with db_manager.get_cursor() as cursor:
            # 查询指定的项目数据
            sql = '''
            SELECT project_msg_id, project_no, select_msg_id, project_name, select_name, select_status_value
            FROM zhenxuan_querySelectProjectList 
            WHERE project_no = %s AND select_msg_id = %s
            '''
            cursor.execute(sql, (project_no, select_msg_id))
            result = cursor.fetchone()
            
            if result:
                print('✅ 在上游表中找到数据:')
                print(f'  项目消息ID: {result["project_msg_id"]}')
                print(f'  项目编号: {result["project_no"]}')
                print(f'  甄选消息ID: {result["select_msg_id"]}')
                print(f'  项目名称: {result["project_name"]}')
                print(f'  甄选名称: {result["select_name"]}')
                print(f'  甄选状态: {result["select_status_value"]}')
            else:
                print('❌ 在上游表中未找到指定数据')
                return False
                
            # 检查是否在目标表中已存在
            sql2 = '''
            SELECT id, select_msg_id, project_code, project_no, notice_name, real_publish_time
            FROM zhenxuan_queryNoticeHistoryBySelectId 
            WHERE project_code = %s AND select_msg_id = %s
            '''
            cursor.execute(sql2, (project_no, select_msg_id))
            result2 = cursor.fetchone()
            
            if result2:
                print('\n✅ 在目标表中找到数据:')
                print(f'  ID: {result2["id"]}')
                print(f'  甄选消息ID: {result2["select_msg_id"]}')
                print(f'  项目代码: {result2["project_code"]}')
                print(f'  项目编号: {result2["project_no"]}')
                print(f'  通知名称: {result2["notice_name"]}')
                print(f'  发布时间: {result2["real_publish_time"]}')
            else:
                print('\n❌ 在目标表中未找到数据')
                
            return True
        
    except Exception as e:
        logger.error(f"❌ 查询失败: {e}")
        return False
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    check_project_data()
