#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新创建甄选项目查询列表数据表
严格根据JSON数据结构创建
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import DatabaseManager, ZHENXUAN_DB_CONFIG

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def recreate_table():
    """重新创建数据表"""
    
    # 连接数据库
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    if not db_manager.connect():
        logger.error("❌ 数据库连接失败")
        return False
    
    logger.info("🚀 开始重新创建数据表...")
    
    try:
        with db_manager.get_cursor() as cursor:
            # 1. 删除旧表
            logger.info("📋 删除旧表（如果存在）...")
            cursor.execute("DROP TABLE IF EXISTS `zhenxuan_querySelectProjectList`")
            db_manager.connection.commit()
            logger.info("✅ 旧表删除完成")
            
            # 2. 创建新表 - 严格按照JSON数据结构
            logger.info("📋 创建新表...")
            create_sql = """
            CREATE TABLE `zhenxuan_querySelectProjectList` (
              `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
              
              -- API请求参数（扩展字段）
              `select_rev_id` VARCHAR(100) DEFAULT NULL COMMENT '甄选版本ID（入参）',
              `request_params` JSON DEFAULT NULL COMMENT '请求参数JSON',
              
              -- 响应基础信息（根级字段）
              `busi_date` DATETIME DEFAULT NULL COMMENT '业务日期 - busiDate',
              `code` VARCHAR(20) DEFAULT NULL COMMENT '响应代码 - code',
              `message` TEXT DEFAULT NULL COMMENT '响应消息 - message',
              
              -- resultBody分页信息
              `total` INT DEFAULT 0 COMMENT '总记录数 - resultBody.total',
              `size` INT DEFAULT 0 COMMENT '每页大小 - resultBody.size',
              `current` INT DEFAULT 0 COMMENT '当前页码 - resultBody.current',
              `pages` INT DEFAULT 0 COMMENT '总页数 - resultBody.pages',
              
              -- records数组中的项目核心信息
              `project_msg_id` VARCHAR(50) NOT NULL COMMENT '项目消息ID - projectMsgId',
              `work_order_msg_id` VARCHAR(50) DEFAULT NULL COMMENT '工单消息ID - workOrderMsgId',
              `shut_order_msg_id` VARCHAR(50) DEFAULT NULL COMMENT '关闭工单消息ID - shutOrderMsgId',
              `select_msg_id` VARCHAR(50) DEFAULT NULL COMMENT '甄选消息ID - selectMsgId',
              `select_apply_id` VARCHAR(50) DEFAULT NULL COMMENT '甄选申请ID - selectApplyId',
              
              -- 项目基本信息
              `project_name` VARCHAR(200) NOT NULL COMMENT '项目名称 - projectName',
              `select_name` VARCHAR(200) NOT NULL COMMENT '甄选需求名称 - selectName',
              `count` VARCHAR(10) DEFAULT '0' COMMENT '甄选方案数量 - count',
              `project_no` VARCHAR(50) DEFAULT NULL COMMENT '项目编码 - projectNo',
              
              -- 甄选类型
              `select_type` VARCHAR(20) DEFAULT NULL COMMENT '甄选类型 - selectType',
              `select_type_value` VARCHAR(50) DEFAULT NULL COMMENT '甄选类型值 - selectTypeValue',
              
              -- 项目分类
              `project_type` VARCHAR(20) DEFAULT NULL COMMENT '项目类型 - projectType',
              `project_label` VARCHAR(20) DEFAULT NULL COMMENT '项目标签 - projectLabel',
              
              -- 业务区域
              `business_area` VARCHAR(20) DEFAULT NULL COMMENT '业务区域代码 - businessArea',
              `business_area_value` VARCHAR(50) DEFAULT NULL COMMENT '业务区域名称 - businessAreaValue',
              
              -- 时间信息
              `start_time` DATETIME DEFAULT NULL COMMENT '开始时间 - startTime',
              `create_time` DATETIME DEFAULT NULL COMMENT '创建时间 - createTime',
              
              -- 状态信息
              `select_status` VARCHAR(20) DEFAULT NULL COMMENT '甄选状态代码 - selectStatus',
              `select_status_value` VARCHAR(50) DEFAULT NULL COMMENT '甄选状态值 - selectStatusValue',
              
              -- 部门和人员
              `initiate_department` VARCHAR(100) DEFAULT NULL COMMENT '发起部门 - initiateDepartment',
              `create_staff` VARCHAR(50) DEFAULT NULL COMMENT '创建人员代码 - createStaff',
              `create_staff_value` VARCHAR(50) DEFAULT NULL COMMENT '创建人员姓名 - createStaffValue',
              `next_todo_handler` VARCHAR(50) DEFAULT NULL COMMENT '下一步处理人代码 - nextTodoHandler',
              `next_todo_handler_value` VARCHAR(50) DEFAULT NULL COMMENT '下一步处理人姓名 - nextTodoHandlerValue',
              
              -- 操作标识
              `is_fixed_softness` VARCHAR(10) DEFAULT '0' COMMENT '是否固定软件 - isFixedSoftness',
              `is_operable` VARCHAR(10) DEFAULT '0' COMMENT '是否可操作 - isOperable',
              `is_terminable` VARCHAR(10) DEFAULT '0' COMMENT '是否可终止 - isTerminable',
              `is_allow_second` VARCHAR(10) DEFAULT NULL COMMENT '是否允许二次 - isAllowSecond',
              
              -- 变更类型
              `change_type1` VARCHAR(50) DEFAULT NULL COMMENT '变更类型1 - changeType1',
              `change_type2` VARCHAR(50) DEFAULT NULL COMMENT '变更类型2 - changeType2',
              
              -- 甄选分类
              `select_category` VARCHAR(20) DEFAULT NULL COMMENT '甄选分类代码 - selectCategory',
              `select_category_value` VARCHAR(50) DEFAULT NULL COMMENT '甄选分类值 - selectCategoryValue',
              
              -- 二次谈判
              `dpcs_select_second_negotiate` VARCHAR(50) DEFAULT NULL COMMENT '二次谈判标识 - dpcsSelectSecondNegotiate',
              
              -- 原始数据
              `raw_data` JSON DEFAULT NULL COMMENT '原始JSON数据',
              
              -- 系统字段
              `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
              `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
              
              -- 索引
              UNIQUE KEY `uk_project_msg_id` (`project_msg_id`),
              KEY `idx_project_no` (`project_no`),
              KEY `idx_select_status` (`select_status`),
              KEY `idx_business_area` (`business_area`),
              KEY `idx_create_time` (`create_time`),
              KEY `idx_start_time` (`start_time`),
              KEY `idx_create_staff` (`create_staff`),
              KEY `idx_select_category` (`select_category`),
              KEY `idx_composite_status_area` (`select_status`, `business_area`),
              KEY `idx_composite_time_status` (`create_time`, `select_status`)
              
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='甄选项目查询列表数据表'
            """
            
            cursor.execute(create_sql)
            db_manager.connection.commit()
            logger.info("✅ 表创建成功")
            
            # 3. 验证表创建
            logger.info("🔍 验证表创建...")
            cursor.execute("SHOW TABLES LIKE 'zhenxuan_querySelectProjectList'")
            result = cursor.fetchone()
            
            if result:
                logger.info("✅ 表 'zhenxuan_querySelectProjectList' 验证成功")
                
                # 显示表结构
                cursor.execute("DESCRIBE zhenxuan_querySelectProjectList")
                columns = cursor.fetchall()
                logger.info(f"📋 表结构: {len(columns)} 个字段")
                
                # 显示所有字段及其对应的JSON字段
                logger.info("📋 字段映射:")
                json_mapping = {
                    'busi_date': 'busiDate',
                    'code': 'code', 
                    'message': 'message',
                    'total': 'resultBody.total',
                    'size': 'resultBody.size',
                    'current': 'resultBody.current',
                    'pages': 'resultBody.pages',
                    'project_msg_id': 'projectMsgId',
                    'work_order_msg_id': 'workOrderMsgId',
                    'shut_order_msg_id': 'shutOrderMsgId',
                    'select_msg_id': 'selectMsgId',
                    'select_apply_id': 'selectApplyId',
                    'project_name': 'projectName',
                    'select_name': 'selectName',
                    'count': 'count',
                    'project_no': 'projectNo',
                    'select_type': 'selectType',
                    'select_type_value': 'selectTypeValue',
                    'project_type': 'projectType',
                    'project_label': 'projectLabel',
                    'business_area': 'businessArea',
                    'business_area_value': 'businessAreaValue',
                    'start_time': 'startTime',
                    'create_time': 'createTime',
                    'select_status': 'selectStatus',
                    'select_status_value': 'selectStatusValue',
                    'initiate_department': 'initiateDepartment',
                    'create_staff': 'createStaff',
                    'create_staff_value': 'createStaffValue',
                    'next_todo_handler': 'nextTodoHandler',
                    'next_todo_handler_value': 'nextTodoHandlerValue',
                    'is_fixed_softness': 'isFixedSoftness',
                    'is_operable': 'isOperable',
                    'is_terminable': 'isTerminable',
                    'is_allow_second': 'isAllowSecond',
                    'change_type1': 'changeType1',
                    'change_type2': 'changeType2',
                    'select_category': 'selectCategory',
                    'select_category_value': 'selectCategoryValue',
                    'dpcs_select_second_negotiate': 'dpcsSelectSecondNegotiate'
                }
                
                for col in columns:
                    field_name = col['Field']
                    json_field = json_mapping.get(field_name, '系统字段')
                    logger.info(f"  - {field_name}: {col['Type']} → {json_field}")
                
                return True
            else:
                logger.error("❌ 表验证失败")
                return False
                
    except Exception as e:
        logger.error(f"❌ 创建表失败: {e}")
        return False
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    success = recreate_table()
    if success:
        logger.info("🎉 数据表重新创建完成！")
        logger.info("📋 表结构已严格按照JSON数据结构映射")
        logger.info("📋 所有字段都已正确对应JSON中的字段名")
    else:
        logger.error("❌ 数据表创建失败！")
        sys.exit(1)
