#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建甄选审核跟踪历史数据表
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import DatabaseManager, ZHENXUAN_DB_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_audit_track_table():
    """创建审核跟踪历史表"""
    
    # 创建表的SQL
    create_table_sql = """
    -- 删除表（如果存在）
    DROP TABLE IF EXISTS `zhenxuan_queryselectaudittrackhistory`;

    -- 创建甄选审核跟踪历史表
    CREATE TABLE `zhenxuan_queryselectaudittrackhistory` (
      `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',

      -- API请求参数（扩展字段）
      `work_order_msg_id` VARCHAR(100) NOT NULL COMMENT '工单消息ID（入参）',
      `request_params` JSON DEFAULT NULL COMMENT '请求参数JSON',

      -- 响应基础信息（根级字段）
      `busi_date` DATETIME DEFAULT NULL COMMENT '业务日期 - busiDate',
      `code` VARCHAR(20) DEFAULT NULL COMMENT '响应代码 - code',
      `message` TEXT DEFAULT NULL COMMENT '响应消息 - message',

      -- resultBody基础信息
      `own_opinion` TEXT DEFAULT NULL COMMENT '自己意见 - ownOpinion',
      `doc_title` VARCHAR(500) DEFAULT NULL COMMENT '文档标题 - docTitle',
      `last_approval_end_time` DATETIME DEFAULT NULL COMMENT '最后审批结束时间 - lastApprovalEndTime',
      `drafting_unit` VARCHAR(200) DEFAULT NULL COMMENT '起草单位 - DRAFTING_UNIT',
      `distribute_unit` JSON DEFAULT NULL COMMENT '分发单位列表 - DISTRIBUTE_UNIT',
      `drafting_staff_name` VARCHAR(100) DEFAULT NULL COMMENT '起草人员姓名 - DRAFTING_STAFF_NAME',
      `drafting_staff_tel` VARCHAR(20) DEFAULT NULL COMMENT '起草人员电话 - DRAFTING_STAFF_TEL',
      `approval_end_time` VARCHAR(50) DEFAULT NULL COMMENT '审批结束时间 - APPROVAL_END_TIME',

      -- CUR_REVIEW_LEVEL_LIST 审核级别列表（单条记录对应一个审核步骤）
      `total_seq` VARCHAR(10) DEFAULT NULL COMMENT '总序号 - TOTAL_SEQ',
      `cur_review_id` VARCHAR(50) DEFAULT NULL COMMENT '当前审核ID - CUR_REVIEW_ID',
      `cur_review_name` VARCHAR(200) DEFAULT NULL COMMENT '当前审核名称 - CUR_REVIEW_NAME',
      `cur_riview_role_id` VARCHAR(100) DEFAULT NULL COMMENT '当前审核角色ID - CUR_RIVIEW_ROLE_ID',
      `cur_riview_role_name` VARCHAR(200) DEFAULT NULL COMMENT '当前审核角色名称 - CUR_RIVIEW_ROLE_NAME',
      `cur_riview_oper_id` VARCHAR(100) DEFAULT NULL COMMENT '当前审核操作员ID - CUR_RIVIEW_OPER_ID',
      `cur_riview_oper_name` VARCHAR(100) DEFAULT NULL COMMENT '当前审核操作员姓名 - CUR_RIVIEW_OPER_NAME',
      `cur_riview_oper_class` VARCHAR(200) DEFAULT NULL COMMENT '当前审核操作员部门 - CUR_RIVIEW_OPER_CLASS',
      `riview_state` INT DEFAULT NULL COMMENT '审核状态 - RIVIEW_STATE',
      `end_date` DATETIME DEFAULT NULL COMMENT '结束日期 - END_DATE',
      `flow_define_id` VARCHAR(100) DEFAULT NULL COMMENT '流程定义ID - FLOW_DEFINE_ID',
      `flow_name` VARCHAR(200) DEFAULT NULL COMMENT '流程名称 - FLOW_NAME',
      `cur_review_level` INT DEFAULT NULL COMMENT '当前审核级别 - CUR_REVIEW_LEVEL',
      `cur_review_level_name` VARCHAR(200) DEFAULT NULL COMMENT '当前审核级别名称 - CUR_REVIEW_LEVEL_NAME',
      `comment` TEXT DEFAULT NULL COMMENT '审核意见 - COMMENT',

      -- 原始数据
      `raw_data` JSON DEFAULT NULL COMMENT '原始JSON数据',
      `review_level_raw_data` JSON DEFAULT NULL COMMENT '审核级别原始JSON数据',
      
      -- 系统字段
      `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
      `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
      
      -- 索引
      KEY `idx_work_order_msg_id` (`work_order_msg_id`),
      KEY `idx_cur_riview_oper_id` (`cur_riview_oper_id`),
      KEY `idx_flow_define_id` (`flow_define_id`),
      KEY `idx_riview_state` (`riview_state`),
      KEY `idx_cur_review_level` (`cur_review_level`),
      KEY `idx_total_seq` (`total_seq`),
      KEY `idx_end_date` (`end_date`),
      KEY `idx_composite_work_order_seq` (`work_order_msg_id`, `total_seq`)
      
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='甄选审核跟踪历史数据表';
    """

    # 创建视图的SQL
    create_view_sql = """
    CREATE OR REPLACE VIEW `v_zhenxuan_audit_track_summary` AS
    SELECT
        id,
        work_order_msg_id,
        total_seq,
        cur_riview_oper_name,
        cur_riview_oper_class,
        cur_review_level_name,
        riview_state,
        end_date,
        flow_name,
        comment,
        created_at
    FROM `zhenxuan_queryselectaudittrackhistory`
    ORDER BY work_order_msg_id, CAST(total_seq AS UNSIGNED)
    """

    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    try:
        if not db_manager.connect():
            logger.error("❌ 数据库连接失败")
            return False

        logger.info("🚀 开始创建甄选审核跟踪历史数据表...")

        # 执行创建表SQL
        with db_manager.get_cursor() as cursor:
            # 先删除表
            cursor.execute("DROP TABLE IF EXISTS `zhenxuan_queryselectaudittrackhistory`")

            # 创建表
            create_sql = create_table_sql.split('CREATE TABLE')[1]
            create_sql = 'CREATE TABLE' + create_sql
            # 移除注释和多余的分号
            create_sql = create_sql.split(';')[0] + ';'

            cursor.execute(create_sql)
            db_manager.connection.commit()
            logger.info("✅ 数据表创建成功")

        # 执行创建视图SQL
        try:
            with db_manager.get_cursor() as cursor:
                cursor.execute(create_view_sql)
                db_manager.connection.commit()
                logger.info("✅ 视图创建成功")
        except Exception as e:
            logger.warning(f"⚠️ 视图创建失败: {e}")
            # 视图创建失败不影响主要功能

        # 验证表创建
        with db_manager.get_cursor() as cursor:
            cursor.execute("""
                SELECT 
                    TABLE_NAME,
                    TABLE_COMMENT,
                    TABLE_COLLATION
                FROM information_schema.TABLES 
                WHERE TABLE_SCHEMA = 'zhenxuandb' 
                AND TABLE_NAME = 'zhenxuan_queryselectaudittrackhistory'
            """)
            result = cursor.fetchone()
            
            if result:
                logger.info(f"✅ 表验证成功: {result['TABLE_NAME']} - {result['TABLE_COMMENT']}")
                return True
            else:
                logger.error("❌ 表验证失败")
                return False

    except Exception as e:
        logger.error(f"❌ 创建表失败: {e}")
        return False
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    success = create_audit_track_table()
    if success:
        logger.info("🎉 甄选审核跟踪历史数据表创建完成！")
    else:
        logger.error("❌ 甄选审核跟踪历史数据表创建失败！")
        sys.exit(1)
