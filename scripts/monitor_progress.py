#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控数据获取进度
"""

import sys
import os
import time
from datetime import datetime
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import get_db_manager

def monitor_progress():
    """监控进度"""
    try:
        db = get_db_manager()
        db.connect()

        # 获取总的selectMsgId数量
        total_result = db.execute_query('''
        SELECT COUNT(DISTINCT select_msg_id) as total 
        FROM zhenxuan_querySelectProjectList 
        WHERE select_msg_id IS NOT NULL AND select_msg_id != ""
        ''')
        total_count = total_result[0]['total']

        # 获取已处理的数量
        processed_result = db.execute_query('''
        SELECT COUNT(*) as processed 
        FROM zhenxuan_querypartnerselectdetail
        ''')
        processed_count = processed_result[0]['processed']

        # 计算进度
        progress_percent = (processed_count / total_count * 100) if total_count > 0 else 0

        # 获取最近处理的数据
        recent_result = db.execute_query('''
        SELECT project_name, created_at 
        FROM zhenxuan_querypartnerselectdetail 
        ORDER BY created_at DESC 
        LIMIT 5
        ''')

        print(f"📊 数据获取进度监控")
        print(f"🕒 监控时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📈 总进度: {processed_count}/{total_count} ({progress_percent:.1f}%)")
        print(f"⏱️ 预计剩余: {total_count - processed_count} 项目")
        
        if processed_count > 0:
            print(f"\n📋 最近处理的项目:")
            for i, row in enumerate(recent_result, 1):
                project_name = row['project_name'][:40] + '...' if len(row['project_name']) > 40 else row['project_name']
                print(f"  {i}. {project_name} - {row['created_at']}")

        # 进度条
        bar_length = 50
        filled_length = int(bar_length * progress_percent / 100)
        bar = '█' * filled_length + '░' * (bar_length - filled_length)
        print(f"\n🔄 进度条: [{bar}] {progress_percent:.1f}%")

        if progress_percent >= 100:
            print("\n🎉 数据获取已完成！")
        else:
            print(f"\n⏳ 数据获取进行中...")

    except Exception as e:
        print(f"❌ 监控失败: {e}")

if __name__ == '__main__':
    monitor_progress()
