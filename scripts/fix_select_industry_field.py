#!/usr/bin/env python3
"""
修复 select_industry 字段长度
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from database.db_config import get_db_manager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_field():
    """修复字段长度"""
    
    # 连接数据库
    db_manager = get_db_manager(use_zhenxuan=True)
    if not db_manager.connect():
        logger.error("❌ 数据库连接失败")
        return False
    
    try:
        with db_manager.get_cursor() as cursor:
            sql = "ALTER TABLE zhenxuan_queryNoticeHistoryBySelectId MODIFY COLUMN select_industry VARCHAR(100) DEFAULT NULL COMMENT '甄选行业 - selectIndustry'"
            cursor.execute(sql)
            db_manager.connection.commit()
            logger.info("✅ 字段长度修改成功")
            return True
        
    except Exception as e:
        logger.error(f"❌ 修改字段失败: {e}")
        return False
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    fix_field()
