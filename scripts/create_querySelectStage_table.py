"""
创建甄选阶段查询数据表
执行 create_zhenxuan_querySelectStage.sql 脚本
"""

import os
import sys
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from database.db_config import ZHENXUAN_DB_CONFIG, DatabaseManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_table():
    """创建甄选阶段查询数据表"""
    
    # SQL文件路径
    sql_file_path = os.path.join(project_root, 'database', 'create_zhenxuan_querySelectStage.sql')
    
    if not os.path.exists(sql_file_path):
        logger.error(f"❌ SQL文件不存在: {sql_file_path}")
        return False
    
    try:
        # 读取SQL文件
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 分割SQL语句（按分号分割，但保留完整的语句）
        sql_statements = []
        current_statement = ""

        for line in sql_content.split('\n'):
            line = line.strip()
            if line and not line.startswith('--'):
                current_statement += line + " "
                if line.endswith(';'):
                    sql_statements.append(current_statement.strip())
                    current_statement = ""

        # 过滤掉空语句
        sql_statements = [stmt for stmt in sql_statements if stmt and not stmt.upper().startswith('USE')]
        
        # 创建数据库管理器
        db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
        
        logger.info("🚀 开始创建甄选阶段查询数据表...")
        
        with db_manager.get_connection() as conn:
            with conn.cursor() as cursor:
                
                for i, sql_statement in enumerate(sql_statements, 1):
                    try:
                        logger.info(f"📋 执行SQL语句 {i}/{len(sql_statements)}")
                        logger.debug(f"SQL: {sql_statement[:100]}...")
                        cursor.execute(sql_statement)
                        conn.commit()
                        
                        # 特殊处理一些语句的日志输出
                        if 'DROP TABLE' in sql_statement.upper():
                            logger.info("🗑️ 删除旧表（如果存在）")
                        elif 'CREATE TABLE' in sql_statement.upper():
                            logger.info("📊 创建新表结构")
                        elif 'CREATE OR REPLACE VIEW' in sql_statement.upper():
                            logger.info("👁️ 创建视图")
                        elif 'DESCRIBE' in sql_statement.upper():
                            logger.info("🔍 验证表结构")
                            # 显示表结构
                            results = cursor.fetchall()
                            if results:
                                logger.info("📋 表结构验证结果:")
                                for row in results:
                                    logger.info(f"   {row}")
                        elif 'SELECT' in sql_statement.upper() and 'information_schema' in sql_statement.lower():
                            logger.info("✅ 验证表创建")
                            # 显示验证结果
                            results = cursor.fetchall()
                            if results:
                                for row in results:
                                    logger.info(f"   表名: {row[0]}, 注释: {row[1]}, 排序规则: {row[2]}")
                        
                    except Exception as e:
                        logger.error(f"❌ 执行SQL语句失败: {e}")
                        logger.error(f"📋 SQL语句: {sql_statement[:100]}...")
                        return False
        
        logger.info("✅ 甄选阶段查询数据表创建成功！")
        
        # 验证表是否创建成功
        verify_table_creation(db_manager)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建表失败: {e}")
        return False

def verify_table_creation(db_manager: DatabaseManager):
    """验证表创建是否成功"""
    
    try:
        with db_manager.get_connection() as conn:
            with conn.cursor() as cursor:
                
                # 检查表是否存在
                cursor.execute("""
                    SELECT TABLE_NAME, TABLE_COMMENT, TABLE_COLLATION
                    FROM information_schema.TABLES 
                    WHERE TABLE_SCHEMA = 'zhenxuandb' 
                    AND TABLE_NAME = 'zhenxuan_querySelectStage'
                """)
                
                result = cursor.fetchone()
                if result:
                    table_name, table_comment, table_collation = result
                    logger.info(f"✅ 表验证成功:")
                    logger.info(f"   📊 表名: {table_name}")
                    logger.info(f"   📝 注释: {table_comment}")
                    logger.info(f"   🔤 排序规则: {table_collation}")
                    
                    # 检查字段数量
                    cursor.execute("SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = 'zhenxuandb' AND TABLE_NAME = 'zhenxuan_querySelectStage'")
                    field_count = cursor.fetchone()[0]
                    logger.info(f"   📋 字段数量: {field_count}")
                    
                    # 检查索引数量
                    cursor.execute("SELECT COUNT(*) FROM information_schema.STATISTICS WHERE TABLE_SCHEMA = 'zhenxuandb' AND TABLE_NAME = 'zhenxuan_querySelectStage'")
                    index_count = cursor.fetchone()[0]
                    logger.info(f"   🔍 索引数量: {index_count}")
                    
                else:
                    logger.error("❌ 表验证失败: 表不存在")
                    
    except Exception as e:
        logger.error(f"❌ 表验证失败: {e}")

def main():
    """主程序入口"""
    logger.info("🎯 甄选阶段查询数据表创建程序")
    logger.info("=" * 50)
    
    success = create_table()
    
    if success:
        logger.info("🎉 程序执行成功！")
        logger.info("💡 接下来可以运行数据获取程序:")
        logger.info("   python scripts/fetch_querySelectStage.py")
    else:
        logger.error("💥 程序执行失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
