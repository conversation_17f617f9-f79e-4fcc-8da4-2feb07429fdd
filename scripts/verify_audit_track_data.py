"""
验证本地审核跟踪历史数据
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from database.db_config import ZHENXUAN_DB_CONFIG, DatabaseManager

def verify_audit_track_data():
    """验证审核跟踪历史数据"""
    
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    if not db_manager.connect():
        print("❌ 数据库连接失败")
        return
    
    try:
        with db_manager.get_connection() as conn:
            with conn.cursor() as cursor:
                # 查看总记录数
                cursor.execute("SELECT COUNT(*) FROM zhenxuan_querylocalaudittrackhistory")
                total_count = cursor.fetchone()[0]
                print(f"📊 总记录数: {total_count}")
                
                # 查看最新的几条记录
                cursor.execute("""
                    SELECT project_msg_id, business_id, work_order_msg_id, step_name, status,
                           audit_handler, create_time, finish_time, audit_remark
                    FROM zhenxuan_querylocalaudittrackhistory
                    ORDER BY created_at DESC
                    LIMIT 5
                """)
                records = cursor.fetchall()
                
                print("\n📋 最新的5条记录:")
                for i, record in enumerate(records, 1):
                    project_msg_id, business_id, work_order_msg_id, step_name, status, audit_handler, create_time, finish_time, audit_remark = record
                    print(f"\n{i}. 项目消息ID: {project_msg_id}")
                    print(f"   业务ID: {business_id}")
                    print(f"   工单消息ID: {work_order_msg_id}")
                    print(f"   步骤名称: {step_name}")
                    print(f"   状态: {status}")
                    print(f"   审核处理人: {audit_handler}")
                    print(f"   创建时间: {create_time}")
                    print(f"   完成时间: {finish_time}")
                    print(f"   审核备注: {audit_remark}")
                
                # 统计不同状态的记录数
                print("\n📈 状态统计:")
                cursor.execute("""
                    SELECT status, COUNT(*) as count
                    FROM zhenxuan_querylocalaudittrackhistory 
                    GROUP BY status
                    ORDER BY count DESC
                """)
                status_stats = cursor.fetchall()
                
                for status, count in status_stats:
                    print(f"   {status}: {count}条")
                
                # 统计不同步骤的记录数
                print("\n📈 步骤统计:")
                cursor.execute("""
                    SELECT step_name, COUNT(*) as count
                    FROM zhenxuan_querylocalaudittrackhistory 
                    GROUP BY step_name
                    ORDER BY count DESC
                """)
                step_stats = cursor.fetchall()
                
                for step_name, count in step_stats:
                    print(f"   {step_name}: {count}条")
                
                # 测试关联查询视图
                print("\n🔗 关联查询测试:")
                cursor.execute("""
                    SELECT project_id, project_msg_id, project_name,
                           audit_id, step_name, audit_status, audit_handler
                    FROM v_zhenxuan_project_audit_relation
                    WHERE audit_id IS NOT NULL
                    LIMIT 3
                """)
                relation_records = cursor.fetchall()
                
                if relation_records:
                    for record in relation_records:
                        project_id, project_msg_id, project_name, audit_id, step_name, audit_status, audit_handler = record
                        print(f"   项目ID: {project_id}, 项目消息ID: {project_msg_id}")
                        print(f"   项目名称: {project_name}, 审核步骤: {step_name}")
                        print(f"   审核状态: {audit_status}, 处理人: {audit_handler}")
                        print()
                else:
                    print("   暂无关联数据")
                    
    except Exception as e:
        print(f"❌ 验证失败: {e}")
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    print("🔍 验证本地审核跟踪历史数据")
    print("=" * 50)
    verify_audit_track_data()
