#!/usr/bin/env python3
"""
甄选邮件信息历史查询数据获取脚本
根据 queryNoticeHistoryBySelectId 接口获取邮件信息数据并入库

功能：
1. 从 zhenxuan_querySelectProjectList 表获取 projectNo 和 selectMsgId 作为入参
2. 调用 queryNoticeHistoryBySelectId 接口获取邮件信息历史数据
3. 将数据转换并保存到 zhenxuan_queryNoticeHistoryBySelectId 表
4. 支持单个查询和批量轮询模式

使用方法：
    python scripts/zhenxuan_queryNoticeHistoryBySelectId.py --all  # 轮询所有数据
    python scripts/zhenxuan_queryNoticeHistoryBySelectId.py --select-msg-id "xxx" --project-code "xxx"  # 单个查询
    python scripts/zhenxuan_queryNoticeHistoryBySelectId.py --query  # 查询已同步数据
"""

import sys
import os
import json
import time
import logging
import requests
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from database.db_config import get_db_manager
from auth_loader import AuthLoader

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('zhenxuan_notice_history_fetch.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ZhenxuanNoticeHistoryFetcher:
    """甄选邮件信息历史数据获取器"""
    
    def __init__(self, cookie_file_path: Optional[str] = None):
        """
        初始化数据获取器

        Args:
            cookie_file_path: Cookie文件路径，默认使用cookies/cookies_dict_zhenxuan.json
        """
        self.base_url = "https://dict.gmcc.net:30722"
        self.db_manager = get_db_manager(use_zhenxuan=True)
        self.session = requests.Session()

        # 使用 AuthLoader 加载认证信息
        auth_loader = AuthLoader(cookie_file_path)
        if auth_loader.load_auth_data():
            # 更新session的认证信息
            auth_loader.update_session(self.session)
            logger.info("✅ 认证信息加载成功")
        else:
            logger.error("❌ 认证信息加载失败")

        # 设置基本请求头（AuthLoader会自动添加认证相关的headers）
        self.session.headers.update({
            'Host': 'dict.gmcc.net:30722',
            'Referer': 'https://dict.gmcc.net:30722/ptn/main/selectDemand/detail',
        })
    

    

    
    def fetch_notice_history(self, select_msg_id: str, project_code: str) -> Optional[Dict[str, Any]]:
        """
        获取甄选邮件信息历史数据
        
        Args:
            select_msg_id: 甄选消息ID
            project_code: 项目代码
            
        Returns:
            Dict: API响应数据，失败返回None
        """
        url = f"{self.base_url}/partner/materialManage/pnrSelect/queryNoticeHistoryBySelectId"
        
        # 构造请求参数
        params = {
            "selectMsgId": select_msg_id,
            "projectCode": project_code
        }
        
        try:
            logger.info(f"🌐 请求邮件历史数据: selectMsgId={select_msg_id}, projectCode={project_code}")

            # 使用session发送请求（认证信息已通过AuthLoader设置）
            response = self.session.get(
                url,
                params=params,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ 成功获取邮件历史数据: {len(data.get('resultBody', []))} 条记录")
                return data
            else:
                logger.error(f"❌ 请求失败，状态码: {response.status_code}")
                logger.error(f"响应内容: {response.text[:500]}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 请求异常: {e}")
            return None
    
    def transform_data(self, raw_data: Dict[str, Any], select_msg_id: str, project_code: str, project_no: str) -> List[Dict[str, Any]]:
        """
        转换API响应数据为数据库格式
        
        Args:
            raw_data: API原始响应数据
            select_msg_id: 甄选消息ID
            project_code: 项目代码
            project_no: 项目编号
            
        Returns:
            List[Dict]: 转换后的数据列表
        """
        try:
            result_list = []
            result_body = raw_data.get('resultBody', [])
            
            if not result_body:
                logger.warning("⚠️ resultBody为空")
                return []
            
            for notice_item in result_body:
                # 转换数据格式
                transformed_item = {
                    # 入参字段
                    'select_msg_id': select_msg_id,
                    'project_code': project_code,
                    'project_no': project_no,
                    'request_params': json.dumps({
                        'selectMsgId': select_msg_id,
                        'projectCode': project_code
                    }),
                    
                    # 响应基础信息
                    'busi_date': self.parse_datetime(raw_data.get('busiDate')),
                    'code': raw_data.get('code'),
                    'message': raw_data.get('message'),
                    
                    # 邮件信息字段
                    'select_notice_id': notice_item.get('selectNoticeId'),
                    'notice_name': notice_item.get('noticeName'),
                    'select_type': notice_item.get('selectType'),
                    'select_type_value': notice_item.get('selectTypeValue'),
                    'select_time': self.parse_datetime(notice_item.get('selectTime')),
                    'real_publish_time': self.parse_datetime(notice_item.get('realPublishTime')),
                    'select_industry': notice_item.get('selectIndustry'),
                    'notice_row_num': notice_item.get('noticeRowNum'),
                    'notices_version': notice_item.get('noticesVersion'),
                    'industry_value': notice_item.get('industryValue'),
                    'send_status': notice_item.get('sendStatus'),
                    'send_status_value': notice_item.get('sendStatusValue'),
                    'email_partner_ids': notice_item.get('emailPartnerIds'),
                    'is_clarify': notice_item.get('isClarify', False),
                    
                    # 原始数据
                    'raw_data': json.dumps(notice_item, ensure_ascii=False)
                }
                
                result_list.append(transformed_item)
            
            logger.info(f"✅ 成功转换 {len(result_list)} 条邮件历史记录")
            return result_list
            
        except Exception as e:
            logger.error(f"❌ 数据转换失败: {e}")
            return []
    
    def parse_datetime(self, date_str: str) -> Optional[str]:
        """
        解析日期时间字符串
        
        Args:
            date_str: 日期时间字符串
            
        Returns:
            str: 格式化后的日期时间字符串，失败返回None
        """
        if not date_str:
            return None
        
        try:
            # 处理多种日期格式
            if '.' in date_str:
                # 处理带毫秒的格式：2025-03-25 17:42:38.442
                dt = datetime.strptime(date_str.split('.')[0], '%Y-%m-%d %H:%M:%S')
            else:
                # 处理标准格式：2025-03-21 00:00:00
                dt = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
            
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            logger.warning(f"⚠️ 日期解析失败: {date_str} - {e}")
            return None

    def save_notice_history(self, notice_data_list: List[Dict[str, Any]]) -> bool:
        """
        保存邮件历史数据到数据库

        Args:
            notice_data_list: 邮件历史数据列表

        Returns:
            bool: 保存是否成功
        """
        if not notice_data_list:
            logger.warning("⚠️ 没有数据需要保存")
            return False

        if not self.db_manager.connect():
            return False

        try:
            sql = """
            INSERT INTO zhenxuan_queryNoticeHistoryBySelectId (
                select_msg_id, project_code, project_no, request_params,
                busi_date, code, message,
                select_notice_id, notice_name, select_type, select_type_value,
                select_time, real_publish_time, select_industry, notice_row_num,
                notices_version, industry_value, send_status, send_status_value,
                email_partner_ids, is_clarify, raw_data
            ) VALUES (
                %(select_msg_id)s, %(project_code)s, %(project_no)s, %(request_params)s,
                %(busi_date)s, %(code)s, %(message)s,
                %(select_notice_id)s, %(notice_name)s, %(select_type)s, %(select_type_value)s,
                %(select_time)s, %(real_publish_time)s, %(select_industry)s, %(notice_row_num)s,
                %(notices_version)s, %(industry_value)s, %(send_status)s, %(send_status_value)s,
                %(email_partner_ids)s, %(is_clarify)s, %(raw_data)s
            ) ON DUPLICATE KEY UPDATE
                project_code = VALUES(project_code),
                project_no = VALUES(project_no),
                request_params = VALUES(request_params),
                busi_date = VALUES(busi_date),
                code = VALUES(code),
                message = VALUES(message),
                notice_name = VALUES(notice_name),
                select_type = VALUES(select_type),
                select_type_value = VALUES(select_type_value),
                select_time = VALUES(select_time),
                real_publish_time = VALUES(real_publish_time),
                select_industry = VALUES(select_industry),
                notice_row_num = VALUES(notice_row_num),
                notices_version = VALUES(notices_version),
                industry_value = VALUES(industry_value),
                send_status = VALUES(send_status),
                send_status_value = VALUES(send_status_value),
                email_partner_ids = VALUES(email_partner_ids),
                is_clarify = VALUES(is_clarify),
                raw_data = VALUES(raw_data),
                updated_at = CURRENT_TIMESTAMP
            """

            success_count = 0
            with self.db_manager.get_cursor() as cursor:
                for notice_data in notice_data_list:
                    try:
                        cursor.execute(sql, notice_data)
                        success_count += 1
                    except Exception as e:
                        logger.error(f"❌ 插入记录失败: {e}")
                        logger.error(f"记录数据: {notice_data.get('select_notice_id', 'Unknown')}")
                        continue

                self.db_manager.connection.commit()

            logger.info(f"✅ 成功保存 {success_count}/{len(notice_data_list)} 条邮件历史记录")
            return success_count > 0

        except Exception as e:
            logger.error(f"❌ 保存数据失败: {e}")
            return False
        finally:
            self.db_manager.disconnect()

    def get_project_params_from_db(self) -> List[Tuple[str, str, str]]:
        """
        从数据库获取项目参数列表

        Returns:
            List[Tuple[str, str, str]]: (select_msg_id, project_no, project_no) 列表
        """
        if not self.db_manager.connect():
            return []

        try:
            sql = """
            SELECT DISTINCT select_msg_id, project_no
            FROM zhenxuan_querySelectProjectList
            WHERE select_msg_id IS NOT NULL
            AND project_no IS NOT NULL
            AND select_msg_id != ''
            AND project_no != ''
            ORDER BY select_msg_id DESC
            """

            with self.db_manager.get_cursor() as cursor:
                cursor.execute(sql)
                results = cursor.fetchall()

                # 转换为元组列表
                param_list = [(row['select_msg_id'], row['project_no'], row['project_no']) for row in results]

                logger.info(f"📋 从数据库获取到 {len(param_list)} 个项目参数")
                return param_list

        except Exception as e:
            logger.error(f"❌ 获取项目参数失败: {e}")
            return []
        finally:
            self.db_manager.disconnect()

    def fetch_and_save_all(self) -> Dict[str, int]:
        """
        轮询所有项目的邮件历史数据

        Returns:
            Dict[str, int]: 统计信息
        """
        logger.info("🚀 开始轮询所有项目的邮件历史数据...")

        # 获取项目参数列表
        project_params = self.get_project_params_from_db()

        if not project_params:
            logger.warning("⚠️ 没有找到可用的项目参数")
            return {'total': 0, 'success': 0, 'failed': 0}

        stats = {
            'total': len(project_params),
            'success': 0,
            'failed': 0
        }

        logger.info(f"📊 准备处理 {stats['total']} 个项目")

        for i, (select_msg_id, project_code, project_no) in enumerate(project_params, 1):
            try:
                logger.info(f"📊 处理进度: {i}/{stats['total']} - selectMsgId: {select_msg_id}, projectCode: {project_code}")

                # 获取邮件历史数据
                raw_data = self.fetch_notice_history(select_msg_id, project_code)

                if raw_data:
                    # 转换数据格式
                    transformed_data = self.transform_data(raw_data, select_msg_id, project_code, project_no)

                    if transformed_data:
                        # 保存到数据库
                        if self.save_notice_history(transformed_data):
                            stats['success'] += 1
                        else:
                            stats['failed'] += 1
                    else:
                        logger.warning(f"⚠️ 数据转换失败: selectMsgId={select_msg_id}")
                        stats['failed'] += 1
                else:
                    logger.warning(f"⚠️ 获取数据失败: selectMsgId={select_msg_id}")
                    stats['failed'] += 1

                # 添加延迟避免请求过快
                time.sleep(1)

            except Exception as e:
                logger.error(f"❌ 处理项目失败 selectMsgId={select_msg_id}: {e}")
                stats['failed'] += 1
                continue

        logger.info(f"🎉 轮询完成！总计: {stats['total']}, 成功: {stats['success']}, 失败: {stats['failed']}")
        return stats

    def query_data(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        查询数据库中的邮件历史数据

        Args:
            limit: 查询记录数限制

        Returns:
            List[Dict]: 查询结果
        """
        if not self.db_manager.connect():
            return []

        try:
            sql = """
            SELECT
                id, select_msg_id, project_code, project_no, notice_name,
                select_type_value, real_publish_time, send_status_value,
                industry_value, notices_version, is_clarify, created_at
            FROM zhenxuan_queryNoticeHistoryBySelectId
            ORDER BY real_publish_time DESC, created_at DESC
            LIMIT %s
            """

            with self.db_manager.get_cursor() as cursor:
                cursor.execute(sql, (limit,))
                return cursor.fetchall()

        except Exception as e:
            logger.error(f"❌ 查询数据失败: {e}")
            return []
        finally:
            self.db_manager.disconnect()


def main():
    """主程序入口"""
    import argparse

    parser = argparse.ArgumentParser(description='甄选邮件信息历史数据获取和入库程序')
    parser.add_argument('--select-msg-id', type=str, help='指定甄选消息ID')
    parser.add_argument('--project-code', type=str, help='指定项目代码')
    parser.add_argument('--cookie-file', type=str, help='Cookie文件路径，默认使用cookies/cookies_dict_zhenxuan.json')
    parser.add_argument('--cookie', type=str, help='更新Cookie字符串')
    parser.add_argument('--query', action='store_true', help='查询已同步的数据')
    parser.add_argument('--limit', type=int, default=10, help='查询记录数限制，默认10')
    parser.add_argument('--all', action='store_true', help='轮询所有项目模式，从数据库获取所有项目参数并同步')

    args = parser.parse_args()

    # 创建数据获取器（使用指定的Cookie文件）
    fetcher = ZhenxuanNoticeHistoryFetcher(cookie_file_path=args.cookie_file)

    # 更新Cookie（如果提供）
    if args.cookie:
        fetcher.update_cookies(args.cookie)

    # 查询模式
    if args.query:
        logger.info("🔍 查询数据库中的邮件历史数据...")
        results = fetcher.query_data(args.limit)

        if results:
            logger.info(f"📋 查询到 {len(results)} 条记录:")
            for i, record in enumerate(results, 1):
                logger.info(f"  {i}. {record['notice_name']} ({record['project_code']})")
                logger.info(f"     类型: {record['select_type_value']}")
                logger.info(f"     发布时间: {record['real_publish_time']}")
                logger.info(f"     状态: {record['send_status_value']}")
                logger.info(f"     入库: {record['created_at']}")
                logger.info("")
        else:
            logger.info("📋 没有查询到数据")
        return

    # 轮询所有项目模式
    if args.all:
        logger.info("🔄 轮询所有项目模式...")
        stats = fetcher.fetch_and_save_all()

        if stats['success'] > 0:
            logger.info(f"✅ 轮询完成，成功处理{stats['success']}个项目")
        else:
            logger.error("❌ 轮询失败，没有成功处理任何项目")
        return

    # 单个项目模式
    if args.select_msg_id and args.project_code:
        logger.info(f"🎯 处理单个项目: selectMsgId={args.select_msg_id}, projectCode={args.project_code}")

        # 获取邮件历史数据
        raw_data = fetcher.fetch_notice_history(args.select_msg_id, args.project_code)

        if raw_data:
            # 转换数据格式（这里project_no使用project_code）
            transformed_data = fetcher.transform_data(raw_data, args.select_msg_id, args.project_code, args.project_code)

            if transformed_data:
                if fetcher.save_notice_history(transformed_data):
                    logger.info("✅ 单个项目处理成功")
                else:
                    logger.error("❌ 单个项目保存失败")
            else:
                logger.error("❌ 单个项目数据转换失败")
        else:
            logger.error("❌ 单个项目获取失败")
        return

    # 如果没有指定参数，显示帮助信息
    parser.print_help()


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("🛑 程序被用户中断")
    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        sys.exit(1)
