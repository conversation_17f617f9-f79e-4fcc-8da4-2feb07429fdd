#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证甄选项目数据
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import DatabaseManager, ZHENXUAN_DB_CONFIG

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def verify_data():
    """验证数据"""
    
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    if not db_manager.connect():
        logger.error("❌ 数据库连接失败")
        return False
    
    try:
        logger.info("🔍 开始验证数据...")
        
        with db_manager.get_cursor() as cursor:
            # 1. 统计总记录数
            cursor.execute("SELECT COUNT(*) as total_records FROM zhenxuan_querySelectProjectList")
            total_result = cursor.fetchone()
            total_records = total_result['total_records']
            logger.info(f"📊 总记录数: {total_records}")
            
            # 2. 统计唯一项目数
            cursor.execute("SELECT COUNT(DISTINCT project_msg_id) as unique_projects FROM zhenxuan_querySelectProjectList")
            unique_result = cursor.fetchone()
            unique_projects = unique_result['unique_projects']
            logger.info(f"📊 唯一项目数: {unique_projects}")
            
            # 3. 最后同步时间
            cursor.execute("SELECT MAX(created_at) as last_sync FROM zhenxuan_querySelectProjectList")
            sync_result = cursor.fetchone()
            last_sync = sync_result['last_sync']
            logger.info(f"📊 最后同步时间: {last_sync}")
            
            # 4. 按状态统计
            cursor.execute("""
                SELECT select_status_value, COUNT(*) as count 
                FROM zhenxuan_querySelectProjectList 
                GROUP BY select_status_value 
                ORDER BY count DESC
            """)
            status_stats = cursor.fetchall()
            logger.info("📊 按状态统计:")
            for stat in status_stats:
                logger.info(f"  - {stat['select_status_value']}: {stat['count']} 条")
            
            # 5. 按区域统计（前5名）
            cursor.execute("""
                SELECT business_area_value, COUNT(*) as count 
                FROM zhenxuan_querySelectProjectList 
                GROUP BY business_area_value 
                ORDER BY count DESC 
                LIMIT 5
            """)
            area_stats = cursor.fetchall()
            logger.info("📊 按区域统计（前5名）:")
            for stat in area_stats:
                logger.info(f"  - {stat['business_area_value']}: {stat['count']} 条")
            
            # 6. 检查selectRevId字段
            cursor.execute("""
                SELECT select_rev_id, COUNT(*) as count 
                FROM zhenxuan_querySelectProjectList 
                WHERE select_rev_id IS NOT NULL
                GROUP BY select_rev_id
            """)
            rev_stats = cursor.fetchall()
            if rev_stats:
                logger.info("📊 甄选版本ID统计:")
                for stat in rev_stats:
                    logger.info(f"  - {stat['select_rev_id']}: {stat['count']} 条")
            else:
                logger.info("📊 暂无甄选版本ID数据")
            
            # 7. 最新5条记录
            cursor.execute("""
                SELECT project_name, project_no, select_status_value, create_time, created_at
                FROM zhenxuan_querySelectProjectList 
                ORDER BY created_at DESC 
                LIMIT 5
            """)
            latest_records = cursor.fetchall()
            logger.info("📊 最新5条记录:")
            for i, record in enumerate(latest_records, 1):
                logger.info(f"  {i}. {record['project_name'][:30]}...")
                logger.info(f"     项目编号: {record['project_no']}")
                logger.info(f"     状态: {record['select_status_value']}")
                logger.info(f"     创建时间: {record['create_time']}")
                logger.info(f"     入库时间: {record['created_at']}")
                logger.info("")
            
            # 8. 数据完整性检查
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    COUNT(project_msg_id) as has_project_id,
                    COUNT(project_name) as has_project_name,
                    COUNT(project_no) as has_project_no,
                    COUNT(raw_data) as has_raw_data
                FROM zhenxuan_querySelectProjectList
            """)
            integrity_result = cursor.fetchone()
            logger.info("📊 数据完整性检查:")
            logger.info(f"  - 总记录数: {integrity_result['total']}")
            logger.info(f"  - 有项目ID: {integrity_result['has_project_id']}")
            logger.info(f"  - 有项目名称: {integrity_result['has_project_name']}")
            logger.info(f"  - 有项目编号: {integrity_result['has_project_no']}")
            logger.info(f"  - 有原始数据: {integrity_result['has_raw_data']}")
            
            # 9. 表结构验证
            cursor.execute("DESCRIBE zhenxuan_querySelectProjectList")
            columns = cursor.fetchall()
            logger.info(f"📊 表结构: {len(columns)} 个字段")
            
            # 检查关键字段
            key_fields = ['select_rev_id', 'project_msg_id', 'project_name', 'raw_data']
            existing_fields = [col['Field'] for col in columns]
            
            for field in key_fields:
                if field in existing_fields:
                    logger.info(f"  ✅ 关键字段 '{field}' 存在")
                else:
                    logger.error(f"  ❌ 关键字段 '{field}' 缺失")
            
            logger.info("🎉 数据验证完成！")
            return True
            
    except Exception as e:
        logger.error(f"❌ 数据验证失败: {e}")
        return False
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    success = verify_data()
    if success:
        logger.info("✅ 数据验证通过！")
    else:
        logger.error("❌ 数据验证失败！")
        sys.exit(1)
