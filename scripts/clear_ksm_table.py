"""
清空KSM表数据
"""

import os
import sys
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from database.db_config import ZHENXUAN_DB_CONFIG, DatabaseManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clear_ksm_table():
    """清空KSM表数据"""
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    try:
        if not db_manager.connect():
            logger.error("❌ 数据库连接失败")
            return False
        
        with db_manager.get_cursor() as cursor:
            # 查询当前记录数
            cursor.execute("SELECT COUNT(*) as count FROM zhenxuan_queryLocalAuditTrackHistory_ksm")
            result = cursor.fetchone()
            current_count = result['count']
            
            logger.info(f"📊 当前表中有 {current_count} 条记录")
            
            if current_count > 0:
                # 清空表数据
                logger.info("🗑️ 开始清空表数据...")
                cursor.execute("TRUNCATE TABLE zhenxuan_queryLocalAuditTrackHistory_ksm")
                db_manager.connection.commit()
                
                # 验证清空结果
                cursor.execute("SELECT COUNT(*) as count FROM zhenxuan_queryLocalAuditTrackHistory_ksm")
                result = cursor.fetchone()
                after_count = result['count']
                
                logger.info(f"✅ 表数据清空成功！清空前: {current_count} 条，清空后: {after_count} 条")
            else:
                logger.info("📋 表中没有数据，无需清空")
            
            return True
                
    except Exception as e:
        logger.error(f"❌ 清空表数据失败: {e}")
        return False
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    logger.info("🚀 开始清空KSM表数据...")
    if clear_ksm_table():
        logger.info("✅ 清空操作完成")
    else:
        logger.error("❌ 清空操作失败")
