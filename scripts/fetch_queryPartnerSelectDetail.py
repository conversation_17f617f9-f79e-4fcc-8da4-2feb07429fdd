#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
甄选合作伙伴详情数据获取脚本
从 queryPartnerSelectDetail 接口获取数据并入库

功能：
1. 从 zhenxuan_querySelectProjectList 表获取 selectMsgId
2. 使用 cookie 认证访问 API 接口
3. 获取合作伙伴详情数据并入库到 zhenxuan_queryPartnerSelectDetail 表
4. 支持轮询模式和单次获取模式

作者: RIPER Agent
创建时间: 2025-07-09
"""

import os
import sys
import json
import time
import logging
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional
import urllib3

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import get_db_manager
from auth_loader import AuthLoader

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/fetch_queryPartnerSelectDetail.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PartnerSelectDetailFetcher:
    """甄选合作伙伴详情数据获取器"""
    
    def __init__(self, cookie_file_path=None):
        """初始化获取器"""
        self.base_url = "https://dict.gmcc.net:30722"
        self.api_endpoint = "/partner/materialManage/pnrSelect/queryPartnerSelectDetail"
        self.cookie_file = "cookies/cookies_dict_zhenxuan.json"
        self.db_manager = get_db_manager()
        self.session = requests.Session()
        # 使用 AuthLoader 加载认证信息
        auth_loader = AuthLoader(cookie_file_path)
        if auth_loader.load_auth_data():
            # 更新session的认证信息
            auth_loader.update_session(self.session)
            logger.info("✅ 认证信息加载成功")
        else:
            logger.error("❌ 认证信息加载失败")
            
        # 设置基本请求头（AuthLoader会自动添加认证相关的headers）
        self.session.headers.update({
            'Host': "dict.gmcc.net:30722",
            'Origin': "http://dict.gmcc.net:30722",
            'Referer': "http://dict.gmcc.net:30722/ptn/main/selectDemand",
        })
        self.session.verify = False  # 忽略SSL证书验证
        
        # 设置请求头
        self.session.headers.update({
            'Host': 'dict.gmcc.net:30722',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'sec-ch-ua-platform': '"Windows"',
            'Authorization': 'Bearer d25c514c-e026-4ddf-b455-9929dfcd3cfb',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://dict.gmcc.net:30722/ptn/main/selectDemand/detail',
            'Accept-Language': 'zh-CN,zh;q=0.9,ee;q=0.8'
        })
        
        self._load_cookies()
    
    def _load_cookies(self) -> bool:
        """
        加载cookie文件并设置到session
        
        Returns:
            bool: 加载是否成功
        """
        try:
            if not os.path.exists(self.cookie_file):
                logger.error(f"❌ Cookie文件不存在: {self.cookie_file}")
                return False
            
            with open(self.cookie_file, 'r', encoding='utf-8') as f:
                cookies_data = json.load(f)
            
            # 转换cookie格式：从JSON数组转换为HTTP头字符串
            cookie_pairs = []
            for cookie in cookies_data:
                name = cookie.get('name', '')
                value = cookie.get('value', '')
                if name and value:
                    cookie_pairs.append(f"{name}={value}")
            
            cookie_string = '; '.join(cookie_pairs)
            self.session.headers['Cookie'] = cookie_string
            
            logger.info(f"✅ 成功加载 {len(cookies_data)} 个cookie")
            logger.debug(f"Cookie字符串: {cookie_string[:100]}...")
            return True
            
        except Exception as e:
            logger.error(f"❌ 加载cookie失败: {e}")
            return False
    
    def get_select_msg_ids(self) -> List[str]:
        """
        从 zhenxuan_querySelectProjectList 表获取所有 selectMsgId

        Returns:
            List[str]: selectMsgId列表
        """
        try:
            sql = """
            SELECT DISTINCT select_msg_id, created_at
            FROM zhenxuan_querySelectProjectList
            WHERE select_msg_id IS NOT NULL
            AND select_msg_id != ''
            ORDER BY created_at DESC
            """

            results = self.db_manager.execute_query(sql)
            select_msg_ids = [row['select_msg_id'] for row in results]

            logger.info(f"✅ 从数据库获取到 {len(select_msg_ids)} 个 selectMsgId")
            return select_msg_ids

        except Exception as e:
            logger.error(f"❌ 获取selectMsgId失败: {e}")
            return []
    
    def fetch_partner_detail(self, select_rev_id: str) -> Optional[Dict[str, Any]]:
        """
        获取指定selectRevId的合作伙伴详情数据
        
        Args:
            select_rev_id: 甄选版本ID
            
        Returns:
            Optional[Dict]: API响应数据，失败返回None
        """
        try:
            url = f"{self.base_url}{self.api_endpoint}"
            params = {'selectRevId': select_rev_id}
            
            logger.info(f"🔄 正在获取合作伙伴详情: selectRevId={select_rev_id}")
            
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # 检查响应状态
            if data.get('code') == '000000':
                logger.info(f"✅ 成功获取合作伙伴详情: {select_rev_id}")
                return data
            else:
                logger.warning(f"⚠️ API返回错误: {data.get('message', '未知错误')}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON解析失败: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ 获取数据失败: {e}")
            return None
    
    def save_partner_detail(self, data: Dict[str, Any], select_rev_id: str) -> bool:
        """
        保存合作伙伴详情数据到数据库
        
        Args:
            data: API响应数据
            select_rev_id: 甄选版本ID
            
        Returns:
            bool: 保存是否成功
        """
        try:
            result_body = data.get('resultBody', {})
            
            # 构建插入数据
            insert_data = {
                # 请求参数
                'select_rev_id': select_rev_id,
                'request_params': json.dumps({'selectRevId': select_rev_id}),
                
                # 响应基础信息
                'busi_date': self._parse_datetime(data.get('busiDate')),
                'code': data.get('code'),
                'message': data.get('message'),
                
                # 核心项目信息
                'project_msg_id': result_body.get('projectMsgId'),
                'project_name': result_body.get('projectName'),
                'project_code': result_body.get('projectCode'),
                'project_no': result_body.get('projectNo'),
                'iproject_id': result_body.get('iprojectId'),
                'group_project_code': result_body.get('groupProjectCode'),
                
                # 项目类型和分类
                'project_type': result_body.get('projectType'),
                'project_type_value': result_body.get('projectTypeValue'),
                'project_label': result_body.get('projectLabel'),
                'project_label_value': result_body.get('projectLabelValue'),
                
                # 甄选相关信息 - 重点：selectMsgId字段
                'select_msg_id': result_body.get('selectMsgId'),
                'select_name': result_body.get('selectName'),
                'select_status': result_body.get('selectStatus'),
                'select_status_value': result_body.get('selectStatusValue'),
                'select_type': result_body.get('selectType'),
                'select_type_value': result_body.get('selectTypeValue'),
                'select_category': result_body.get('selectCategory'),
                'select_category_value': result_body.get('selectCategoryValue'),
                
                # 通知相关
                'select_notice_type': result_body.get('selectNoticeType'),
                'notice_id': result_body.get('noticeId'),
                
                # 行业信息
                'industry': result_body.get('industry'),
                'industry_value': result_body.get('industryValue'),
                'select_industry': result_body.get('selectIndustry'),
                'select_industry_value': result_body.get('selectIndustryValue'),
                
                # 管理标识
                'is_pmo_manage': result_body.get('isPmoManage'),
                'is_pmo_manage_value': result_body.get('isPmoManageValue'),
                'is_sub_sign': result_body.get('isSubSign'),
                'is_sub_sign_value': result_body.get('isSubSignValue'),
                'is_investment_project': result_body.get('isInvestmentProject'),
                'is_investment_project_value': result_body.get('isInvestmentProjectValue'),
                'is_fixed_softness': result_body.get('isFixedSoftness'),
                'is_fixed_softness_value': result_body.get('isFixedSoftnessValue'),
                
                # 工程项目
                'engineering_project_code': result_body.get('engineeringProjectCode'),
                
                # 时间信息
                'start_time': self._parse_date(result_body.get('startTime')),
                'end_time': self._parse_date(result_body.get('endTime')),
                
                # 部门和人员
                'initiate_department': result_body.get('initiateDepartment'),
                'create_staff': result_body.get('createStaff'),
                'plan_create_staff': result_body.get('planCreateStaff'),
                'next_todo_handler': result_body.get('nextTodoHandler'),
                'next_todo_handler_value': result_body.get('nextTodoHandlerValue'),
                
                # 客户信息
                'customer_name': result_body.get('customerName'),
                
                # 场景信息
                'first_scene': result_body.get('firstScene'),
                'first_scene_value': result_body.get('firstSceneValue'),
                'second_scene': result_body.get('secondScene'),
                'second_scene_value': result_body.get('secondSceneValue'),
                
                # 需求内容
                'select_demand_content': result_body.get('selectDemandContent'),
                
                # 预算信息
                'select_budget': self._parse_decimal(result_body.get('selectBudget')),
                'non_tax_select_budget': self._parse_decimal(result_body.get('nonTaxSelectBudget')),
                
                # 业务区域
                'business_area': result_body.get('businessArea'),
                'business_area_value': result_body.get('businessAreaValue'),
                
                # 甄选版本信息
                'select_rev_id_detail': result_body.get('selectRevId'),
                'select_rev_name': result_body.get('selectRevName'),
                'select_rev_describe': result_body.get('selectRevDescribe'),
                
                # 联系信息
                'operator_contact': result_body.get('operatorContact'),
                'operator_contact_phone': result_body.get('operatorContactPhone'),
                'operator_contact_email': result_body.get('operatorContactEmail'),
                
                # 文件ID
                'file_id': result_body.get('fileId'),
                'file_id1': result_body.get('fileId1'),
                'file_id2': result_body.get('fileId2'),
                'file_id3': result_body.get('fileId3'),
                
                # 工单信息
                'work_order_msg_id': result_body.get('workOrderMsgId'),
                
                # 其他字段
                'phone': result_body.get('phone'),
                'employee_name': result_body.get('employeeName'),
                'email': result_body.get('email'),
                'select_time': self._parse_datetime(result_body.get('selectTime')),
                'select_address': result_body.get('selectAddress'),
                
                # 终止相关
                'termination_reason': result_body.get('terminationReason'),
                'other_explain': result_body.get('otherExplain'),
                'termination_explain': result_body.get('terminationExplain'),
                'shutdown_basis': result_body.get('shutdownBasis'),
                
                # 审核相关
                'next_audit_handler': result_body.get('nextAuditHandler'),
                'current_audit_step': result_body.get('currentAuditStep'),
                
                # 产品会议
                'product_meeting_basis': result_body.get('productMeetingBasis'),
                'product_meeting_basis2': result_body.get('productMeetingBasis2'),
                
                # 评审状态
                'select_review_status': result_body.get('selectReviewStatus'),
                'pass_time': self._parse_datetime(result_body.get('passTime')),
                
                # 需求类型
                'select_demand_type': result_body.get('selectDemandType'),
                'select_demand_type_value': result_body.get('selectDemandTypeValue'),
                
                # 在线申请
                'is_online_apply': result_body.get('isOnlineApply'),
                
                # 甄选标题和内容
                'select_title': result_body.get('selectTitle'),
                'select_content': result_body.get('selectContent'),
                
                # 审核团队
                'audit_team_ids': result_body.get('auditTeamIds'),
                'audit_team_handler': result_body.get('auditTeamHandler'),
                
                # 文档编号
                'document_no': result_body.get('documentNo'),
                'doc_number_sub': result_body.get('docNumberSub'),
                
                # T解决方案
                'is_t_solution': result_body.get('isTSolution'),
                
                # 甄选级别
                'select_level': result_body.get('selectLevel'),
                
                # 推送通知
                'push_notice': result_body.get('pushNotice'),
                
                # 创建年份
                'create_year': result_body.get('createYear'),
                
                # 评审团队
                'review_team_msg_id': result_body.get('reviewTeamMsgId'),
                
                # 甄选依据
                'select_basis': result_body.get('selectBasis'),
                'select_basis2': result_body.get('selectBasis2'),
                'select_basis3': result_body.get('selectBasis3'),
                
                # 原始数据
                'raw_data': json.dumps(data, ensure_ascii=False)
            }
            
            # 执行插入或更新
            sql = """
            INSERT INTO zhenxuan_queryPartnerSelectDetail (
                select_rev_id, request_params, busi_date, code, message,
                project_msg_id, project_name, project_code, project_no, iproject_id, group_project_code,
                project_type, project_type_value, project_label, project_label_value,
                select_msg_id, select_name, select_status, select_status_value, select_type, select_type_value,
                select_category, select_category_value, select_notice_type, notice_id,
                industry, industry_value, select_industry, select_industry_value,
                is_pmo_manage, is_pmo_manage_value, is_sub_sign, is_sub_sign_value,
                is_investment_project, is_investment_project_value, is_fixed_softness, is_fixed_softness_value,
                engineering_project_code, start_time, end_time,
                initiate_department, create_staff, plan_create_staff, next_todo_handler, next_todo_handler_value,
                customer_name, first_scene, first_scene_value, second_scene, second_scene_value,
                select_demand_content, select_budget, non_tax_select_budget,
                business_area, business_area_value, select_rev_id_detail, select_rev_name, select_rev_describe,
                operator_contact, operator_contact_phone, operator_contact_email,
                file_id, file_id1, file_id2, file_id3, work_order_msg_id,
                phone, employee_name, email, select_time, select_address,
                termination_reason, other_explain, termination_explain, shutdown_basis,
                next_audit_handler, current_audit_step, product_meeting_basis, product_meeting_basis2,
                select_review_status, pass_time, select_demand_type, select_demand_type_value,
                is_online_apply, select_title, select_content, audit_team_ids, audit_team_handler,
                document_no, doc_number_sub, is_t_solution, select_level, push_notice,
                create_year, review_team_msg_id, select_basis, select_basis2, select_basis3, raw_data
            ) VALUES (
                %(select_rev_id)s, %(request_params)s, %(busi_date)s, %(code)s, %(message)s,
                %(project_msg_id)s, %(project_name)s, %(project_code)s, %(project_no)s, %(iproject_id)s, %(group_project_code)s,
                %(project_type)s, %(project_type_value)s, %(project_label)s, %(project_label_value)s,
                %(select_msg_id)s, %(select_name)s, %(select_status)s, %(select_status_value)s, %(select_type)s, %(select_type_value)s,
                %(select_category)s, %(select_category_value)s, %(select_notice_type)s, %(notice_id)s,
                %(industry)s, %(industry_value)s, %(select_industry)s, %(select_industry_value)s,
                %(is_pmo_manage)s, %(is_pmo_manage_value)s, %(is_sub_sign)s, %(is_sub_sign_value)s,
                %(is_investment_project)s, %(is_investment_project_value)s, %(is_fixed_softness)s, %(is_fixed_softness_value)s,
                %(engineering_project_code)s, %(start_time)s, %(end_time)s,
                %(initiate_department)s, %(create_staff)s, %(plan_create_staff)s, %(next_todo_handler)s, %(next_todo_handler_value)s,
                %(customer_name)s, %(first_scene)s, %(first_scene_value)s, %(second_scene)s, %(second_scene_value)s,
                %(select_demand_content)s, %(select_budget)s, %(non_tax_select_budget)s,
                %(business_area)s, %(business_area_value)s, %(select_rev_id_detail)s, %(select_rev_name)s, %(select_rev_describe)s,
                %(operator_contact)s, %(operator_contact_phone)s, %(operator_contact_email)s,
                %(file_id)s, %(file_id1)s, %(file_id2)s, %(file_id3)s, %(work_order_msg_id)s,
                %(phone)s, %(employee_name)s, %(email)s, %(select_time)s, %(select_address)s,
                %(termination_reason)s, %(other_explain)s, %(termination_explain)s, %(shutdown_basis)s,
                %(next_audit_handler)s, %(current_audit_step)s, %(product_meeting_basis)s, %(product_meeting_basis2)s,
                %(select_review_status)s, %(pass_time)s, %(select_demand_type)s, %(select_demand_type_value)s,
                %(is_online_apply)s, %(select_title)s, %(select_content)s, %(audit_team_ids)s, %(audit_team_handler)s,
                %(document_no)s, %(doc_number_sub)s, %(is_t_solution)s, %(select_level)s, %(push_notice)s,
                %(create_year)s, %(review_team_msg_id)s, %(select_basis)s, %(select_basis2)s, %(select_basis3)s, %(raw_data)s
            ) ON DUPLICATE KEY UPDATE
                select_name = VALUES(select_name),
                select_status = VALUES(select_status),
                select_status_value = VALUES(select_status_value),
                customer_name = VALUES(customer_name),
                select_budget = VALUES(select_budget),
                raw_data = VALUES(raw_data),
                updated_at = CURRENT_TIMESTAMP
            """
            
            affected_rows = self.db_manager.execute_update(sql, insert_data)
            
            if affected_rows > 0:
                logger.info(f"✅ 成功保存合作伙伴详情: {result_body.get('projectName', 'Unknown')}")
                return True
            else:
                logger.warning(f"⚠️ 数据未发生变化: {select_rev_id}")
                return True
                
        except Exception as e:
            logger.error(f"❌ 保存数据失败: {e}")
            return False
    
    def _parse_datetime(self, date_str: str) -> Optional[str]:
        """解析日期时间字符串"""
        if not date_str:
            return None
        try:
            # 尝试解析不同格式的日期时间
            for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d']:
                try:
                    dt = datetime.strptime(date_str, fmt)
                    return dt.strftime('%Y-%m-%d %H:%M:%S')
                except ValueError:
                    continue
            return date_str
        except:
            return None
    
    def _parse_date(self, date_str: str) -> Optional[str]:
        """解析日期字符串"""
        if not date_str:
            return None
        try:
            dt = datetime.strptime(date_str, '%Y-%m-%d')
            return dt.strftime('%Y-%m-%d')
        except:
            return None
    
    def _parse_decimal(self, value: Any) -> Optional[float]:
        """解析数字字符串"""
        if value is None:
            return None
        try:
            return float(value)
        except:
            return None
    
    def fetch_all_partner_details(self, limit: int = None) -> Dict[str, int]:
        """
        获取所有合作伙伴详情数据
        
        Args:
            limit: 限制处理数量，None表示处理所有
            
        Returns:
            Dict[str, int]: 统计信息
        """
        stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
        
        try:
            # 获取所有selectMsgId
            select_msg_ids = self.get_select_msg_ids()
            
            if not select_msg_ids:
                logger.warning("⚠️ 未找到任何selectMsgId")
                return stats
            
            # 应用限制
            if limit:
                select_msg_ids = select_msg_ids[:limit]
            
            stats['total'] = len(select_msg_ids)
            logger.info(f"🚀 开始处理 {stats['total']} 个合作伙伴详情")
            
            for i, select_msg_id in enumerate(select_msg_ids, 1):
                logger.info(f"📋 处理进度: {i}/{stats['total']} - selectMsgId: {select_msg_id}")
                
                # 检查是否已存在
                existing_sql = "SELECT id FROM zhenxuan_queryPartnerSelectDetail WHERE select_rev_id = %s"
                existing = self.db_manager.execute_query(existing_sql, (select_msg_id,))
                
                if existing:
                    logger.info(f"⏭️ 数据已存在，跳过: {select_msg_id}")
                    stats['skipped'] += 1
                    continue
                
                # 获取数据
                data = self.fetch_partner_detail(select_msg_id)
                
                if data:
                    # 保存数据
                    if self.save_partner_detail(data, select_msg_id):
                        stats['success'] += 1
                    else:
                        stats['failed'] += 1
                else:
                    stats['failed'] += 1
                
                # 添加延迟避免请求过快
                time.sleep(1)
            
            logger.info(f"🎉 处理完成! 总计: {stats['total']}, 成功: {stats['success']}, 失败: {stats['failed']}, 跳过: {stats['skipped']}")
            
        except Exception as e:
            logger.error(f"❌ 批量处理失败: {e}")
        
        return stats

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='甄选合作伙伴详情数据获取脚本')
    parser.add_argument('--select-rev-id', type=str, help='指定selectRevId获取单个详情')
    parser.add_argument('--all', action='store_true', help='获取所有合作伙伴详情')
    parser.add_argument('--limit', type=int, help='限制处理数量')
    
    args = parser.parse_args()
    
    # 确保日志目录存在
    os.makedirs('logs', exist_ok=True)
    
    fetcher = PartnerSelectDetailFetcher()
    
    if args.select_rev_id:
        # 获取单个详情
        logger.info(f"🎯 获取单个合作伙伴详情: {args.select_rev_id}")
        data = fetcher.fetch_partner_detail(args.select_rev_id)
        if data:
            if fetcher.save_partner_detail(data, args.select_rev_id):
                logger.info("✅ 单个详情获取成功")
            else:
                logger.error("❌ 单个详情保存失败")
        else:
            logger.error("❌ 单个详情获取失败")
    
    elif args.all:
        # 获取所有详情
        logger.info("🚀 开始获取所有合作伙伴详情")
        stats = fetcher.fetch_all_partner_details(args.limit)
        logger.info(f"📊 最终统计: {stats}")
    
    else:
        # 默认获取所有详情
        logger.info("🚀 开始获取所有合作伙伴详情（默认模式）")
        stats = fetcher.fetch_all_partner_details()
        logger.info(f"📊 最终统计: {stats}")

if __name__ == '__main__':
    main()
