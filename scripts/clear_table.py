#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理表数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import get_db_manager

def clear_table():
    db = get_db_manager()
    db.connect()

    # 清理数据
    affected_rows = db.execute_update('DELETE FROM zhenxuan_querypartnerselectdetail')
    print(f'已清理 {affected_rows} 条测试数据')

    # 重置自增ID
    db.execute_update('ALTER TABLE zhenxuan_querypartnerselectdetail AUTO_INCREMENT = 1')
    print('已重置自增ID')

    # 验证清理结果
    results = db.execute_query('SELECT COUNT(*) as count FROM zhenxuan_querypartnerselectdetail')
    count = results[0]['count']
    print(f'清理后记录数: {count}')

if __name__ == '__main__':
    clear_table()
