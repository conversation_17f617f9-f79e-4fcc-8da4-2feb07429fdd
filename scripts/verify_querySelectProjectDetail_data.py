#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证甄选项目详情数据
检查数据是否正确保存到数据库

作者: RIPER系统
创建时间: 2025-07-08
"""

import sys
import os
import pymysql

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import get_db_manager

def verify_data():
    """验证数据"""
    
    try:
        db_manager = get_db_manager(use_zhenxuan=True)
        
        if not db_manager.connect():
            print("❌ 数据库连接失败")
            return False
        
        with db_manager.get_connection() as conn:
            with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                # 检查数据总数
                cursor.execute("SELECT COUNT(*) as total FROM zhenxuan_querySelectProjectDetail")
                total_result = cursor.fetchone()
                print(f"📊 数据总数: {total_result['total']}")
                
                # 检查最新的5条数据
                cursor.execute("""
                SELECT 
                    project_msg_id, 
                    project_name, 
                    select_status_value, 
                    customer_name,
                    business_area_value,
                    create_time,
                    created_at
                FROM zhenxuan_querySelectProjectDetail 
                ORDER BY created_at DESC 
                LIMIT 5
                """)
                
                results = cursor.fetchall()
                
                print("\n📋 最新5条数据:")
                print("-" * 120)
                print(f"{'项目ID':<20} {'项目名称':<30} {'状态':<15} {'客户名称':<25} {'地区':<8} {'创建时间':<20}")
                print("-" * 120)
                
                for r in results:
                    project_name = (r['project_name'][:27] + '...') if r['project_name'] and len(r['project_name']) > 30 else (r['project_name'] or 'N/A')
                    customer_name = (r['customer_name'][:22] + '...') if r['customer_name'] and len(r['customer_name']) > 25 else (r['customer_name'] or 'N/A')
                    
                    print(f"{r['project_msg_id']:<20} {project_name:<30} {r['select_status_value'] or 'N/A':<15} {customer_name:<25} {r['business_area_value'] or 'N/A':<8} {str(r['create_time']) or 'N/A':<20}")
                
                print("-" * 120)
                
                # 检查字段完整性
                cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    COUNT(project_name) as has_project_name,
                    COUNT(customer_name) as has_customer_name,
                    COUNT(select_budget) as has_budget,
                    COUNT(raw_data) as has_raw_data
                FROM zhenxuan_querySelectProjectDetail
                """)
                
                completeness = cursor.fetchone()
                print(f"\n📈 数据完整性统计:")
                print(f"   - 总记录数: {completeness['total']}")
                print(f"   - 有项目名称: {completeness['has_project_name']} ({completeness['has_project_name']/completeness['total']*100:.1f}%)")
                print(f"   - 有客户名称: {completeness['has_customer_name']} ({completeness['has_customer_name']/completeness['total']*100:.1f}%)")
                print(f"   - 有预算信息: {completeness['has_budget']} ({completeness['has_budget']/completeness['total']*100:.1f}%)")
                print(f"   - 有原始数据: {completeness['has_raw_data']} ({completeness['has_raw_data']/completeness['total']*100:.1f}%)")
                
                # 检查状态分布
                cursor.execute("""
                SELECT 
                    select_status_value,
                    COUNT(*) as count
                FROM zhenxuan_querySelectProjectDetail 
                WHERE select_status_value IS NOT NULL
                GROUP BY select_status_value
                ORDER BY count DESC
                """)
                
                status_results = cursor.fetchall()
                print(f"\n📊 状态分布:")
                for status in status_results:
                    print(f"   - {status['select_status_value']}: {status['count']}个")
                
        print("\n✅ 数据验证完成！")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    verify_data()
