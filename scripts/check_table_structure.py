"""
检查数据库表结构
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from database.db_config import ZHENXUAN_DB_CONFIG, DatabaseManager

def check_table_structure():
    """检查表结构"""
    
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    if not db_manager.connect():
        print("❌ 数据库连接失败")
        return
    
    try:
        with db_manager.get_connection() as conn:
            with conn.cursor() as cursor:
                # 检查所有表
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()

                print("📊 数据库中的表:")
                for table in tables:
                    print(f"   - {table[0]}")

                # 检查zhenxuan_querySelectStage表结构
                print("\n🔍 zhenxuan_querySelectStage表结构:")
                try:
                    cursor.execute("DESCRIBE zhenxuan_querySelectStage")
                    columns = cursor.fetchall()

                    for column in columns:
                        print(f"   {column[0]} - {column[1]} - {column[2]}")
                except Exception as e:
                    print(f"   表不存在或查询失败: {e}")

                # 检查是否有包含business_id的表
                print("\n🔍 查找包含business_id字段的表:")
                cursor.execute("""
                    SELECT TABLE_NAME, COLUMN_NAME
                    FROM information_schema.COLUMNS
                    WHERE TABLE_SCHEMA = 'zhenxuandb'
                    AND COLUMN_NAME LIKE '%business%'
                """)
                business_columns = cursor.fetchall()

                if business_columns:
                    for table_name, column_name in business_columns:
                        print(f"   {table_name}.{column_name}")
                else:
                    print("   没有找到包含business_id的表")
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    check_table_structure()
