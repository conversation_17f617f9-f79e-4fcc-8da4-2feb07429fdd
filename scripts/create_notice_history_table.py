#!/usr/bin/env python3
"""
创建甄选邮件信息历史查询数据表
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from database.db_config import get_db_manager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_table():
    """创建数据表"""
    
    # 读取SQL文件
    sql_file = project_root / "database" / "create_zhenxuan_queryNoticeHistoryBySelectId.sql"
    
    if not sql_file.exists():
        logger.error(f"❌ SQL文件不存在: {sql_file}")
        return False
    
    with open(sql_file, 'r', encoding='utf-8') as f:
        sql_content = f.read()
    
    # 分割SQL语句
    sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
    
    # 连接数据库
    db_manager = get_db_manager(use_zhenxuan=True)
    if not db_manager.connect():
        logger.error("❌ 数据库连接失败")
        return False
    
    try:
        with db_manager.get_cursor() as cursor:
            for i, sql_stmt in enumerate(sql_statements, 1):
                if sql_stmt.strip():
                    try:
                        logger.info(f"📝 执行SQL语句 {i}/{len(sql_statements)}")
                        cursor.execute(sql_stmt)
                        logger.info(f"✅ SQL语句 {i} 执行成功")
                    except Exception as e:
                        logger.error(f"❌ SQL语句 {i} 执行失败: {e}")
                        logger.error(f"SQL内容: {sql_stmt[:100]}...")
                        continue
        
        db_manager.connection.commit()
        logger.info("🎉 数据表创建完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建表失败: {e}")
        return False
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    create_table()
