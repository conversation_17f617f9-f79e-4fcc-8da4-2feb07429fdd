#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建甄选项目详情查询数据表
根据 querySelectProjectDetail 接口的JSON数据结构创建对应的MySQL表

功能：
1. 创建 zhenxuan_querySelectProjectDetail 数据表
2. 验证表结构和索引
3. 创建相关视图

作者: RIPER系统
创建时间: 2025-07-08
"""

import sys
import os
import logging
import pymysql
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import get_db_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/create_querySelectProjectDetail_table.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_table():
    """创建甄选项目详情查询数据表"""
    
    # 读取SQL文件
    sql_file = 'database/create_zhenxuan_querySelectProjectDetail.sql'
    
    if not os.path.exists(sql_file):
        logger.error(f"❌ SQL文件不存在: {sql_file}")
        return False
    
    try:
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 获取数据库管理器
        db_manager = get_db_manager(use_zhenxuan=True)
        
        if not db_manager.connect():
            logger.error("❌ 数据库连接失败")
            return False
        
        logger.info("🚀 开始创建甄选项目详情查询数据表...")
        
        # 分割SQL语句（按分号分割）
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        with db_manager.get_connection() as conn:
            with conn.cursor() as cursor:
                for i, sql_stmt in enumerate(sql_statements, 1):
                    try:
                        if sql_stmt.upper().startswith('USE'):
                            # 跳过USE语句，因为已经连接到指定数据库
                            continue
                        
                        logger.info(f"📝 执行SQL语句 {i}/{len(sql_statements)}")
                        logger.debug(f"SQL: {sql_stmt[:100]}...")
                        
                        cursor.execute(sql_stmt)
                        conn.commit()
                        
                        logger.info(f"✅ SQL语句 {i} 执行成功")
                        
                    except Exception as e:
                        logger.error(f"❌ SQL语句 {i} 执行失败: {e}")
                        logger.debug(f"失败的SQL: {sql_stmt}")
                        # 继续执行其他语句
                        continue
        
        logger.info("🎉 甄选项目详情查询数据表创建完成！")
        
        # 验证表创建
        verify_table_creation(db_manager)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建表失败: {e}")
        return False

def verify_table_creation(db_manager):
    """验证表创建结果"""
    
    try:
        logger.info("🔍 验证表创建结果...")
        
        # 检查表是否存在
        sql_check_table = """
        SELECT 
            TABLE_NAME,
            TABLE_COMMENT,
            TABLE_COLLATION,
            ENGINE
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = 'zhenxuandb' 
        AND TABLE_NAME = 'zhenxuan_querySelectProjectDetail'
        """
        
        with db_manager.get_connection() as conn:
            with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql_check_table)
                table_info = cursor.fetchone()
                
                if table_info:
                    logger.info(f"✅ 表创建成功: {table_info['TABLE_NAME']}")
                    logger.info(f"   - 表注释: {table_info['TABLE_COMMENT']}")
                    logger.info(f"   - 字符集: {table_info['TABLE_COLLATION']}")
                    logger.info(f"   - 存储引擎: {table_info['ENGINE']}")
                else:
                    logger.error("❌ 表创建失败，未找到目标表")
                    return False
                
                # 检查字段数量
                sql_check_columns = """
                SELECT COUNT(*) as column_count
                FROM information_schema.COLUMNS 
                WHERE TABLE_SCHEMA = 'zhenxuandb' 
                AND TABLE_NAME = 'zhenxuan_querySelectProjectDetail'
                """
                
                cursor.execute(sql_check_columns)
                column_info = cursor.fetchone()
                
                if column_info:
                    logger.info(f"✅ 字段数量: {column_info['column_count']}")
                
                # 检查索引
                sql_check_indexes = """
                SELECT 
                    INDEX_NAME,
                    COLUMN_NAME,
                    NON_UNIQUE
                FROM information_schema.STATISTICS 
                WHERE TABLE_SCHEMA = 'zhenxuandb' 
                AND TABLE_NAME = 'zhenxuan_querySelectProjectDetail'
                ORDER BY INDEX_NAME, SEQ_IN_INDEX
                """
                
                cursor.execute(sql_check_indexes)
                indexes = cursor.fetchall()
                
                if indexes:
                    logger.info(f"✅ 索引数量: {len(set(idx['INDEX_NAME'] for idx in indexes))}")
                    
                    # 显示主要索引
                    unique_indexes = set()
                    for idx in indexes:
                        if idx['INDEX_NAME'] not in unique_indexes:
                            unique_indexes.add(idx['INDEX_NAME'])
                            index_type = "唯一索引" if idx['NON_UNIQUE'] == 0 else "普通索引"
                            logger.info(f"   - {idx['INDEX_NAME']}: {index_type}")
                
                # 检查视图
                sql_check_view = """
                SELECT 
                    TABLE_NAME
                FROM information_schema.VIEWS 
                WHERE TABLE_SCHEMA = 'zhenxuandb' 
                AND TABLE_NAME = 'v_zhenxuan_project_detail_summary'
                """
                
                cursor.execute(sql_check_view)
                view_info = cursor.fetchone()
                
                if view_info:
                    logger.info(f"✅ 视图创建成功: {view_info['TABLE_NAME']}")
                else:
                    logger.warning("⚠️ 视图创建可能失败")
        
        logger.info("🎉 表结构验证完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 验证表创建失败: {e}")
        return False

def show_table_structure():
    """显示表结构详情"""
    
    try:
        logger.info("📋 显示表结构详情...")
        
        db_manager = get_db_manager(use_zhenxuan=True)
        
        if not db_manager.connect():
            logger.error("❌ 数据库连接失败")
            return False
        
        sql_describe = "DESCRIBE zhenxuan_querySelectProjectDetail"
        
        with db_manager.get_connection() as conn:
            with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql_describe)
                columns = cursor.fetchall()
                
                logger.info("📊 表字段结构:")
                logger.info("-" * 80)
                logger.info(f"{'字段名':<30} {'类型':<20} {'允许NULL':<10} {'键':<10} {'默认值':<15}")
                logger.info("-" * 80)
                
                for col in columns:
                    logger.info(f"{col['Field']:<30} {col['Type']:<20} {col['Null']:<10} {col['Key']:<10} {str(col['Default']):<15}")
                
                logger.info("-" * 80)
                logger.info(f"总字段数: {len(columns)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 显示表结构失败: {e}")
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='创建甄选项目详情查询数据表')
    parser.add_argument('--show-structure', action='store_true', help='显示表结构详情')
    parser.add_argument('--verify-only', action='store_true', help='仅验证表是否存在')
    
    args = parser.parse_args()
    
    # 创建日志目录
    os.makedirs('logs', exist_ok=True)
    
    logger.info("🚀 启动甄选项目详情查询数据表创建程序")
    logger.info(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        if args.verify_only:
            # 仅验证
            db_manager = get_db_manager(use_zhenxuan=True)
            if db_manager.connect():
                verify_table_creation(db_manager)
            else:
                logger.error("❌ 数据库连接失败")
                return 1
        elif args.show_structure:
            # 显示表结构
            show_table_structure()
        else:
            # 创建表
            if create_table():
                logger.info("✅ 表创建程序执行成功")
                
                # 显示表结构
                show_table_structure()
            else:
                logger.error("❌ 表创建程序执行失败")
                return 1
    
    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        return 1
    
    logger.info(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    return 0

if __name__ == "__main__":
    exit(main())
