"""
创建本地审核跟踪历史数据表（KSM版本）
根据 queryLocalAuditTrackHistory 接口数据结构创建数据表
特别增加 selectRevId 字段用于关联查询
"""

import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from database.db_config import ZHENXUAN_DB_CONFIG, DatabaseManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/create_queryLocalAuditTrackHistory_ksm_table.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class QueryLocalAuditTrackHistoryKsmTableCreator:
    """本地审核跟踪历史数据表（KSM版本）创建器"""
    
    def __init__(self):
        """初始化"""
        self.db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
        self.sql_file_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'database',
            'create_zhenxuan_queryLocalAuditTrackHistory_ksm.sql'
        )
    
    def create_table(self) -> bool:
        """
        创建数据表
        
        Returns:
            bool: 创建是否成功
        """
        try:
            logger.info("🚀 开始创建本地审核跟踪历史数据表（KSM版本）...")
            
            # 连接数据库
            if not self.db_manager.connect():
                logger.error("❌ 数据库连接失败")
                return False
            
            # 读取SQL文件
            if not os.path.exists(self.sql_file_path):
                logger.error(f"❌ SQL文件不存在: {self.sql_file_path}")
                return False
            
            with open(self.sql_file_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            logger.info(f"📄 读取SQL文件: {self.sql_file_path}")
            
            # 分割SQL语句（按分号分割）
            sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            logger.info(f"📋 共有 {len(sql_statements)} 条SQL语句需要执行")
            
            # 执行SQL语句
            with self.db_manager.get_cursor() as cursor:
                for i, sql_statement in enumerate(sql_statements, 1):
                    try:
                        # 跳过注释行
                        if sql_statement.startswith('--') or sql_statement.startswith('/*'):
                            continue
                        
                        logger.info(f"⚡ 执行第 {i} 条SQL语句...")
                        cursor.execute(sql_statement)
                        
                        # 如果是查询语句，显示结果
                        if sql_statement.upper().strip().startswith('SELECT') or sql_statement.upper().strip().startswith('DESCRIBE'):
                            results = cursor.fetchall()
                            if results:
                                logger.info(f"📊 查询结果 ({len(results)} 条记录):")
                                for result in results:
                                    logger.info(f"   {result}")
                        
                    except Exception as e:
                        logger.warning(f"⚠️ SQL语句执行警告 (第{i}条): {e}")
                        # 继续执行其他语句
                        continue
                
                # 提交事务
                self.db_manager.connection.commit()
            
            logger.info("✅ 本地审核跟踪历史数据表（KSM版本）创建成功！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建数据表失败: {e}")
            return False
        finally:
            self.db_manager.disconnect()
    
    def verify_table(self) -> bool:
        """
        验证表创建是否成功
        
        Returns:
            bool: 验证是否成功
        """
        try:
            logger.info("🔍 验证表创建结果...")
            
            if not self.db_manager.connect():
                return False
            
            with self.db_manager.get_cursor() as cursor:
                # 检查表是否存在
                cursor.execute("""
                    SELECT TABLE_NAME, TABLE_COMMENT, TABLE_COLLATION
                    FROM information_schema.TABLES 
                    WHERE TABLE_SCHEMA = 'zhenxuandb' 
                    AND TABLE_NAME = 'zhenxuan_queryLocalAuditTrackHistory_ksm'
                """)
                
                table_info = cursor.fetchone()
                if table_info:
                    logger.info(f"✅ 表存在: {table_info['TABLE_NAME']}")
                    logger.info(f"📝 表注释: {table_info['TABLE_COMMENT']}")
                    logger.info(f"🔤 字符集: {table_info['TABLE_COLLATION']}")
                else:
                    logger.error("❌ 表不存在")
                    return False
                
                # 检查字段结构
                cursor.execute("DESCRIBE zhenxuan_queryLocalAuditTrackHistory_ksm")
                columns = cursor.fetchall()
                
                logger.info(f"📋 表结构 ({len(columns)} 个字段):")
                for column in columns:
                    logger.info(f"   {column['Field']} - {column['Type']} - {column['Comment'] if 'Comment' in column else 'N/A'}")
                
                # 检查索引
                cursor.execute("""
                    SELECT INDEX_NAME, COLUMN_NAME, INDEX_COMMENT
                    FROM information_schema.STATISTICS 
                    WHERE TABLE_SCHEMA = 'zhenxuandb' 
                    AND TABLE_NAME = 'zhenxuan_queryLocalAuditTrackHistory_ksm'
                    ORDER BY INDEX_NAME, SEQ_IN_INDEX
                """)
                
                indexes = cursor.fetchall()
                logger.info(f"🔍 索引信息 ({len(indexes)} 个索引字段):")
                for index in indexes:
                    logger.info(f"   {index['INDEX_NAME']} - {index['COLUMN_NAME']}")
                
                # 检查视图
                cursor.execute("""
                    SELECT TABLE_NAME, TABLE_TYPE
                    FROM information_schema.TABLES 
                    WHERE TABLE_SCHEMA = 'zhenxuandb' 
                    AND TABLE_NAME LIKE '%audit%ksm%'
                    ORDER BY TABLE_NAME
                """)
                
                views = cursor.fetchall()
                logger.info(f"👁️ 相关视图 ({len(views)} 个):")
                for view in views:
                    logger.info(f"   {view['TABLE_NAME']} - {view['TABLE_TYPE']}")
            
            logger.info("✅ 表验证完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 表验证失败: {e}")
            return False
        finally:
            self.db_manager.disconnect()

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("🚀 本地审核跟踪历史数据表（KSM版本）创建程序启动")
    logger.info("=" * 60)
    
    # 创建表创建器
    creator = QueryLocalAuditTrackHistoryKsmTableCreator()
    
    # 创建表
    if creator.create_table():
        logger.info("✅ 数据表创建成功")
        
        # 验证表
        if creator.verify_table():
            logger.info("✅ 数据表验证成功")
            logger.info("🎉 本地审核跟踪历史数据表（KSM版本）创建完成！")
        else:
            logger.error("❌ 数据表验证失败")
    else:
        logger.error("❌ 数据表创建失败")
    
    logger.info("=" * 60)
    logger.info("🏁 程序执行完成")
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
