#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据表是否创建成功
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import DatabaseManager, ZHENXUAN_DB_CONFIG

def test_table():
    """测试表是否存在"""
    db = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    if not db.connect():
        print("❌ 数据库连接失败")
        return
    
    try:
        with db.get_cursor() as cursor:
            # 检查表是否存在
            cursor.execute("SHOW TABLES LIKE 'zhenxuan_%'")
            tables = cursor.fetchall()
            print(f"📋 找到 {len(tables)} 个甄选相关表:")
            for table in tables:
                table_name = list(table.values())[0]
                print(f"  - {table_name}")
            
            # 检查目标表
            cursor.execute("SHOW TABLES LIKE 'zhenxuan_querySelectAuditTrackHistory'")
            result = cursor.fetchone()
            
            if result:
                print("✅ 目标表已存在")
                
                # 检查表结构
                cursor.execute("DESCRIBE zhenxuan_querySelectAuditTrackHistory")
                fields = cursor.fetchall()
                print(f"📋 表字段数量: {len(fields)}")
                print("📋 主要字段:")
                for field in fields[:10]:  # 显示前10个字段
                    print(f"  - {field['Field']}: {field['Type']}")
                if len(fields) > 10:
                    print(f"  ... 还有 {len(fields) - 10} 个字段")
                    
            else:
                print("❌ 目标表不存在，尝试创建...")
                # 重新创建表
                from scripts.create_audit_track_table import create_audit_track_table
                if create_audit_track_table():
                    print("✅ 表创建成功")
                else:
                    print("❌ 表创建失败")
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    finally:
        db.disconnect()

if __name__ == "__main__":
    test_table()
