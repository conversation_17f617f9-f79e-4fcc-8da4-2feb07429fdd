#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
甄选项目数据获取和入库程序
基于 querySelectProjectList 接口
"""

import requests
import json
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional, List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import DatabaseManager, ZHENXUAN_DB_CONFIG
from auth_loader import AuthLoader

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('zhenxuan_data_fetch.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ZhenxuanDataFetcher:
    """甄选数据获取器"""

    def __init__(self, cookie_file_path=None):
        """初始化"""
        self.base_url = "http://dict.gmcc.net:30722"
        self.db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
        self.session = requests.Session()

        # 使用 AuthLoader 加载认证信息
        auth_loader = AuthLoader(cookie_file_path)
        if auth_loader.load_auth_data():
            # 更新session的认证信息
            auth_loader.update_session(self.session)
            logger.info("✅ 认证信息加载成功")
        else:
            logger.error("❌ 认证信息加载失败")

        # 设置基本请求头（AuthLoader会自动添加认证相关的headers）
        self.session.headers.update({
            'Host': "dict.gmcc.net:30722",
            'Origin': "http://dict.gmcc.net:30722",
            'Referer': "http://dict.gmcc.net:30722/ptn/main/selectDemand",
        })





    def update_cookies(self, cookie_string: str):
        """
        更新Cookie

        Args:
            cookie_string: Cookie字符串，格式如 "key1=value1; key2=value2"
        """
        try:
            cookie_pairs = cookie_string.split('; ')
            for pair in cookie_pairs:
                if '=' in pair:
                    key, value = pair.split('=', 1)
                    self.cookies[key] = value

            # 重新生成Cookie字符串并更新headers
            new_cookie_pairs = []
            for name, value in self.cookies.items():
                new_cookie_pairs.append(f"{name}={value}")

            self.cookie_string = "; ".join(new_cookie_pairs)
            self.headers['Cookie'] = self.cookie_string

            logger.info("✅ Cookie更新成功")
        except Exception as e:
            logger.error(f"❌ Cookie更新失败: {e}")
    
    def fetch_project_list(self, 
                          select_rev_id: Optional[str] = None,
                          select_category: str = "1",
                          current_page: int = 1,
                          page_size: int = 100) -> Optional[Dict[str, Any]]:
        """
        获取甄选项目列表数据
        
        Args:
            select_rev_id: 甄选版本ID（可选参数）
            select_category: 甄选分类，默认"1"
            current_page: 当前页码，默认1
            page_size: 每页大小，默认3000
            
        Returns:
            Dict: API响应数据，失败返回None
        """
        url = f"{self.base_url}/partner/materialManage/pnrSelectProject/querySelectProjectList"
        
        # 构造请求参数
        payload = {
            "selecCategory": select_category,
            "currentPage": current_page,
            "pageSize": page_size
        }
        
        # 如果提供了selectRevId，添加到参数中
        if select_rev_id:
            payload["selectRevId"] = select_rev_id
        
        try:
            logger.info(f"🔄 开始获取数据，页码: {current_page}, 页大小: {page_size}")
            if select_rev_id:
                logger.info(f"📋 甄选版本ID: {select_rev_id}")
            
            # 发送请求（认证信息已通过AuthLoader设置到session中）
            response = self.session.post(
                url,
                json=payload,  # 使用json参数自动设置Content-Type
                timeout=30
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析JSON响应
            data = response.json()
            
            # 检查业务状态码
            if data.get('code') != '000000':
                logger.error(f"❌ API返回错误: {data.get('message', '未知错误')}")
                return None
            
            logger.info(f"✅ 数据获取成功，响应码: {data.get('code')}")
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 网络请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON解析失败: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ 获取数据失败: {e}")
            return None
    
    def transform_record_data(self, 
                             record: Dict[str, Any], 
                             response_data: Dict[str, Any],
                             select_rev_id: Optional[str] = None,
                             request_params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        转换单条记录数据为数据库格式
        
        Args:
            record: 单条记录数据
            response_data: 完整响应数据
            select_rev_id: 甄选版本ID
            request_params: 请求参数
            
        Returns:
            Dict: 转换后的数据库记录
        """
        result_body = response_data.get('resultBody', {})
        
        # 解析时间字段
        def parse_datetime(date_str):
            if not date_str:
                return None
            try:
                return datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
            except:
                return None
        
        return {
            # API请求参数
            'select_rev_id': select_rev_id,
            'request_params': json.dumps(request_params) if request_params else None,

            # 响应基础信息（根级字段）
            'busi_date': parse_datetime(response_data.get('busiDate')),
            'code': response_data.get('code'),
            'message': response_data.get('message'),

            # resultBody分页信息
            'total': result_body.get('total', 0),
            'size': result_body.get('size', 0),
            'current': result_body.get('current', 0),
            'pages': result_body.get('pages', 0),
            
            # 项目核心信息
            'project_msg_id': record.get('projectMsgId'),
            'work_order_msg_id': record.get('workOrderMsgId'),
            'shut_order_msg_id': record.get('shutOrderMsgId'),
            'select_msg_id': record.get('selectMsgId'),
            'select_apply_id': record.get('selectApplyId'),
            
            # 项目基本信息
            'project_name': record.get('projectName'),
            'select_name': record.get('selectName'),
            'count': record.get('count', '0'),
            'project_no': record.get('projectNo'),
            
            # 甄选类型
            'select_type': record.get('selectType'),
            'select_type_value': record.get('selectTypeValue'),
            
            # 项目分类
            'project_type': record.get('projectType'),
            'project_label': record.get('projectLabel'),
            
            # 业务区域
            'business_area': record.get('businessArea'),
            'business_area_value': record.get('businessAreaValue'),
            
            # 时间信息
            'start_time': parse_datetime(record.get('startTime')),
            'create_time': parse_datetime(record.get('createTime')),
            
            # 状态信息
            'select_status': record.get('selectStatus'),
            'select_status_value': record.get('selectStatusValue'),
            
            # 部门和人员
            'initiate_department': record.get('initiateDepartment'),
            'create_staff': record.get('createStaff'),
            'create_staff_value': record.get('createStaffValue'),
            'next_todo_handler': record.get('nextTodoHandler'),
            'next_todo_handler_value': record.get('nextTodoHandlerValue'),
            
            # 操作标识
            'is_fixed_softness': record.get('isFixedSoftness', '0'),
            'is_operable': record.get('isOperable', '0'),
            'is_terminable': record.get('isTerminable', '0'),
            'is_allow_second': record.get('isAllowSecond'),
            
            # 变更类型
            'change_type1': record.get('changeType1'),
            'change_type2': record.get('changeType2'),
            
            # 甄选分类
            'select_category': record.get('selectCategory'),
            'select_category_value': record.get('selectCategoryValue'),
            
            # 二次谈判
            'dpcs_select_second_negotiate': record.get('dpcsSelectSecondNegotiate'),
            
            # 原始数据
            'raw_data': json.dumps(record, ensure_ascii=False)
        }

    def insert_record(self, record_data: Dict[str, Any]) -> bool:
        """
        插入单条记录到数据库

        Args:
            record_data: 记录数据

        Returns:
            bool: 插入是否成功
        """
        sql = """
        INSERT INTO zhenxuan_querySelectProjectList (
            select_rev_id, request_params, busi_date, code, message,
            total, size, current, pages,
            project_msg_id, work_order_msg_id, shut_order_msg_id, select_msg_id, select_apply_id,
            project_name, select_name, count, project_no,
            select_type, select_type_value, project_type, project_label,
            business_area, business_area_value, start_time, create_time,
            select_status, select_status_value, initiate_department,
            create_staff, create_staff_value, next_todo_handler, next_todo_handler_value,
            is_fixed_softness, is_operable, is_terminable, is_allow_second,
            change_type1, change_type2, select_category, select_category_value,
            dpcs_select_second_negotiate, raw_data
        ) VALUES (
            %(select_rev_id)s, %(request_params)s, %(busi_date)s, %(code)s, %(message)s,
            %(total)s, %(size)s, %(current)s, %(pages)s,
            %(project_msg_id)s, %(work_order_msg_id)s, %(shut_order_msg_id)s, %(select_msg_id)s, %(select_apply_id)s,
            %(project_name)s, %(select_name)s, %(count)s, %(project_no)s,
            %(select_type)s, %(select_type_value)s, %(project_type)s, %(project_label)s,
            %(business_area)s, %(business_area_value)s, %(start_time)s, %(create_time)s,
            %(select_status)s, %(select_status_value)s, %(initiate_department)s,
            %(create_staff)s, %(create_staff_value)s, %(next_todo_handler)s, %(next_todo_handler_value)s,
            %(is_fixed_softness)s, %(is_operable)s, %(is_terminable)s, %(is_allow_second)s,
            %(change_type1)s, %(change_type2)s, %(select_category)s, %(select_category_value)s,
            %(dpcs_select_second_negotiate)s, %(raw_data)s
        ) ON DUPLICATE KEY UPDATE
            select_rev_id = VALUES(select_rev_id),
            request_params = VALUES(request_params),
            busi_date = VALUES(busi_date),
            code = VALUES(code),
            message = VALUES(message),
            total = VALUES(total),
            size = VALUES(size),
            current = VALUES(current),
            pages = VALUES(pages),
            work_order_msg_id = VALUES(work_order_msg_id),
            shut_order_msg_id = VALUES(shut_order_msg_id),
            select_msg_id = VALUES(select_msg_id),
            select_apply_id = VALUES(select_apply_id),
            project_name = VALUES(project_name),
            select_name = VALUES(select_name),
            count = VALUES(count),
            project_no = VALUES(project_no),
            select_type = VALUES(select_type),
            select_type_value = VALUES(select_type_value),
            project_type = VALUES(project_type),
            project_label = VALUES(project_label),
            business_area = VALUES(business_area),
            business_area_value = VALUES(business_area_value),
            start_time = VALUES(start_time),
            create_time = VALUES(create_time),
            select_status = VALUES(select_status),
            select_status_value = VALUES(select_status_value),
            initiate_department = VALUES(initiate_department),
            create_staff = VALUES(create_staff),
            create_staff_value = VALUES(create_staff_value),
            next_todo_handler = VALUES(next_todo_handler),
            next_todo_handler_value = VALUES(next_todo_handler_value),
            is_fixed_softness = VALUES(is_fixed_softness),
            is_operable = VALUES(is_operable),
            is_terminable = VALUES(is_terminable),
            is_allow_second = VALUES(is_allow_second),
            change_type1 = VALUES(change_type1),
            change_type2 = VALUES(change_type2),
            select_category = VALUES(select_category),
            select_category_value = VALUES(select_category_value),
            dpcs_select_second_negotiate = VALUES(dpcs_select_second_negotiate),
            raw_data = VALUES(raw_data),
            updated_at = CURRENT_TIMESTAMP
        """

        try:
            with self.db_manager.get_cursor() as cursor:
                cursor.execute(sql, record_data)
                self.db_manager.connection.commit()
                return True
        except Exception as e:
            logger.error(f"❌ 插入记录失败: {e}")
            logger.error(f"记录数据: {record_data.get('project_msg_id', 'Unknown')}")
            return False

    def sync_all_data(self,
                     select_rev_id: Optional[str] = None,
                     select_category: str = "1",
                     max_pages: int = 50) -> int:
        """
        同步所有数据

        Args:
            select_rev_id: 甄选版本ID
            select_category: 甄选分类
            max_pages: 最大页数限制

        Returns:
            int: 同步成功的记录数
        """
        logger.info("🚀 开始同步甄选项目数据...")

        if not self.db_manager.connect():
            logger.error("❌ 数据库连接失败")
            return 0

        total_synced = 0
        current_page = 1

        try:
            while current_page <= max_pages:
                # 获取当前页数据
                response_data = self.fetch_project_list(
                    select_rev_id=select_rev_id,
                    select_category=select_category,
                    current_page=current_page,
                    page_size=100  # 每页100条，避免单次请求过大
                )

                if not response_data:
                    logger.error(f"❌ 获取第{current_page}页数据失败")
                    break

                result_body = response_data.get('resultBody', {})
                records = result_body.get('records', [])
                total = result_body.get('total', 0)

                if not records:
                    logger.info("✅ 没有更多数据")
                    break

                # 构造请求参数用于记录
                request_params = {
                    "selecCategory": select_category,
                    "currentPage": current_page,
                    "pageSize": 100
                }
                if select_rev_id:
                    request_params["selectRevId"] = select_rev_id

                # 处理每条记录
                page_synced = 0
                for record in records:
                    try:
                        # 转换数据格式
                        record_data = self.transform_record_data(
                            record, response_data, select_rev_id, request_params
                        )

                        # 插入数据库
                        if self.insert_record(record_data):
                            page_synced += 1
                            total_synced += 1

                    except Exception as e:
                        logger.error(f"❌ 处理记录失败: {e}")
                        logger.error(f"记录ID: {record.get('projectMsgId', 'Unknown')}")

                logger.info(f"✅ 第{current_page}页同步完成: {page_synced}/{len(records)} 条记录")

                # 检查是否还有下一页
                if current_page * 100 >= total:
                    logger.info(f"✅ 所有数据同步完成，总计: {total} 条")
                    break

                current_page += 1

                # 添加延迟避免请求过快
                import time
                time.sleep(1)

            logger.info(f"🎉 数据同步完成，成功同步 {total_synced} 条记录")
            return total_synced

        except Exception as e:
            logger.error(f"❌ 数据同步异常: {e}")
            return total_synced
        finally:
            self.db_manager.disconnect()

    def query_data(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        查询数据库中的数据

        Args:
            limit: 查询记录数限制

        Returns:
            List[Dict]: 查询结果
        """
        if not self.db_manager.connect():
            return []

        try:
            sql = """
            SELECT
                id, project_msg_id, project_name, select_name, project_no,
                business_area_value, select_status_value, create_staff_value,
                create_time, start_time, created_at
            FROM zhenxuan_querySelectProjectList
            ORDER BY created_at DESC
            LIMIT %s
            """

            with self.db_manager.get_cursor() as cursor:
                cursor.execute(sql, (limit,))
                return cursor.fetchall()

        except Exception as e:
            logger.error(f"❌ 查询数据失败: {e}")
            return []
        finally:
            self.db_manager.disconnect()


def main():
    """主程序入口"""
    import argparse

    parser = argparse.ArgumentParser(description='甄选项目数据获取和入库程序')
    parser.add_argument('--select-rev-id', type=str, help='甄选版本ID')
    parser.add_argument('--select-category', type=str, default='1', help='甄选分类，默认为1')
    parser.add_argument('--max-pages', type=int, default=50, help='最大页数限制，默认50')
    parser.add_argument('--cookie-file', type=str, help='Cookie文件路径，默认使用cookies/cookies_dict_zhenxuan.json')
    parser.add_argument('--cookie', type=str, help='更新Cookie字符串')
    parser.add_argument('--query', action='store_true', help='查询已同步的数据')
    parser.add_argument('--limit', type=int, default=10, help='查询记录数限制，默认10')
    parser.add_argument('--all', action='store_true', help='查询全部数据模式，轮询入库所有数据（不限制页数）')

    args = parser.parse_args()

    # 创建数据获取器（使用指定的Cookie文件）
    fetcher = ZhenxuanDataFetcher(cookie_file_path=args.cookie_file)

    # 更新Cookie（如果提供）
    if args.cookie:
        fetcher.update_cookies(args.cookie)

    # 查询模式
    if args.query:
        logger.info("🔍 查询数据库中的数据...")
        results = fetcher.query_data(args.limit)

        if results:
            logger.info(f"📋 查询到 {len(results)} 条记录:")
            for i, record in enumerate(results, 1):
                logger.info(f"  {i}. {record['project_name']} ({record['project_no']})")
                logger.info(f"     状态: {record['select_status_value']}")
                logger.info(f"     创建: {record['create_time']}")
                logger.info(f"     入库: {record['created_at']}")
                logger.info("")
        else:
            logger.info("📋 没有查询到数据")
        return

    # 全部数据同步模式
    if args.all:
        logger.info("🌍 全部数据同步模式启动...")
        logger.info("📋 将查询并入库所有可用数据（不限制页数）")
        logger.info(f"📋 甄选分类: {args.select_category}")

        # 执行全部数据同步（不限制页数）
        synced_count = fetcher.sync_all_data(
            select_rev_id=args.select_rev_id,
            select_category=args.select_category,
            max_pages=999  # 设置一个很大的数字，实际会根据API返回的总数自动停止
        )

        if synced_count > 0:
            logger.info(f"🎉 全部数据同步成功！共同步 {synced_count} 条记录")
        else:
            logger.error("❌ 全部数据同步失败或没有新数据")
        return

    # 标准同步模式
    logger.info("🚀 开始数据同步...")
    logger.info(f"📋 甄选版本ID: {args.select_rev_id or '未指定'}")
    logger.info(f"📋 甄选分类: {args.select_category}")
    logger.info(f"📋 最大页数: {args.max_pages}")

    # 执行数据同步
    synced_count = fetcher.sync_all_data(
        select_rev_id=args.select_rev_id,
        select_category=args.select_category,
        max_pages=args.max_pages
    )

    if synced_count > 0:
        logger.info(f"🎉 数据同步成功！共同步 {synced_count} 条记录")
    else:
        logger.error("❌ 数据同步失败或没有新数据")


if __name__ == "__main__":
    main()
