"""
创建本地审核跟踪历史数据表
执行 create_zhenxuan_queryLocalAuditTrackHistory.sql 文件
"""

import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from database.db_config import ZHENXUAN_DB_CONFIG, DatabaseManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/create_queryLocalAuditTrackHistory_table.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_audit_track_history_table():
    """创建本地审核跟踪历史数据表"""
    
    logger.info("🚀 开始创建本地审核跟踪历史数据表...")
    
    # SQL文件路径
    sql_file_path = os.path.join(project_root, 'database', 'create_zhenxuan_queryLocalAuditTrackHistory.sql')
    
    if not os.path.exists(sql_file_path):
        logger.error(f"❌ SQL文件不存在: {sql_file_path}")
        return False
    
    # 创建数据库管理器
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    try:
        # 连接数据库
        if not db_manager.connect():
            logger.error("❌ 数据库连接失败")
            return False
        
        logger.info("✅ 数据库连接成功")
        
        # 读取SQL文件
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        logger.info(f"📖 读取SQL文件: {sql_file_path}")
        
        # 分割SQL语句（按分号分割，忽略注释）
        sql_statements = []
        current_statement = ""
        
        for line in sql_content.split('\n'):
            line = line.strip()
            
            # 跳过空行和注释行
            if not line or line.startswith('--'):
                continue
            
            current_statement += line + " "
            
            # 如果行以分号结尾，表示一个完整的SQL语句
            if line.endswith(';'):
                sql_statements.append(current_statement.strip())
                current_statement = ""
        
        logger.info(f"📊 解析到 {len(sql_statements)} 个SQL语句")
        
        # 执行SQL语句
        with db_manager.get_cursor() as cursor:
            for i, sql_statement in enumerate(sql_statements, 1):
                if not sql_statement:
                    continue
                
                try:
                    logger.info(f"⚡ 执行第 {i}/{len(sql_statements)} 个SQL语句...")
                    cursor.execute(sql_statement)
                    
                    # 特殊处理一些语句的日志输出
                    if 'DROP TABLE' in sql_statement.upper():
                        logger.info("🗑️ 删除旧表（如果存在）")
                    elif 'CREATE TABLE' in sql_statement.upper():
                        logger.info("📊 创建新表结构")
                    elif 'CREATE OR REPLACE VIEW' in sql_statement.upper():
                        logger.info("👁️ 创建视图")
                    elif 'DESCRIBE' in sql_statement.upper():
                        logger.info("🔍 验证表结构")
                        # 显示表结构
                        results = cursor.fetchall()
                        if results:
                            logger.info("📋 表结构验证结果:")
                            for row in results:
                                logger.info(f"   {row}")
                    elif 'SELECT' in sql_statement.upper() and 'information_schema' in sql_statement.lower():
                        logger.info("✅ 验证表创建")
                        # 显示验证结果
                        try:
                            results = cursor.fetchall()
                            if results:
                                for row in results:
                                    if len(row) >= 3:  # 表信息查询
                                        logger.info(f"   表名: {row[0]}, 注释: {row[1]}, 排序规则: {row[2]}")
                                    elif len(row) >= 2:  # 索引信息查询
                                        logger.info(f"   索引: {row[0]}, 字段: {row[1]}")
                                    else:
                                        logger.info(f"   结果: {row}")
                        except Exception as fetch_error:
                            logger.warning(f"⚠️ 获取查询结果失败: {fetch_error}")
                    
                except Exception as e:
                    logger.error(f"❌ 执行SQL语句失败: {e}")
                    logger.error(f"   SQL: {sql_statement[:100]}...")
                    return False
        
        # 提交事务
        db_manager.connection.commit()
        logger.info("✅ 所有SQL语句执行成功")
        
        # 最终验证
        logger.info("🔍 执行最终验证...")
        
        with db_manager.get_connection() as conn:
            with conn.cursor() as cursor:
                # 简单验证表是否存在
                cursor.execute("SHOW TABLES LIKE 'zhenxuan_queryLocalAuditTrackHistory'")
                result = cursor.fetchone()

                if result:
                    logger.info(f"✅ 表验证成功: {result[0]}")

                    # 检查字段数量
                    cursor.execute("SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = 'zhenxuandb' AND TABLE_NAME = 'zhenxuan_queryLocalAuditTrackHistory'")
                    field_count = cursor.fetchone()[0]
                    logger.info(f"   📋 字段数量: {field_count}")

                    # 检查索引数量
                    cursor.execute("SELECT COUNT(*) FROM information_schema.STATISTICS WHERE TABLE_SCHEMA = 'zhenxuandb' AND TABLE_NAME = 'zhenxuan_queryLocalAuditTrackHistory'")
                    index_count = cursor.fetchone()[0]
                    logger.info(f"   🔍 索引数量: {index_count}")

                    # 检查视图数量
                    cursor.execute("SHOW TABLES LIKE '%audit%'")
                    views = cursor.fetchall()
                    logger.info(f"   👁️ 相关视图数量: {len(views)}")

                else:
                    logger.error("❌ 表验证失败: 表不存在")
                    return False
        
        logger.info("🎉 本地审核跟踪历史数据表创建完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建表失败: {e}")
        return False
    finally:
        db_manager.disconnect()

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("🚀 本地审核跟踪历史数据表创建程序")
    logger.info("=" * 60)
    
    start_time = datetime.now()
    
    try:
        success = create_audit_track_history_table()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if success:
            logger.info("=" * 60)
            logger.info("🎉 程序执行成功！")
            logger.info(f"⏱️ 总耗时: {duration:.2f} 秒")
            logger.info("=" * 60)
        else:
            logger.error("=" * 60)
            logger.error("❌ 程序执行失败！")
            logger.error(f"⏱️ 总耗时: {duration:.2f} 秒")
            logger.error("=" * 60)
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("\n⚠️ 程序被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 程序执行异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
