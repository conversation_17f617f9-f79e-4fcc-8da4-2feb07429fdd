"""
测试KSM表创建和数据获取
"""

import os
import sys
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from database.db_config import ZHENXUAN_DB_CONFIG, DatabaseManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_database():
    """测试数据库连接和表结构"""
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    try:
        if not db_manager.connect():
            logger.error("❌ 数据库连接失败")
            return
        
        with db_manager.get_cursor() as cursor:
            # 检查所有audit相关的表
            cursor.execute("SHOW TABLES LIKE '%audit%'")
            tables = cursor.fetchall()
            logger.info(f"📋 找到 {len(tables)} 个audit相关表:")
            for table in tables:
                table_name = list(table.values())[0]
                logger.info(f"   - {table_name}")
            
            # 检查KSM表是否存在
            cursor.execute("SHOW TABLES LIKE '%ksm%'")
            ksm_tables = cursor.fetchall()
            logger.info(f"📋 找到 {len(ksm_tables)} 个KSM相关表:")
            for table in ksm_tables:
                table_name = list(table.values())[0]
                logger.info(f"   - {table_name}")
            
            # 检查合作伙伴详情表的数据
            cursor.execute("""
                SELECT COUNT(*) as count,
                       COUNT(DISTINCT select_rev_id_detail) as distinct_select_rev_id
                FROM zhenxuan_queryPartnerSelectDetail 
                WHERE select_rev_id_detail IS NOT NULL 
                AND select_rev_id_detail != ''
            """)
            result = cursor.fetchone()
            logger.info(f"📊 合作伙伴详情表统计:")
            logger.info(f"   总记录数: {result['count']}")
            logger.info(f"   有效selectRevId数: {result['distinct_select_rev_id']}")
            
            # 显示一些示例数据
            cursor.execute("""
                SELECT select_rev_id_detail, work_order_msg_id, project_name
                FROM zhenxuan_queryPartnerSelectDetail 
                WHERE select_rev_id_detail IS NOT NULL 
                AND select_rev_id_detail != ''
                LIMIT 5
            """)
            samples = cursor.fetchall()
            logger.info(f"📋 示例数据:")
            for i, sample in enumerate(samples, 1):
                logger.info(f"   {i}. selectRevId: {sample['select_rev_id_detail']}")
                logger.info(f"      workOrderMsgId: {sample['work_order_msg_id']}")
                logger.info(f"      项目名称: {sample['project_name']}")
                
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
    finally:
        db_manager.disconnect()

def create_table_manually():
    """手动创建KSM表"""
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    try:
        if not db_manager.connect():
            logger.error("❌ 数据库连接失败")
            return
        
        # 手动创建表的SQL
        create_sql = """
        CREATE TABLE IF NOT EXISTS `zhenxuan_queryLocalAuditTrackHistory_ksm` (
          `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
          `select_rev_id` VARCHAR(100) NOT NULL COMMENT '甄选版本ID',
          `business_id` VARCHAR(50) NOT NULL COMMENT '业务ID（入参）',
          `work_order_msg_id` VARCHAR(100) DEFAULT NULL COMMENT '工单消息ID（入参）',
          `step_name_filter` VARCHAR(100) DEFAULT NULL COMMENT '步骤名称过滤（入参）',
          `request_params` JSON DEFAULT NULL COMMENT '请求参数JSON',
          `busi_date` DATETIME DEFAULT NULL COMMENT '业务日期',
          `code` VARCHAR(20) DEFAULT NULL COMMENT '响应代码',
          `message` TEXT DEFAULT NULL COMMENT '响应消息',
          `audit_process_track_id` VARCHAR(50) DEFAULT NULL COMMENT '审核流程跟踪ID',
          `step_name` VARCHAR(100) DEFAULT NULL COMMENT '步骤名称',
          `create_time` DATETIME DEFAULT NULL COMMENT '创建时间',
          `finish_time` DATETIME DEFAULT NULL COMMENT '完成时间',
          `status` VARCHAR(50) DEFAULT NULL COMMENT '状态',
          `audit_handler` VARCHAR(100) DEFAULT NULL COMMENT '审核处理人',
          `audit_remark` TEXT DEFAULT NULL COMMENT '审核备注',
          `raw_data` JSON DEFAULT NULL COMMENT '原始JSON数据',
          `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
          `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
          INDEX `idx_select_rev_id` (`select_rev_id`),
          INDEX `idx_business_id` (`business_id`),
          INDEX `idx_step_name` (`step_name`),
          INDEX `idx_status` (`status`),
          UNIQUE KEY `uk_audit_track_unique_ksm` (`select_rev_id`, `business_id`, `audit_process_track_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='本地审核跟踪历史数据表（KSM版本）'
        """
        
        with db_manager.get_cursor() as cursor:
            logger.info("🚀 开始创建KSM表...")
            cursor.execute(create_sql)
            db_manager.connection.commit()
            logger.info("✅ KSM表创建成功")
            
            # 验证表创建
            cursor.execute("DESCRIBE zhenxuan_queryLocalAuditTrackHistory_ksm")
            columns = cursor.fetchall()
            logger.info(f"📋 表结构验证 ({len(columns)} 个字段):")
            for column in columns:
                logger.info(f"   {column['Field']} - {column['Type']}")
                
    except Exception as e:
        logger.error(f"❌ 创建表失败: {e}")
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    logger.info("🚀 开始测试KSM表和数据...")
    test_database()
    
    logger.info("\n🔧 手动创建KSM表...")
    create_table_manually()
    
    logger.info("\n🔍 再次验证...")
    test_database()
