"""
检查KSM表数据统计
"""

import os
import sys
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from database.db_config import ZHENXUAN_DB_CONFIG, DatabaseManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_ksm_data_stats():
    """检查KSM表数据统计"""
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    try:
        if not db_manager.connect():
            logger.error("❌ 数据库连接失败")
            return
        
        with db_manager.get_cursor() as cursor:
            # 总记录数
            cursor.execute("SELECT COUNT(*) as total FROM zhenxuan_queryLocalAuditTrackHistory_ksm")
            total = cursor.fetchone()['total']
            logger.info(f"📊 总记录数: {total}")
            
            # 按selectRevId统计
            cursor.execute("""
                SELECT COUNT(DISTINCT select_rev_id) as unique_select_rev_ids
                FROM zhenxuan_queryLocalAuditTrackHistory_ksm
            """)
            unique_ids = cursor.fetchone()['unique_select_rev_ids']
            logger.info(f"📋 唯一selectRevId数: {unique_ids}")
            
            # 按步骤统计
            cursor.execute("""
                SELECT step_name, COUNT(*) as count
                FROM zhenxuan_queryLocalAuditTrackHistory_ksm
                GROUP BY step_name
                ORDER BY count DESC
                LIMIT 10
            """)
            steps = cursor.fetchall()
            logger.info(f"📋 步骤统计 (前10):")
            for step in steps:
                logger.info(f"   {step['step_name']}: {step['count']} 条")
            
            # 按状态统计
            cursor.execute("""
                SELECT status, COUNT(*) as count
                FROM zhenxuan_queryLocalAuditTrackHistory_ksm
                GROUP BY status
                ORDER BY count DESC
            """)
            statuses = cursor.fetchall()
            logger.info(f"📋 状态统计:")
            for status in statuses:
                logger.info(f"   {status['status']}: {status['count']} 条")
            
            # 环节4特殊查询：制定甄选方案审核-在线填写
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM zhenxuan_queryLocalAuditTrackHistory_ksm
                WHERE step_name = '制定甄选方案审核-在线填写'
            """)
            online_review = cursor.fetchone()['count']
            logger.info(f"🎯 环节4【制定甄选方案审核-在线填写】记录数: {online_review}")
            
            # 黄振国的任务完成记录
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM zhenxuan_queryLocalAuditTrackHistory_ksm
                WHERE audit_handler LIKE '%黄振国%'
                AND step_name = '制定甄选方案审核-在线填写'
            """)
            huang_count = cursor.fetchone()['count']
            logger.info(f"👤 黄振国在环节4的任务完成记录数: {huang_count}")
            
            # 显示黄振国的具体记录
            cursor.execute("""
                SELECT select_rev_id, audit_handler, create_time, finish_time, status, audit_remark
                FROM zhenxuan_queryLocalAuditTrackHistory_ksm
                WHERE audit_handler LIKE '%黄振国%'
                AND step_name = '制定甄选方案审核-在线填写'
                ORDER BY finish_time DESC
                LIMIT 5
            """)
            huang_records = cursor.fetchall()
            logger.info(f"👤 黄振国最近5条环节4记录:")
            for i, record in enumerate(huang_records, 1):
                logger.info(f"   {i}. selectRevId: {record['select_rev_id']}")
                logger.info(f"      处理人: {record['audit_handler']}")
                logger.info(f"      创建时间: {record['create_time']}")
                logger.info(f"      完成时间: {record['finish_time']}")
                logger.info(f"      状态: {record['status']}")
                logger.info(f"      备注: {record['audit_remark']}")
                logger.info("")
                
    except Exception as e:
        logger.error(f"❌ 检查数据统计失败: {e}")
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    logger.info("🚀 开始检查KSM表数据统计...")
    check_ksm_data_stats()
