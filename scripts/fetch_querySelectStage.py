"""
甄选阶段查询数据获取程序
根据 querySelectStage 接口获取甄选阶段数据并入库
"""

import os
import sys
import json
import requests
import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional, List

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from database.db_config import ZHENXUAN_DB_CONFIG, DatabaseManager
from auth_loader import AuthLoader

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/fetch_querySelectStage.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class QuerySelectStageFetcher:
    """甄选阶段查询数据获取器"""
    
    def __init__(self, cookie_file_path=None):
        """初始化"""
        self.base_url = "https://dict.gmcc.net:30722"
        self.db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
        self.session = requests.Session()
        # 使用 AuthLoader 加载认证信息
        auth_loader = AuthLoader(cookie_file_path)
        if auth_loader.load_auth_data():
            # 更新session的认证信息
            auth_loader.update_session(self.session)
            logger.info("✅ 认证信息加载成功")
        else:
            logger.error("❌ 认证信息加载失败")
            
        # 设置基本请求头（AuthLoader会自动添加认证相关的headers）
        self.session.headers.update({
            'Host': "dict.gmcc.net:30722",
            'Origin': "http://dict.gmcc.net:30722",
            'Referer': "http://dict.gmcc.net:30722/ptn/main/selectDemand",
        })
        
        
        
        # 初始化Cookie
        self.cookies = {}
        self.cookie_string = ""

        # 设置默认Cookie文件路径
        if cookie_file_path is None:
            # 查找项目根目录下的cookies文件
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            cookie_file_path = os.path.join(project_root, 'cookies', 'cookies_dict_zhenxuan.json')

        # 加载Cookie
        

    def load_cookies_from_file(self, cookie_file_path: str):
        """
        从JSON文件加载Cookie，转换为Cookie字符串格式
        保留所有Cookie，包括同名但不同path的Cookie

        Args:
            cookie_file_path: Cookie文件路径
        """
        try:
            if os.path.exists(cookie_file_path):
                with open(cookie_file_path, 'r', encoding='utf-8') as f:
                    cookie_data = json.load(f)

                # 生成Cookie字符串（保留所有Cookie，包括同名的）
                cookie_pairs = []
                cookie_dict = {}  # 用于显示和统计

                for cookie in cookie_data:
                    name = cookie['name']
                    value = cookie['value']
                    path = cookie.get('path', '/')

                    # 添加到Cookie字符串（所有Cookie都要包含）
                    cookie_pairs.append(f"{name}={value}")

                    # 用于显示的字典（同名Cookie显示最后一个，但实际都会发送）
                    if name not in cookie_dict:
                        cookie_dict[name] = []
                    cookie_dict[name].append({'value': value, 'path': path})

                self.cookie_string = "; ".join(cookie_pairs)
                self.headers['Cookie'] = self.cookie_string
                self.cookies = cookie_dict

                logger.info(f"✅ 成功加载Cookie文件: {cookie_file_path}")
                logger.info(f"📊 Cookie统计: 共{len(cookie_data)}个Cookie")
                
                # 显示Cookie详情（用于调试）
                for name, values in cookie_dict.items():
                    if len(values) > 1:
                        logger.info(f"   🔄 {name}: {len(values)}个值 (多路径)")
                    else:
                        logger.info(f"   ✓ {name}: {values[0]['value'][:20]}...")

            else:
                logger.warning(f"⚠️ Cookie文件不存在: {cookie_file_path}")
                logger.info("🔄 使用默认Cookie配置")

        except Exception as e:
            logger.error(f"❌ 加载Cookie文件失败: {e}")
            logger.info("🔄 使用默认Cookie配置")

    def update_cookies(self, cookie_string: str):
        """
        更新Cookie

        Args:
            cookie_string: Cookie字符串，格式如 "key1=value1; key2=value2"
        """
        try:
            cookie_pairs = cookie_string.split('; ')
            for pair in cookie_pairs:
                if '=' in pair:
                    key, value = pair.split('=', 1)
                    self.cookies[key] = value

            # 重新生成Cookie字符串并更新headers
            new_cookie_pairs = []
            for name, value in self.cookies.items():
                new_cookie_pairs.append(f"{name}={value}")

            self.cookie_string = "; ".join(new_cookie_pairs)
            self.headers['Cookie'] = self.cookie_string

            logger.info("✅ Cookie更新成功")
        except Exception as e:
            logger.error(f"❌ Cookie更新失败: {e}")

    def get_project_msg_ids_from_db(self, get_all: bool = False) -> List[str]:
        """
        从zhenxuan_queryPartnerSelectDetail表获取projectMsgId列表

        Args:
            get_all: 是否获取全部数据，默认False（限制数量）

        Returns:
            List[str]: projectMsgId列表
        """
        try:
            # 首先尝试从zhenxuan_queryPartnerSelectDetail表获取
            if get_all:
                sql = "SELECT DISTINCT project_msg_id FROM zhenxuan_queryPartnerSelectDetail WHERE project_msg_id IS NOT NULL"
            else:
                sql = "SELECT DISTINCT project_msg_id FROM zhenxuan_queryPartnerSelectDetail WHERE project_msg_id IS NOT NULL LIMIT 10"

            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql)
                    results = cursor.fetchall()

                    if results:
                        project_msg_ids = [row[0] for row in results]
                        if get_all:
                            logger.info(f"✅ 从zhenxuan_queryPartnerSelectDetail表获取到{len(project_msg_ids)}个projectMsgId（全部数据）")
                        else:
                            logger.info(f"✅ 从zhenxuan_queryPartnerSelectDetail表获取到{len(project_msg_ids)}个projectMsgId")
                        return project_msg_ids
                    else:
                        logger.warning("⚠️ zhenxuan_queryPartnerSelectDetail表中没有找到projectMsgId")

        except Exception as e:
            logger.warning(f"⚠️ 从zhenxuan_queryPartnerSelectDetail表获取数据失败: {e}")

        # 如果上面失败，尝试从zhenxuan_querySelectProjectList表获取
        try:
            if get_all:
                sql = "SELECT DISTINCT project_msg_id FROM zhenxuan_querySelectProjectList WHERE project_msg_id IS NOT NULL"
            else:
                sql = "SELECT DISTINCT project_msg_id FROM zhenxuan_querySelectProjectList WHERE project_msg_id IS NOT NULL LIMIT 10"

            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql)
                    results = cursor.fetchall()

                    if results:
                        project_msg_ids = [row[0] for row in results]
                        if get_all:
                            logger.info(f"✅ 从zhenxuan_querySelectProjectList表获取到{len(project_msg_ids)}个projectMsgId（全部数据）")
                        else:
                            logger.info(f"✅ 从zhenxuan_querySelectProjectList表获取到{len(project_msg_ids)}个projectMsgId（限制10个用于测试）")
                        return project_msg_ids
                    else:
                        logger.warning("⚠️ zhenxuan_querySelectProjectList表中没有找到projectMsgId")

        except Exception as e:
            logger.error(f"❌ 从数据库获取projectMsgId失败: {e}")

        # 如果都失败，返回示例数据
        logger.info("🔄 使用示例projectMsgId进行测试")
        return ["1877647623078199296"]

    def fetch_select_stage_data(self, project_msg_id: str, select_rev_id: str = None) -> Optional[Dict[str, Any]]:
        """
        获取甄选阶段数据
        
        Args:
            project_msg_id: 项目消息ID
            select_rev_id: 甄选版本ID（可选）
            
        Returns:
            Dict: API响应数据，失败返回None
        """
        url = f"{self.base_url}/partner/materialManage/pnrSelectProject/querySelectStage"
        
        # 构建查询参数
        params = {
            'projectMsgId': project_msg_id,
            'selectRevId': select_rev_id or ''  # API要求必须提供此参数，即使为空
        }
        
        try:
            logger.info(f"🔄 开始获取甄选阶段数据，projectMsgId: {project_msg_id}")
            if select_rev_id:
                logger.info(f"📋 甄选版本ID: {select_rev_id}")
            
            # 发送请求
            response = self.session.get(url, json=payload, timeout=30)
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析JSON响应
            data = response.json()
            
            # 检查业务状态码
            if data.get('code') != '000000':
                logger.error(f"❌ API返回错误: {data.get('message', '未知错误')}")
                return None
            
            logger.info(f"✅ 数据获取成功，响应码: {data.get('code')}")
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 网络请求失败: {e}")
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"📋 响应状态码: {e.response.status_code}")
                try:
                    logger.error(f"📋 响应内容: {e.response.text[:500]}")
                except:
                    pass
            return None
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON解析失败: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ 获取数据失败: {e}")
            return None

    def parse_datetime(self, date_str: str) -> Optional[datetime]:
        """
        解析日期时间字符串

        Args:
            date_str: 日期时间字符串

        Returns:
            datetime: 解析后的日期时间对象，失败返回None
        """
        if not date_str:
            return None

        try:
            # 尝试解析标准格式
            return datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            try:
                # 尝试解析其他格式
                return datetime.strptime(date_str, '%Y-%m-%d')
            except ValueError:
                logger.warning(f"⚠️ 无法解析日期时间: {date_str}")
                return None

    def transform_data(self, data: Dict[str, Any], project_msg_id: str, select_rev_id: str = None) -> Dict[str, Any]:
        """
        转换API响应数据为数据库记录格式

        Args:
            data: API响应数据
            project_msg_id: 项目消息ID
            select_rev_id: 甄选版本ID

        Returns:
            Dict: 转换后的数据库记录
        """
        result_body = data.get('resultBody', {})

        return {
            # 请求参数
            'project_msg_id': project_msg_id,
            'select_rev_id': select_rev_id,
            'request_params': json.dumps({
                'projectMsgId': project_msg_id,
                'selectRevId': select_rev_id
            }),

            # 响应基础信息
            'busi_date': self.parse_datetime(data.get('busiDate')),
            'code': data.get('code'),
            'message': data.get('message'),

            # resultBody中的甄选阶段信息
            'select_stage': result_body.get('selectStage'),
            'select_stage_value': result_body.get('selectStageValue'),
            'select_apply_id': result_body.get('selectApplyId'),
            'select_apply_status': result_body.get('selectApplyStatus'),
            'select_msg_id': result_body.get('selectMsgId'),
            'select_plan_status': result_body.get('selectPlanStatus'),
            'select_demand_status': result_body.get('selectDemandStatus'),
            'notice_id': result_body.get('noticeId'),
            'select_clarify_id': result_body.get('selectClarifyId'),
            'review_team_msg_id': result_body.get('reviewTeamMsgId'),
            'clarify_work_order_msg_id': result_body.get('clarifyWorkOrderMsgId'),
            'second_negotiate_id': result_body.get('secondNegotiateId'),
            'work_order_msg_id': result_body.get('workOrderMsgId'),
            'shut_order_msg_id': result_body.get('shutOrderMsgId'),

            # 原始数据
            'raw_data': json.dumps(data, ensure_ascii=False)
        }

    def insert_data(self, record: Dict[str, Any]) -> bool:
        """
        插入数据到数据库

        Args:
            record: 数据库记录

        Returns:
            bool: 插入是否成功
        """
        sql = """
        INSERT INTO zhenxuan_querySelectStage (
            project_msg_id, select_rev_id, request_params, busi_date, code, message,
            select_stage, select_stage_value, select_apply_id, select_apply_status,
            select_msg_id, select_plan_status, select_demand_status, notice_id,
            select_clarify_id, review_team_msg_id, clarify_work_order_msg_id,
            second_negotiate_id, work_order_msg_id, shut_order_msg_id, raw_data
        ) VALUES (
            %(project_msg_id)s, %(select_rev_id)s, %(request_params)s, %(busi_date)s, %(code)s, %(message)s,
            %(select_stage)s, %(select_stage_value)s, %(select_apply_id)s, %(select_apply_status)s,
            %(select_msg_id)s, %(select_plan_status)s, %(select_demand_status)s, %(notice_id)s,
            %(select_clarify_id)s, %(review_team_msg_id)s, %(clarify_work_order_msg_id)s,
            %(second_negotiate_id)s, %(work_order_msg_id)s, %(shut_order_msg_id)s, %(raw_data)s
        ) ON DUPLICATE KEY UPDATE
            busi_date = VALUES(busi_date),
            code = VALUES(code),
            message = VALUES(message),
            select_stage = VALUES(select_stage),
            select_stage_value = VALUES(select_stage_value),
            select_apply_status = VALUES(select_apply_status),
            select_plan_status = VALUES(select_plan_status),
            select_demand_status = VALUES(select_demand_status),
            notice_id = VALUES(notice_id),
            select_clarify_id = VALUES(select_clarify_id),
            review_team_msg_id = VALUES(review_team_msg_id),
            clarify_work_order_msg_id = VALUES(clarify_work_order_msg_id),
            second_negotiate_id = VALUES(second_negotiate_id),
            work_order_msg_id = VALUES(work_order_msg_id),
            shut_order_msg_id = VALUES(shut_order_msg_id),
            raw_data = VALUES(raw_data),
            updated_at = CURRENT_TIMESTAMP
        """

        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, record)
                    conn.commit()

                    if cursor.rowcount > 0:
                        logger.info(f"✅ 数据插入成功: projectMsgId={record['project_msg_id']}")
                        return True
                    else:
                        logger.warning(f"⚠️ 数据未发生变化: projectMsgId={record['project_msg_id']}")
                        return True

        except Exception as e:
            logger.error(f"❌ 数据插入失败: {e}")
            logger.error(f"📋 记录详情: {record}")
            return False

    def sync_all_data(self, select_rev_id: str = None, max_records: int = None, get_all: bool = False):
        """
        同步所有甄选阶段数据

        Args:
            select_rev_id: 甄选版本ID（可选）
            max_records: 最大处理记录数（可选，用于测试）
            get_all: 是否获取全部数据（可选，用于全量同步）
        """
        if get_all:
            logger.info("🚀 开始全量同步甄选阶段数据")
        else:
            logger.info("🚀 开始同步甄选阶段数据")

        # 获取projectMsgId列表
        project_msg_ids = self.get_project_msg_ids_from_db(get_all=get_all)

        if not project_msg_ids:
            logger.error("❌ 没有找到可处理的projectMsgId")
            return

        # 限制处理数量（用于测试，但全量模式下忽略此限制）
        if max_records and not get_all and len(project_msg_ids) > max_records:
            project_msg_ids = project_msg_ids[:max_records]
            logger.info(f"🔄 限制处理数量为{max_records}条记录")
        elif get_all:
            logger.info(f"🔄 全量模式：将处理{len(project_msg_ids)}条记录")

        success_count = 0
        error_count = 0

        # 添加进度统计
        total_count = len(project_msg_ids)
        batch_size = 50  # 每50条记录显示一次进度统计

        for i, project_msg_id in enumerate(project_msg_ids, 1):
            logger.info(f"📋 处理进度: {i}/{total_count} ({i/total_count*100:.1f}%) - {project_msg_id}")

            try:
                # 获取数据
                data = self.fetch_select_stage_data(project_msg_id, select_rev_id)

                if data:
                    # 转换数据
                    record = self.transform_data(data, project_msg_id, select_rev_id)

                    # 插入数据库
                    if self.insert_data(record):
                        success_count += 1
                    else:
                        error_count += 1
                else:
                    error_count += 1

            except Exception as e:
                logger.error(f"❌ 处理projectMsgId {project_msg_id} 时发生错误: {e}")
                error_count += 1

            # 批量进度统计
            if i % batch_size == 0 or i == total_count:
                logger.info(f"📊 阶段统计: 已处理{i}/{total_count}, 成功{success_count}, 失败{error_count}")

            # 请求间隔，避免过快请求
            if i < len(project_msg_ids):
                time.sleep(1)

        # 最终统计
        success_rate = (success_count / total_count * 100) if total_count > 0 else 0
        logger.info(f"✅ 同步完成！总计: {total_count}, 成功: {success_count}, 失败: {error_count}, 成功率: {success_rate:.1f}%")

    def query_data(self, limit: int = 10):
        """
        查询已同步的数据

        Args:
            limit: 查询记录数限制
        """
        sql = """
        SELECT
            project_msg_id,
            select_stage_value,
            select_apply_id,
            select_msg_id,
            work_order_msg_id,
            created_at
        FROM zhenxuan_querySelectStage
        ORDER BY created_at DESC
        LIMIT %s
        """

        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, (limit,))
                    results = cursor.fetchall()

                    if results:
                        logger.info(f"📊 查询到{len(results)}条甄选阶段记录:")
                        logger.info("=" * 100)
                        logger.info(f"{'项目ID':<20} {'甄选阶段':<15} {'申请ID':<20} {'消息ID':<20} {'创建时间':<20}")
                        logger.info("=" * 100)

                        for row in results:
                            project_msg_id, stage_value, apply_id, msg_id, work_order_id, created_at = row
                            logger.info(f"{project_msg_id:<20} {stage_value or 'N/A':<15} {apply_id or 'N/A':<20} {msg_id or 'N/A':<20} {created_at}")

                        logger.info("=" * 100)
                    else:
                        logger.info("📊 没有找到甄选阶段数据")

        except Exception as e:
            logger.error(f"❌ 查询数据失败: {e}")

def main():
    """主程序入口"""
    import argparse

    parser = argparse.ArgumentParser(description='甄选阶段数据获取和入库程序')
    parser.add_argument('--select-rev-id', type=str, help='甄选版本ID')
    parser.add_argument('--max-records', type=int, help='最大处理记录数（用于测试，全量模式下忽略）')
    parser.add_argument('--cookie-file', type=str, help='Cookie文件路径，默认使用cookies/cookies_dict_zhenxuan.json')
    parser.add_argument('--cookie', type=str, help='更新Cookie字符串')
    parser.add_argument('--query', action='store_true', help='查询已同步的数据')
    parser.add_argument('--limit', type=int, default=10, help='查询记录数限制，默认10')
    parser.add_argument('--all', action='store_true', help='全量模式：查询全部数据并轮询入库')

    args = parser.parse_args()

    # 创建数据获取器（使用指定的Cookie文件）
    fetcher = QuerySelectStageFetcher(cookie_file_path=args.cookie_file)

    # 更新Cookie（如果提供）
    if args.cookie:
        fetcher.update_cookies(args.cookie)

    # 查询数据
    if args.query:
        fetcher.query_data(args.limit)
        return

    # 全量模式提示
    if args.all:
        logger.info("🎯 全量模式已启用：将处理所有可用的projectMsgId")
        if args.max_records:
            logger.warning("⚠️ 全量模式下忽略--max-records参数")

    # 同步数据
    fetcher.sync_all_data(
        select_rev_id=args.select_rev_id,
        max_records=args.max_records,
        get_all=args.all
    )

if __name__ == "__main__":
    # 确保日志目录存在
    os.makedirs('logs', exist_ok=True)

    main()
