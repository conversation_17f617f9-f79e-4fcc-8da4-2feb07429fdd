#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
甄选审核跟踪历史数据获取和入库程序
基于 querySelectAuditTrackHistory 接口
"""

import requests
import json
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional, List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import DatabaseManager, ZHENXUAN_DB_CONFIG
from auth_loader import AuthLoader

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('zhenxuan_audit_track_fetch.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ZhenxuanAuditTrackFetcher:
    """甄选审核跟踪历史数据获取器"""

    def __init__(self, cookie_file_path=None):
        """初始化"""
        self.base_url = "https://dict.gmcc.net:30722"
        self.db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
        self.session = requests.Session()
        # 使用 AuthLoader 加载认证信息
        auth_loader = AuthLoader(cookie_file_path)
        if auth_loader.load_auth_data():
            # 更新session的认证信息
            auth_loader.update_session(self.session)
            logger.info("✅ 认证信息加载成功")
        else:
            logger.error("❌ 认证信息加载失败")
            
        # 设置基本请求头（AuthLoader会自动添加认证相关的headers）
        self.session.headers.update({
            'Host': "dict.gmcc.net:30722",
            'Origin': "http://dict.gmcc.net:30722",
            'Referer': "http://dict.gmcc.net:30722/ptn/main/selectDemand",
        })

        

        # 初始化Cookie
        self.cookies = {}

        # 设置默认Cookie文件路径
        if cookie_file_path is None:
            # 查找项目根目录下的cookies文件
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            cookie_file_path = os.path.join(project_root, 'cookies', 'cookies_dict_zhenxuan.json')

        # 加载Cookie
        

    def load_cookies_from_file(self, cookie_file_path: str):
        """
        从JSON文件加载Cookie，转换为Cookie字符串格式
        保留所有Cookie，包括同名但不同path的Cookie

        Args:
            cookie_file_path: Cookie文件路径
        """
        try:
            if os.path.exists(cookie_file_path):
                with open(cookie_file_path, 'r', encoding='utf-8') as f:
                    cookie_data = json.load(f)

                # 生成Cookie字符串（保留所有Cookie，包括同名的）
                cookie_pairs = []
                cookie_dict = {}  # 用于显示和统计

                for cookie in cookie_data:
                    name = cookie['name']
                    value = cookie['value']
                    path = cookie.get('path', '/')

                    # 添加到Cookie字符串（所有Cookie都要包含）
                    cookie_pairs.append(f"{name}={value}")

                    # 用于显示的字典（同名Cookie显示最后一个，但实际都会发送）
                    if name not in cookie_dict:
                        cookie_dict[name] = []
                    cookie_dict[name].append({'value': value, 'path': path})

                self.cookie_string = "; ".join(cookie_pairs)
                self.cookies = {name: cookies[-1]['value'] for name, cookies in cookie_dict.items()}  # 显示用

                # 更新headers中的Cookie
                self.headers['Cookie'] = self.cookie_string

                logger.info(f"✅ 成功加载Cookie文件: {cookie_file_path}")
                logger.info(f"📋 加载了 {len(cookie_data)} 个Cookie项")

                # 显示重复Cookie统计
                for name, cookies in cookie_dict.items():
                    if len(cookies) > 1:
                        logger.info(f"  - {name}: {len(cookies)}个 (不同path)")
                    else:
                        logger.info(f"  - {name}: {cookies[0]['value'][:20]}... (path: {cookies[0]['path']})")

                # 显示Cookie字符串长度
                logger.info(f"📋 完整Cookie字符串长度: {len(self.cookie_string)} 字符")

            else:
                logger.warning(f"⚠️ Cookie文件不存在: {cookie_file_path}")
                logger.info("🔄 使用默认Cookie配置")
                self._set_default_cookies()

        except Exception as e:
            logger.error(f"❌ 加载Cookie文件失败: {e}")
            logger.info("🔄 使用默认Cookie配置")
            self._set_default_cookies()

    
    def update_cookies(self, cookie_string: str):
        """
        更新Cookie

        Args:
            cookie_string: Cookie字符串，格式如 "key1=value1; key2=value2"
        """
        try:
            cookie_pairs = cookie_string.split('; ')
            for pair in cookie_pairs:
                if '=' in pair:
                    key, value = pair.split('=', 1)
                    self.cookies[key] = value

            # 重新生成Cookie字符串并更新headers
            new_cookie_pairs = []
            for name, value in self.cookies.items():
                new_cookie_pairs.append(f"{name}={value}")

            self.cookie_string = "; ".join(new_cookie_pairs)
            self.headers['Cookie'] = self.cookie_string

            logger.info("✅ Cookie更新成功")
        except Exception as e:
            logger.error(f"❌ Cookie更新失败: {e}")

    def fetch_audit_track_history(self, work_order_msg_id: str) -> Optional[Dict[str, Any]]:
        """
        获取甄选审核跟踪历史数据
        
        Args:
            work_order_msg_id: 工单消息ID
            
        Returns:
            Dict: API响应数据，失败返回None
        """
        url = f"{self.base_url}/partner/materialManage/pnrSelectProject/querySelectAuditTrackHistory"
        
        # 构造请求参数
        payload = {
            "workOrderMsgId": work_order_msg_id
        }
        
        try:
            logger.info(f"🔄 开始获取审核跟踪历史数据，工单ID: {work_order_msg_id}")
            
            # 发送请求（Cookie已在headers中设置）
            response = self.session.post(
                url,
                json=payload,
                headers=self.headers,
                timeout=30,
                verify=False  # 忽略SSL证书验证
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析JSON响应
            data = response.json()
            
            # 检查业务状态码
            if data.get('code') != '000000':
                logger.error(f"❌ API返回错误: {data.get('message', '未知错误')}")
                return None
            
            logger.info(f"✅ 数据获取成功，响应码: {data.get('code')}")
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 网络请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON解析失败: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ 获取数据失败: {e}")
            return None

    def transform_audit_track_data(self,
                                  response_data: Dict[str, Any],
                                  work_order_msg_id: str,
                                  request_params: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        转换审核跟踪历史数据为数据库格式

        Args:
            response_data: 完整响应数据
            work_order_msg_id: 工单消息ID
            request_params: 请求参数

        Returns:
            List[Dict]: 转换后的数据库记录列表（每个审核步骤一条记录）
        """
        result_body = response_data.get('resultBody', {})

        # 解析时间字段
        def parse_datetime(date_str):
            if not date_str:
                return None
            try:
                # 尝试多种时间格式
                if '年' in str(date_str) and '月' in str(date_str) and '日' in str(date_str):
                    # 处理 "2025年03月25日" 格式
                    return datetime.strptime(str(date_str), '%Y年%m月%d日')
                else:
                    # 处理 "2025-03-25 09:52:35" 格式
                    return datetime.strptime(str(date_str), '%Y-%m-%d %H:%M:%S')
            except:
                return None

        # 基础数据
        base_data = {
            # API请求参数
            'work_order_msg_id': work_order_msg_id,
            'request_params': json.dumps(request_params) if request_params else None,

            # 响应基础信息（根级字段）
            'busi_date': parse_datetime(response_data.get('busiDate')),
            'code': response_data.get('code'),
            'message': response_data.get('message'),

            # resultBody基础信息
            'own_opinion': result_body.get('ownOpinion'),
            'doc_title': result_body.get('docTitle'),
            'last_approval_end_time': parse_datetime(result_body.get('lastApprovalEndTime')),
            'drafting_unit': result_body.get('DRAFTING_UNIT'),
            'distribute_unit': json.dumps(result_body.get('DISTRIBUTE_UNIT', []), ensure_ascii=False) if result_body.get('DISTRIBUTE_UNIT') else None,
            'drafting_staff_name': result_body.get('DRAFTING_STAFF_NAME'),
            'drafting_staff_tel': result_body.get('DRAFTING_STAFF_TEL'),
            'approval_end_time': result_body.get('APPROVAL_END_TIME'),

            # 原始数据
            'raw_data': json.dumps(response_data, ensure_ascii=False)
        }

        # 处理CUR_REVIEW_LEVEL_LIST数组，每个审核步骤生成一条记录
        records = []
        review_level_list = result_body.get('CUR_REVIEW_LEVEL_LIST', [])

        if not review_level_list:
            # 如果没有审核级别列表，创建一条基础记录
            records.append(base_data.copy())
        else:
            # 为每个审核步骤创建一条记录
            for review_level in review_level_list:
                record = base_data.copy()

                # 添加审核级别特定数据
                record.update({
                    'total_seq': review_level.get('TOTAL_SEQ'),
                    'cur_review_id': review_level.get('CUR_REVIEW_ID'),
                    'cur_review_name': review_level.get('CUR_REVIEW_NAME'),
                    'cur_riview_role_id': review_level.get('CUR_RIVIEW_ROLE_ID'),
                    'cur_riview_role_name': review_level.get('CUR_RIVIEW_ROLE_NAME'),
                    'cur_riview_oper_id': review_level.get('CUR_RIVIEW_OPER_ID'),
                    'cur_riview_oper_name': review_level.get('CUR_RIVIEW_OPER_NAME'),
                    'cur_riview_oper_class': review_level.get('CUR_RIVIEW_OPER_CLASS'),
                    'riview_state': review_level.get('RIVIEW_STATE'),
                    'end_date': parse_datetime(review_level.get('END_DATE')),
                    'flow_define_id': review_level.get('FLOW_DEFINE_ID'),
                    'flow_name': review_level.get('FLOW_NAME'),
                    'cur_review_level': review_level.get('CUR_REVIEW_LEVEL'),
                    'cur_review_level_name': review_level.get('CUR_REVIEW_LEVEL_NAME'),
                    'comment': review_level.get('COMMENT'),

                    # 审核级别原始数据
                    'review_level_raw_data': json.dumps(review_level, ensure_ascii=False)
                })

                records.append(record)

        return records

    def insert_record(self, record_data: Dict[str, Any]) -> bool:
        """
        插入单条记录到数据库

        Args:
            record_data: 记录数据

        Returns:
            bool: 插入是否成功
        """
        sql = """
        INSERT INTO zhenxuan_queryselectaudittrackhistory (
            work_order_msg_id, request_params, busi_date, code, message,
            own_opinion, doc_title, last_approval_end_time, drafting_unit, distribute_unit,
            drafting_staff_name, drafting_staff_tel, approval_end_time,
            total_seq, cur_review_id, cur_review_name, cur_riview_role_id, cur_riview_role_name,
            cur_riview_oper_id, cur_riview_oper_name, cur_riview_oper_class, riview_state,
            end_date, flow_define_id, flow_name, cur_review_level, cur_review_level_name,
            comment, raw_data, review_level_raw_data
        ) VALUES (
            %(work_order_msg_id)s, %(request_params)s, %(busi_date)s, %(code)s, %(message)s,
            %(own_opinion)s, %(doc_title)s, %(last_approval_end_time)s, %(drafting_unit)s, %(distribute_unit)s,
            %(drafting_staff_name)s, %(drafting_staff_tel)s, %(approval_end_time)s,
            %(total_seq)s, %(cur_review_id)s, %(cur_review_name)s, %(cur_riview_role_id)s, %(cur_riview_role_name)s,
            %(cur_riview_oper_id)s, %(cur_riview_oper_name)s, %(cur_riview_oper_class)s, %(riview_state)s,
            %(end_date)s, %(flow_define_id)s, %(flow_name)s, %(cur_review_level)s, %(cur_review_level_name)s,
            %(comment)s, %(raw_data)s, %(review_level_raw_data)s
        ) ON DUPLICATE KEY UPDATE
            request_params = VALUES(request_params),
            busi_date = VALUES(busi_date),
            code = VALUES(code),
            message = VALUES(message),
            own_opinion = VALUES(own_opinion),
            doc_title = VALUES(doc_title),
            last_approval_end_time = VALUES(last_approval_end_time),
            drafting_unit = VALUES(drafting_unit),
            distribute_unit = VALUES(distribute_unit),
            drafting_staff_name = VALUES(drafting_staff_name),
            drafting_staff_tel = VALUES(drafting_staff_tel),
            approval_end_time = VALUES(approval_end_time),
            cur_review_id = VALUES(cur_review_id),
            cur_review_name = VALUES(cur_review_name),
            cur_riview_role_id = VALUES(cur_riview_role_id),
            cur_riview_role_name = VALUES(cur_riview_role_name),
            cur_riview_oper_id = VALUES(cur_riview_oper_id),
            cur_riview_oper_name = VALUES(cur_riview_oper_name),
            cur_riview_oper_class = VALUES(cur_riview_oper_class),
            riview_state = VALUES(riview_state),
            end_date = VALUES(end_date),
            flow_define_id = VALUES(flow_define_id),
            flow_name = VALUES(flow_name),
            cur_review_level = VALUES(cur_review_level),
            cur_review_level_name = VALUES(cur_review_level_name),
            comment = VALUES(comment),
            raw_data = VALUES(raw_data),
            review_level_raw_data = VALUES(review_level_raw_data),
            updated_at = CURRENT_TIMESTAMP
        """

        try:
            with self.db_manager.get_cursor() as cursor:
                cursor.execute(sql, record_data)
                self.db_manager.connection.commit()
                return True
        except Exception as e:
            logger.error(f"❌ 插入记录失败: {e}")
            logger.error(f"记录数据: {record_data.get('work_order_msg_id', 'Unknown')} - {record_data.get('total_seq', 'Unknown')}")
            return False

    def get_work_order_msg_ids(self) -> List[str]:
        """
        从 zhenxuan_queryPartnerSelectDetail 表获取所有 workOrderMsgId

        Returns:
            List[str]: workOrderMsgId 列表
        """
        if not self.db_manager.connect():
            return []

        try:
            sql = """
            SELECT DISTINCT work_order_msg_id
            FROM zhenxuan_querypartnerselectdetail
            WHERE work_order_msg_id IS NOT NULL
            AND work_order_msg_id != ''
            ORDER BY work_order_msg_id DESC
            """

            with self.db_manager.get_cursor() as cursor:
                cursor.execute(sql)
                results = cursor.fetchall()

                work_order_ids = [row['work_order_msg_id'] for row in results]
                logger.info(f"📋 从数据库获取到 {len(work_order_ids)} 个工单ID")
                return work_order_ids

        except Exception as e:
            logger.error(f"❌ 获取工单ID失败: {e}")
            return []
        finally:
            self.db_manager.disconnect()

    def sync_single_work_order(self, work_order_msg_id: str) -> int:
        """
        同步单个工单的审核跟踪历史数据

        Args:
            work_order_msg_id: 工单消息ID

        Returns:
            int: 同步成功的记录数
        """
        logger.info(f"🔄 开始同步工单 {work_order_msg_id} 的审核跟踪历史...")

        # 获取数据
        response_data = self.fetch_audit_track_history(work_order_msg_id)
        if not response_data:
            logger.error(f"❌ 获取工单 {work_order_msg_id} 数据失败")
            return 0

        # 构造请求参数用于记录
        request_params = {
            "workOrderMsgId": work_order_msg_id
        }

        # 转换数据格式
        try:
            records = self.transform_audit_track_data(response_data, work_order_msg_id, request_params)

            if not records:
                logger.warning(f"⚠️ 工单 {work_order_msg_id} 没有审核跟踪数据")
                return 0

            # 插入数据库
            synced_count = 0
            for record in records:
                if self.insert_record(record):
                    synced_count += 1

            logger.info(f"✅ 工单 {work_order_msg_id} 同步完成: {synced_count}/{len(records)} 条记录")
            return synced_count

        except Exception as e:
            logger.error(f"❌ 处理工单 {work_order_msg_id} 数据失败: {e}")
            return 0

    def sync_all_data(self, work_order_msg_id: Optional[str] = None) -> int:
        """
        同步所有审核跟踪历史数据

        Args:
            work_order_msg_id: 指定工单ID，如果为None则同步所有工单

        Returns:
            int: 同步成功的记录数
        """
        logger.info("🚀 开始同步甄选审核跟踪历史数据...")

        if not self.db_manager.connect():
            logger.error("❌ 数据库连接失败")
            return 0

        total_synced = 0

        try:
            if work_order_msg_id:
                # 同步指定工单
                work_order_ids = [work_order_msg_id]
                logger.info(f"📋 指定工单模式: {work_order_msg_id}")
            else:
                # 获取所有工单ID
                work_order_ids = self.get_work_order_msg_ids()
                if not work_order_ids:
                    logger.warning("⚠️ 没有找到可同步的工单ID")
                    return 0
                logger.info(f"📋 轮询模式: 共 {len(work_order_ids)} 个工单")

            # 逐个同步工单
            for i, work_order_id in enumerate(work_order_ids, 1):
                logger.info(f"📋 [{i}/{len(work_order_ids)}] 处理工单: {work_order_id}")

                synced_count = self.sync_single_work_order(work_order_id)
                total_synced += synced_count

                # 添加延迟避免请求过快
                if i < len(work_order_ids):  # 最后一个不需要延迟
                    import time
                    time.sleep(1)

            logger.info(f"🎉 数据同步完成，成功同步 {total_synced} 条记录")
            return total_synced

        except Exception as e:
            logger.error(f"❌ 数据同步异常: {e}")
            return total_synced
        finally:
            self.db_manager.disconnect()

    def query_data(self, work_order_msg_id: Optional[str] = None, limit: int = 10) -> List[Dict[str, Any]]:
        """
        查询数据库中的数据

        Args:
            work_order_msg_id: 工单消息ID，可选
            limit: 查询记录数限制

        Returns:
            List[Dict]: 查询结果
        """
        if not self.db_manager.connect():
            return []

        try:
            if work_order_msg_id:
                sql = """
                SELECT
                    id, work_order_msg_id, total_seq, cur_riview_oper_name,
                    cur_riview_oper_class, cur_review_level_name, riview_state,
                    end_date, flow_name, comment, created_at
                FROM zhenxuan_queryselectaudittrackhistory
                WHERE work_order_msg_id = %s
                ORDER BY CAST(total_seq AS UNSIGNED)
                LIMIT %s
                """
                params = (work_order_msg_id, limit)
            else:
                sql = """
                SELECT
                    id, work_order_msg_id, total_seq, cur_riview_oper_name,
                    cur_riview_oper_class, cur_review_level_name, riview_state,
                    end_date, flow_name, comment, created_at
                FROM zhenxuan_queryselectaudittrackhistory
                ORDER BY created_at DESC, work_order_msg_id, CAST(total_seq AS UNSIGNED)
                LIMIT %s
                """
                params = (limit,)

            with self.db_manager.get_cursor() as cursor:
                cursor.execute(sql, params)
                return cursor.fetchall()

        except Exception as e:
            logger.error(f"❌ 查询数据失败: {e}")
            return []
        finally:
            self.db_manager.disconnect()

def main():
    """主程序入口"""
    import argparse

    parser = argparse.ArgumentParser(description='甄选审核跟踪历史数据获取和入库程序')
    parser.add_argument('--work-order-msg-id', type=str, help='指定工单消息ID')
    parser.add_argument('--cookie-file', type=str, help='Cookie文件路径，默认使用cookies/cookies_dict_zhenxuan.json')
    parser.add_argument('--cookie', type=str, help='更新Cookie字符串')
    parser.add_argument('--query', action='store_true', help='查询已同步的数据')
    parser.add_argument('--limit', type=int, default=10, help='查询记录数限制，默认10')
    parser.add_argument('--all', action='store_true', help='轮询所有工单模式，从数据库获取所有工单ID并同步')

    args = parser.parse_args()

    # 创建数据获取器（使用指定的Cookie文件）
    fetcher = ZhenxuanAuditTrackFetcher(cookie_file_path=args.cookie_file)

    # 更新Cookie（如果提供）
    if args.cookie:
        fetcher.update_cookies(args.cookie)

    # 查询模式
    if args.query:
        logger.info("🔍 查询数据库中的审核跟踪历史数据...")
        results = fetcher.query_data(args.work_order_msg_id, args.limit)

        if results:
            logger.info(f"📋 查询到 {len(results)} 条记录:")
            current_work_order = None
            for i, record in enumerate(results, 1):
                if record['work_order_msg_id'] != current_work_order:
                    current_work_order = record['work_order_msg_id']
                    logger.info(f"\n🔖 工单: {current_work_order}")

                logger.info(f"  {record['total_seq']}. {record['cur_riview_oper_name']} ({record['cur_riview_oper_class']})")
                logger.info(f"     级别: {record['cur_review_level_name']}")
                logger.info(f"     状态: {record['riview_state']} | 时间: {record['end_date']}")
                logger.info(f"     意见: {record['comment']}")
                logger.info(f"     入库: {record['created_at']}")
                logger.info("")
        else:
            logger.info("📋 没有查询到数据")
        return

    # 全部数据同步模式
    if args.all:
        logger.info("🌍 轮询所有工单模式启动...")
        logger.info("📋 将从数据库获取所有工单ID并同步审核跟踪历史数据")

        # 执行全部数据同步
        synced_count = fetcher.sync_all_data()

        if synced_count > 0:
            logger.info(f"🎉 全部数据同步成功！共同步 {synced_count} 条记录")
        else:
            logger.error("❌ 全部数据同步失败或没有新数据")
        return

    # 指定工单同步模式
    if args.work_order_msg_id:
        logger.info("🎯 指定工单同步模式启动...")
        logger.info(f"📋 工单ID: {args.work_order_msg_id}")

        # 执行指定工单同步
        synced_count = fetcher.sync_all_data(work_order_msg_id=args.work_order_msg_id)

        if synced_count > 0:
            logger.info(f"🎉 工单数据同步成功！共同步 {synced_count} 条记录")
        else:
            logger.error("❌ 工单数据同步失败或没有新数据")
        return

    # 默认模式：显示帮助
    parser.print_help()
    logger.info("\n💡 使用示例:")
    logger.info("  python fetch_querySelectAuditTrackHistory.py --all                    # 轮询所有工单")
    logger.info("  python fetch_querySelectAuditTrackHistory.py --work-order-msg-id GD76020250325095234501317  # 指定工单")
    logger.info("  python fetch_querySelectAuditTrackHistory.py --query                 # 查询数据")
    logger.info("  python fetch_querySelectAuditTrackHistory.py --query --work-order-msg-id GD76020250325095234501317  # 查询指定工单")

if __name__ == "__main__":
    main()
