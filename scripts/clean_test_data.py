#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理 zhenxuan_queryPartnerSelectDetail 表的测试数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import get_db_manager
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def clean_test_data():
    """清理测试数据"""
    try:
        db = get_db_manager()
        db.connect()

        # 查看当前数据
        results = db.execute_query('SELECT COUNT(*) as count FROM zhenxuan_querypartnerselectdetail')
        current_count = results[0]['count']
        print(f'📊 当前表中记录数: {current_count}')

        if current_count > 0:
            # 查看具体数据
            data = db.execute_query('SELECT id, project_name, select_msg_id FROM zhenxuan_querypartnerselectdetail ORDER BY id')
            print('\n📋 当前数据:')
            for row in data:
                project_name = row['project_name'][:30] + '...' if len(row['project_name']) > 30 else row['project_name']
                print(f'  ID: {row["id"]}, 项目: {project_name}, selectMsgId: {row["select_msg_id"]}')
            
            # 确认是否清理
            confirm = input('\n❓ 是否清理所有测试数据? (y/N): ')
            if confirm.lower() == 'y':
                # 清理数据
                affected_rows = db.execute_update('DELETE FROM zhenxuan_querypartnerselectdetail')
                print(f'✅ 已清理 {affected_rows} 条测试数据')
                
                # 重置自增ID
                db.execute_update('ALTER TABLE zhenxuan_querypartnerselectdetail AUTO_INCREMENT = 1')
                print('✅ 已重置自增ID')
            else:
                print('❌ 取消清理操作')
        else:
            print('✅ 表中没有数据，无需清理')
            
    except Exception as e:
        logger.error(f'❌ 清理数据失败: {e}')

if __name__ == '__main__':
    clean_test_data()
