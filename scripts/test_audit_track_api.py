#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试甄选审核跟踪历史API连接
"""

import requests
import json
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_cookies():
    """加载Cookie文件"""
    cookie_file = "cookies/cookies_dict_zhenxuan.json"
    
    try:
        with open(cookie_file, 'r', encoding='utf-8') as f:
            cookie_data = json.load(f)
        
        # 生成Cookie字符串
        cookie_pairs = []
        for cookie in cookie_data:
            name = cookie['name']
            value = cookie['value']
            cookie_pairs.append(f"{name}={value}")
        
        cookie_string = "; ".join(cookie_pairs)
        logger.info(f"✅ 加载Cookie成功，共 {len(cookie_data)} 个Cookie")
        return cookie_string
        
    except Exception as e:
        logger.error(f"❌ 加载Cookie失败: {e}")
        return None

def test_api_connection(work_order_msg_id="GD76020250325095234501317"):
    """测试API连接"""

    # 使用curl命令中的Cookie字符串
    cookie_string = "BSS-SESSION=NjU3M2VhYjYtNWRlYi00NGMzLTg1OWQtZDg2YzNiMjdkZThi; NewoaAppToDones=; NewoaAppToReads=; BSS-SESSION=OTkyMWJiNmMtMWNjMi00OGM0LTllYjAtNmI0M2Q0ODA2NTEy; isLogin=ImlzTG9naW4i; requestId=eb0331f0-5bec-11f0-b2ab-d7d92ba136f7; systemUserCode=InpoZW5nZGV3ZW4i; jsession_id_4_boss=n603BE9BD8C39CA4832672F7B13F2B61E-1"

    logger.info(f"✅ 使用curl命令中的Cookie字符串")
    
    # 请求头
    headers = {
        'Host': "dict.gmcc.net:30722",
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        'Accept': "application/json, text/plain, */*",
        'Accept-Encoding': "gzip, deflate, br, zstd",
        'Content-Type': "application/json;charset=UTF-8",
        'Authorization': "Bearer d25c514c-e026-4ddf-b455-9929dfcd3cfb",
        'sec-ch-ua-platform': '"Windows"',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': "?0",
        'Origin': "https://dict.gmcc.net:30722",
        'Sec-Fetch-Site': "same-origin",
        'Sec-Fetch-Mode': "cors",
        'Sec-Fetch-Dest': "empty",
        'Referer': "https://dict.gmcc.net:30722/ptn/main/selectDemand/detail",
        'Accept-Language': "zh-CN,zh;q=0.9,ee;q=0.8",
        'Cookie': cookie_string
    }
    
    # API URL
    url = "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectAuditTrackHistory"
    
    # 请求数据
    payload = {
        "workOrderMsgId": work_order_msg_id
    }
    
    try:
        logger.info(f"🔄 测试API连接...")
        logger.info(f"📋 URL: {url}")
        logger.info(f"📋 工单ID: {work_order_msg_id}")
        logger.info(f"📋 Cookie长度: {len(cookie_string)} 字符")
        
        # 发送请求
        response = requests.post(
            url,
            data=json.dumps(payload),
            headers=headers,
            timeout=30,
            verify=False  # 忽略SSL证书验证
        )
        
        logger.info(f"📋 响应状态码: {response.status_code}")
        logger.info(f"📋 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                logger.info(f"✅ API调用成功")
                logger.info(f"📋 响应代码: {data.get('code', 'N/A')}")
                logger.info(f"📋 响应消息: {data.get('message', 'N/A')}")
                
                if data.get('code') == '000000':
                    result_body = data.get('resultBody', {})
                    review_list = result_body.get('CUR_REVIEW_LEVEL_LIST', [])
                    logger.info(f"📋 审核步骤数量: {len(review_list)}")
                    
                    if review_list:
                        logger.info("📋 前3个审核步骤:")
                        for i, review in enumerate(review_list[:3], 1):
                            logger.info(f"  {i}. {review.get('CUR_RIVIEW_OPER_NAME', 'N/A')} - {review.get('CUR_REVIEW_LEVEL_NAME', 'N/A')}")
                    
                    return True
                else:
                    logger.error(f"❌ API返回错误: {data.get('message', '未知错误')}")
                    return False
                    
            except json.JSONDecodeError as e:
                logger.error(f"❌ JSON解析失败: {e}")
                logger.info(f"📋 响应内容: {response.text[:500]}...")
                return False
        else:
            logger.error(f"❌ HTTP请求失败: {response.status_code}")
            logger.info(f"📋 响应内容: {response.text[:500]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        logger.error(f"❌ 网络请求异常: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 未知错误: {e}")
        return False

def main():
    """主程序"""
    import argparse
    
    parser = argparse.ArgumentParser(description='测试甄选审核跟踪历史API')
    parser.add_argument('--work-order-msg-id', type=str, 
                       default='GD76020250325095234501317',
                       help='工单消息ID')
    
    args = parser.parse_args()
    
    logger.info("🚀 开始测试甄选审核跟踪历史API...")
    
    success = test_api_connection(args.work_order_msg_id)
    
    if success:
        logger.info("🎉 API测试成功！")
    else:
        logger.error("❌ API测试失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
