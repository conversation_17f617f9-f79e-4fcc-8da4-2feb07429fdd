"""
本地审核跟踪历史数据获取程序（KSM版本）
根据 queryLocalAuditTrackHistory 接口获取审核跟踪历史数据并入库
特别增加 selectRevId 字段，从 zhenxuan_queryPartnerSelectDetail 表获取入参
"""

import os
import sys
import json
import requests
import logging
import time
import argparse
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from database.db_config import ZHENXUAN_DB_CONFIG, DatabaseManager
from auth_loader import AuthLoader

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/fetch_queryLocalAuditTrackHistory_ksm.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class QueryLocalAuditTrackHistoryKsmFetcher:
    """本地审核跟踪历史数据获取器（KSM版本）"""
    
    def __init__(self, cookie_file_path=None):
        """初始化"""
        self.base_url = "https://dict.gmcc.net:30722"
        self.api_endpoint = "/partner/materialManage/pnrSelectProject/queryLocalAuditTrackHistory"
        self.db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
        self.session = requests.Session()
        # 使用 AuthLoader 加载认证信息
        auth_loader = AuthLoader(cookie_file_path)
        if auth_loader.load_auth_data():
            # 更新session的认证信息
            auth_loader.update_session(self.session)
            logger.info("✅ 认证信息加载成功")
        else:
            logger.error("❌ 认证信息加载失败")
            
        # 设置基本请求头（AuthLoader会自动添加认证相关的headers）
        self.session.headers.update({
            'Host': "dict.gmcc.net:30722",
            'Origin': "http://dict.gmcc.net:30722",
            'Referer': "http://dict.gmcc.net:30722/ptn/main/selectDemand",
        })
        # 禁用SSL证书验证
        self.session.verify = False
        # 禁用SSL警告
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        
        
        # 初始化Cookie
        self.cookies = {}
        self.cookie_string = ""

        # 设置默认Cookie文件路径
        if cookie_file_path is None:
            # 查找项目根目录下的cookies文件
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            cookie_file_path = os.path.join(project_root, 'cookies', 'cookies_dict_zhenxuan.json')

        # 加载Cookie
        

    def load_cookies_from_file(self, cookie_file_path: str):
        """
        从JSON文件加载Cookie，转换为Cookie字符串格式
        保留所有Cookie，包括同名但不同path的Cookie

        Args:
            cookie_file_path: Cookie文件路径
        """
        try:
            if os.path.exists(cookie_file_path):
                with open(cookie_file_path, 'r', encoding='utf-8') as f:
                    cookie_data = json.load(f)

                # 生成Cookie字符串（保留所有Cookie，包括同名的）
                cookie_pairs = []
                cookie_dict = {}  # 用于显示和统计

                for cookie in cookie_data:
                    name = cookie['name']
                    value = cookie['value']
                    path = cookie.get('path', '/')

                    # 添加到Cookie字符串（所有Cookie都要包含）
                    cookie_pairs.append(f"{name}={value}")

                    # 用于显示的字典（同名Cookie显示最后一个，但实际都会发送）
                    if name not in cookie_dict:
                        cookie_dict[name] = []
                    cookie_dict[name].append({'value': value, 'path': path})

                self.cookie_string = "; ".join(cookie_pairs)
                self.headers['Cookie'] = self.cookie_string

                logger.info(f"✅ 成功加载Cookie文件: {cookie_file_path}")
                logger.info(f"📊 Cookie统计: {len(cookie_data)} 个Cookie项")
                
                # 显示重要的Cookie信息
                important_cookies = ['BSS-SESSION', 'jsession_id_4_boss', 'isLogin']
                for cookie_name in important_cookies:
                    if cookie_name in cookie_dict:
                        logger.info(f"🔑 {cookie_name}: {len(cookie_dict[cookie_name])} 个值")
                
            else:
                logger.warning(f"⚠️ Cookie文件不存在: {cookie_file_path}")
                
        except Exception as e:
            logger.error(f"❌ 加载Cookie文件失败: {e}")

    def update_cookies(self, cookie_string: str):
        """
        更新Cookie字符串

        Args:
            cookie_string: Cookie字符串，格式: "key1=value1; key2=value2"
        """
        self.cookie_string = cookie_string
        self.headers['Cookie'] = cookie_string
        logger.info("✅ Cookie已更新")

    def get_partner_select_details(self) -> List[Dict[str, Any]]:
        """
        从 zhenxuan_queryPartnerSelectDetail 表获取 selectRevId 和 workOrderMsgId
        
        Returns:
            List[Dict]: 包含 selectRevId 和 workOrderMsgId 的记录列表
        """
        try:
            if not self.db_manager.connect():
                return []
            
            sql = """
            SELECT
                select_rev_id_detail as selectRevId,
                work_order_msg_id as workOrderMsgId,
                project_msg_id,
                project_name,
                select_name,
                created_at
            FROM zhenxuan_queryPartnerSelectDetail
            WHERE select_rev_id_detail IS NOT NULL
            AND select_rev_id_detail != ''
            GROUP BY select_rev_id_detail, work_order_msg_id, project_msg_id, project_name, select_name, created_at
            ORDER BY created_at DESC
            """
            
            with self.db_manager.get_cursor() as cursor:
                cursor.execute(sql)
                results = cursor.fetchall()
                
                logger.info(f"📋 从合作伙伴详情表获取到 {len(results)} 条记录")
                return results
                
        except Exception as e:
            logger.error(f"❌ 获取合作伙伴详情失败: {e}")
            return []
        finally:
            self.db_manager.disconnect()

    def fetch_audit_track_data(self, select_rev_id: str, work_order_msg_id: str = None) -> Optional[Dict[str, Any]]:
        """
        获取审核跟踪历史数据
        
        Args:
            select_rev_id: 甄选版本ID（作为businessId）
            work_order_msg_id: 工单消息ID
            
        Returns:
            Dict: API响应数据
        """
        url = f"{self.base_url}{self.api_endpoint}"
        
        # 构建请求参数
        payload = {
            "businessId": select_rev_id,  # 使用selectRevId作为businessId
            "workOrderMsgId": work_order_msg_id,  # 使用workOrderMsgId
            "stepName": ""  # 固定为空字符串
        }
        
        try:
            logger.info(f"🔄 开始获取审核跟踪数据")
            logger.info(f"📋 selectRevId: {select_rev_id}")
            logger.info(f"📋 workOrderMsgId: {work_order_msg_id}")
            
            # 发送请求（Cookie已在headers中设置）
            response = self.session.post(
                url,
                json=payload,
                headers=self.headers,
                timeout=30
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析JSON响应
            data = response.json()
            
            # 检查业务状态码
            if data.get('code') != '000000':
                logger.error(f"❌ API返回错误: {data.get('message', '未知错误')}")
                return None
            
            logger.info(f"✅ 数据获取成功，响应码: {data.get('code')}")
            logger.info(f"📊 获取到 {len(data.get('resultBody', []))} 条审核记录")
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 网络请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON解析失败: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ 获取数据失败: {e}")
            return None

    def save_audit_track_data(self, select_rev_id: str, work_order_msg_id: str, data: Dict[str, Any]) -> bool:
        """
        保存审核跟踪历史数据到数据库

        Args:
            select_rev_id: 甄选版本ID
            work_order_msg_id: 工单消息ID
            data: API响应数据

        Returns:
            bool: 保存是否成功
        """
        try:
            if not self.db_manager.connect():
                return False

            # 获取resultBody数据
            result_body = data.get('resultBody', [])
            if not result_body:
                logger.warning("⚠️ 没有审核跟踪数据需要保存")
                return True

            # 构建插入SQL
            sql = """
            INSERT INTO zhenxuan_queryLocalAuditTrackHistory_ksm (
                select_rev_id, business_id, work_order_msg_id, step_name_filter, request_params,
                busi_date, code, message,
                audit_process_track_id, step_name, create_time, finish_time,
                status, audit_handler, audit_remark, raw_data
            ) VALUES (
                %s, %s, %s, %s, %s,
                %s, %s, %s,
                %s, %s, %s, %s,
                %s, %s, %s, %s
            ) ON DUPLICATE KEY UPDATE
                busi_date = VALUES(busi_date),
                code = VALUES(code),
                message = VALUES(message),
                step_name = VALUES(step_name),
                create_time = VALUES(create_time),
                finish_time = VALUES(finish_time),
                status = VALUES(status),
                audit_handler = VALUES(audit_handler),
                audit_remark = VALUES(audit_remark),
                raw_data = VALUES(raw_data),
                updated_at = CURRENT_TIMESTAMP
            """

            # 准备批量插入数据
            insert_data = []
            for record in result_body:
                # 解析时间字段
                create_time = self.parse_datetime(record.get('createTime'))
                finish_time = self.parse_datetime(record.get('finishTime'))
                busi_date = self.parse_datetime(data.get('busiDate'))

                # 构建请求参数JSON
                request_params = {
                    "businessId": select_rev_id,
                    "workOrderMsgId": work_order_msg_id,
                    "stepName": ""
                }

                insert_data.append((
                    select_rev_id,  # select_rev_id
                    select_rev_id,  # business_id (使用selectRevId作为businessId)
                    work_order_msg_id,  # work_order_msg_id
                    "",  # step_name_filter (固定为空字符串)
                    json.dumps(request_params, ensure_ascii=False),  # request_params
                    busi_date,  # busi_date
                    data.get('code'),  # code
                    data.get('message'),  # message
                    record.get('auditProcessTrackId'),  # audit_process_track_id
                    record.get('stepName'),  # step_name
                    create_time,  # create_time
                    finish_time,  # finish_time
                    record.get('status'),  # status
                    record.get('auditHandler'),  # audit_handler
                    record.get('auditRemark'),  # audit_remark
                    json.dumps(record, ensure_ascii=False)  # raw_data
                ))

            # 批量插入数据
            with self.db_manager.get_cursor() as cursor:
                cursor.executemany(sql, insert_data)
                self.db_manager.connection.commit()

                logger.info(f"✅ 成功保存 {len(insert_data)} 条审核跟踪记录")
                return True

        except Exception as e:
            logger.error(f"❌ 保存审核跟踪数据失败: {e}")
            return False
        finally:
            self.db_manager.disconnect()

    def parse_datetime(self, datetime_str: str) -> Optional[datetime]:
        """
        解析日期时间字符串

        Args:
            datetime_str: 日期时间字符串

        Returns:
            datetime: 解析后的日期时间对象
        """
        if not datetime_str:
            return None

        try:
            # 尝试解析格式: "2025-03-25 09:52:34"
            return datetime.strptime(datetime_str, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            try:
                # 尝试解析格式: "2025-07-09 01:44:46"
                return datetime.strptime(datetime_str, "%Y-%m-%d %H:%M:%S")
            except ValueError:
                logger.warning(f"⚠️ 无法解析日期时间: {datetime_str}")
                return None

    def sync_all_data(self, limit: int = None) -> int:
        """
        同步所有审核跟踪历史数据

        Args:
            limit: 限制处理的记录数，None表示处理所有记录

        Returns:
            int: 成功同步的记录数
        """
        logger.info("🚀 开始同步所有审核跟踪历史数据...")

        # 获取合作伙伴详情数据
        partner_details = self.get_partner_select_details()
        if not partner_details:
            logger.error("❌ 没有获取到合作伙伴详情数据")
            return 0

        # 限制处理数量
        if limit:
            partner_details = partner_details[:limit]
            logger.info(f"📋 限制处理 {limit} 条记录")

        success_count = 0
        total_count = len(partner_details)

        for i, detail in enumerate(partner_details, 1):
            select_rev_id = detail['selectRevId']
            work_order_msg_id = detail['workOrderMsgId']
            project_name = detail.get('project_name', 'N/A')

            logger.info(f"📋 处理第 {i}/{total_count} 条记录")
            logger.info(f"   项目: {project_name}")
            logger.info(f"   selectRevId: {select_rev_id}")

            try:
                # 获取审核跟踪数据
                audit_data = self.fetch_audit_track_data(select_rev_id, work_order_msg_id)

                if audit_data:
                    # 保存数据
                    if self.save_audit_track_data(select_rev_id, work_order_msg_id, audit_data):
                        success_count += 1
                        logger.info(f"✅ 第 {i} 条记录处理成功")
                    else:
                        logger.error(f"❌ 第 {i} 条记录保存失败")
                else:
                    logger.warning(f"⚠️ 第 {i} 条记录获取数据失败")

                # 添加延迟，避免请求过快
                if i < total_count:
                    time.sleep(1)

            except Exception as e:
                logger.error(f"❌ 处理第 {i} 条记录时发生错误: {e}")
                continue

        logger.info(f"🎉 数据同步完成！成功: {success_count}/{total_count}")
        return success_count

    def query_data(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        查询数据库中的数据

        Args:
            limit: 查询记录数限制

        Returns:
            List[Dict]: 查询结果
        """
        if not self.db_manager.connect():
            return []

        try:
            sql = """
            SELECT
                id, select_rev_id, business_id, work_order_msg_id,
                step_name, status, audit_handler, create_time, finish_time,
                audit_remark, created_at
            FROM zhenxuan_queryLocalAuditTrackHistory_ksm
            ORDER BY created_at DESC
            LIMIT %s
            """

            with self.db_manager.get_cursor() as cursor:
                cursor.execute(sql, (limit,))
                return cursor.fetchall()

        except Exception as e:
            logger.error(f"❌ 查询数据失败: {e}")
            return []
        finally:
            self.db_manager.disconnect()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='本地审核跟踪历史数据获取程序（KSM版本）')
    parser.add_argument('--cookie', type=str, help='Cookie字符串')
    parser.add_argument('--cookie-file', type=str, help='Cookie文件路径')
    parser.add_argument('--limit', type=int, help='限制处理的记录数')
    parser.add_argument('--query', action='store_true', help='查询数据库中的数据')
    parser.add_argument('--query-limit', type=int, default=10, help='查询记录数限制')
    parser.add_argument('--all', action='store_true', help='同步所有数据（无限制）')

    args = parser.parse_args()

    logger.info("=" * 80)
    logger.info("🚀 本地审核跟踪历史数据获取程序（KSM版本）启动")
    logger.info("=" * 80)

    # 创建数据获取器
    fetcher = QueryLocalAuditTrackHistoryKsmFetcher(cookie_file_path=args.cookie_file)

    # 更新Cookie（如果提供）
    if args.cookie:
        fetcher.update_cookies(args.cookie)

    # 查询模式
    if args.query:
        logger.info("🔍 查询数据库中的数据...")
        results = fetcher.query_data(args.query_limit)

        if results:
            logger.info(f"📋 查询到 {len(results)} 条记录:")
            for i, record in enumerate(results, 1):
                logger.info(f"  {i}. selectRevId: {record['select_rev_id']}")
                logger.info(f"     步骤: {record['step_name']}")
                logger.info(f"     状态: {record['status']}")
                logger.info(f"     处理人: {record['audit_handler']}")
                logger.info(f"     创建时间: {record['create_time']}")
                logger.info(f"     完成时间: {record['finish_time']}")
                logger.info("")
        else:
            logger.info("📋 没有查询到数据")
        return

    # 全部数据同步模式
    if args.all:
        logger.info("🌍 全部数据同步模式启动...")
        logger.info("📋 将查询并入库所有可用数据")

        # 执行全部数据同步
        synced_count = fetcher.sync_all_data()

        if synced_count > 0:
            logger.info(f"🎉 全部数据同步成功！共同步 {synced_count} 条记录")
        else:
            logger.error("❌ 全部数据同步失败或没有新数据")
        return

    # 限制数据同步模式
    logger.info("🚀 开始数据同步...")
    logger.info(f"📋 限制记录数: {args.limit or '无限制'}")

    # 执行数据同步
    synced_count = fetcher.sync_all_data(limit=args.limit)

    if synced_count > 0:
        logger.info(f"🎉 数据同步成功！共同步 {synced_count} 条记录")
    else:
        logger.error("❌ 数据同步失败或没有新数据")

    logger.info("=" * 80)
    logger.info("🏁 程序执行完成")
    logger.info("=" * 80)

if __name__ == "__main__":
    main()
