"""
最终版字典网站自动登录脚本
优化验证码识别和登录流程
"""

import asyncio
import time
import os
import re
import json
from datetime import datetime
from playwright.async_api import async_playwright
import ddddocr


class FinalDictLoginBot:
    def __init__(self):
        self.url = "https://dict.gmcc.net:30722/dictWeb/login"
        self.username = "liaochulin"
        self.password = "Liaochulin147!"
        self.ocr = ddddocr.DdddOcr(show_ad=False)
        
        # 创建验证码图片保存目录
        self.captcha_dir = "captcha_images"
        if not os.path.exists(self.captcha_dir):
            os.makedirs(self.captcha_dir)

        # 创建cookie保存目录
        self.cookie_dir = "cookies"
        if not os.path.exists(self.cookie_dir):
            os.makedirs(self.cookie_dir)
        
    async def login(self):
        """执行登录流程"""
        async with async_playwright() as p:
            # 启动浏览器
            browser = await p.chromium.launch(
                headless=False, 
                slow_mo=500,  # 减少延迟
                args=['--disable-blink-features=AutomationControlled']  # 避免被检测为自动化
            )
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                ignore_https_errors=True  # 忽略HTTPS证书错误
            )
            page = await context.new_page()
            
            try:
                print("🌐 正在访问登录页面...")
                await page.goto(self.url, wait_until='networkidle')
                
                # 等待页面完全加载
                await asyncio.sleep(3)
                
                # 执行登录流程
                success = await self.perform_login(page)
                
                if success:
                    print("🎉 登录成功！开始执行菜单点击...")
                    # 执行菜单点击和cookie保存
                    await self.navigate_to_zhenxuan(page)
                    # 保持浏览器打开以便查看结果
                    print("⏰ 保持浏览器打开6秒...")
                    await asyncio.sleep(6)
                else:
                    print("❌ 登录失败")
                    await asyncio.sleep(10)
                
            except Exception as e:
                print(f"❌ 登录过程中出现错误: {e}")
                await asyncio.sleep(10)
                
            finally:
                await browser.close()
    
    async def perform_login(self, page):
        """执行登录操作"""
        max_login_attempts = 3
        
        for login_attempt in range(max_login_attempts):
            print(f"\n🚀 第 {login_attempt + 1} 次登录尝试...")
            
            try:
                # 输入用户名
                print("👤 输入用户名...")
                username_input = await page.wait_for_selector('input[name="username"]', timeout=10000)
                await username_input.fill('')  # 清空
                await username_input.fill(self.username)
                print(f"✅ 用户名已输入: {self.username}")
                
                # 输入密码
                print("🔒 输入密码...")
                password_input = await page.wait_for_selector('input[name="password"]', timeout=10000)
                await password_input.fill('')  # 清空
                await password_input.fill(self.password)
                print("✅ 密码已输入")
                
                # 处理验证码
                captcha_success = await self.handle_captcha_smart(page)
                if not captcha_success:
                    print("❌ 验证码处理失败")
                    continue
                
                # 点击登录按钮
                print("🖱️ 点击登录按钮...")
                login_button = await page.wait_for_selector('button:has-text("登录")', timeout=10000)
                await login_button.click()
                print("✅ 登录按钮已点击")
                
                # 等待登录结果
                print("⏳ 等待登录结果...")
                await asyncio.sleep(3)
                
                # 检查登录结果
                login_success = await self.check_login_success(page)
                if login_success:
                    return True
                else:
                    print(f"❌ 第 {login_attempt + 1} 次登录失败")
                    if login_attempt < max_login_attempts - 1:
                        print("🔄 准备重试...")
                        await asyncio.sleep(3)
                
            except Exception as e:
                print(f"❌ 登录尝试 {login_attempt + 1} 出错: {e}")
                if login_attempt < max_login_attempts - 1:
                    await asyncio.sleep(3)
        
        return False
    
    async def handle_captcha_smart(self, page):
        """智能验证码处理"""
        max_captcha_attempts = 8
        
        for attempt in range(max_captcha_attempts):
            try:
                print(f"🔍 验证码识别尝试 {attempt + 1}/{max_captcha_attempts}")
                
                # 查找验证码图片
                captcha_img = await page.wait_for_selector('img[id="getCodeOfPicture"]', timeout=10000)
                
                # 截取验证码图片
                captcha_bytes = await captcha_img.screenshot()
                
                # 保存验证码图片
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                captcha_filename = f"{self.captcha_dir}/captcha_{timestamp}_{attempt+1}.png"
                with open(captcha_filename, 'wb') as f:
                    f.write(captcha_bytes)
                
                # 使用ddddocr识别验证码
                captcha_text = self.ocr.classification(captcha_bytes)
                
                # 清理识别结果
                captcha_text = self.clean_captcha_text(captcha_text)
                
                print(f"🔤 识别结果: '{captcha_text}' (长度: {len(captcha_text)})")
                
                # 验证码合理性检查
                if not self.is_valid_captcha(captcha_text):
                    print(f"⚠️ 验证码格式不合理，重新识别...")
                    await self.refresh_captcha(page)
                    continue
                
                # 输入验证码
                captcha_input = await page.wait_for_selector('input[name="code"]', timeout=10000)
                await captcha_input.fill('')  # 清空
                await asyncio.sleep(0.3)
                await captcha_input.fill(captcha_text)
                
                # 验证输入
                input_value = await captcha_input.input_value()
                if input_value == captcha_text:
                    print(f"✅ 验证码输入成功: '{input_value}'")
                    return True
                else:
                    print(f"⚠️ 输入验证失败: '{input_value}' != '{captcha_text}'")
                
            except Exception as e:
                print(f"❌ 验证码处理失败: {e}")
            
            # 刷新验证码准备重试
            if attempt < max_captcha_attempts - 1:
                await self.refresh_captcha(page)
                await asyncio.sleep(1)
        
        return False
    
    def clean_captcha_text(self, text):
        """清理验证码识别结果"""
        if not text:
            return ""
        
        # 移除空格和特殊字符
        text = re.sub(r'[^a-zA-Z0-9]', '', text)
        
        # 常见字符替换
        replacements = {
            'O': '0',  # 字母O替换为数字0
            'I': '1',  # 字母I替换为数字1
            'l': '1',  # 小写l替换为数字1
            'S': '5',  # 字母S有时会被误识别
            'G': '6',  # 字母G有时会被误识别
        }
        
        for old, new in replacements.items():
            text = text.replace(old, new)
        
        return text
    
    def is_valid_captcha(self, text):
        """验证码合理性检查"""
        if not text:
            return False
        
        # 长度检查
        if len(text) < 3 or len(text) > 6:
            return False
        
        # 字符检查 - 只允许字母和数字
        if not re.match(r'^[a-zA-Z0-9]+$', text):
            return False
        
        return True
    
    async def refresh_captcha(self, page):
        """刷新验证码"""
        try:
            print("🔄 刷新验证码...")
            captcha_img = await page.query_selector('img[id="getCodeOfPicture"]')
            if captcha_img:
                await captcha_img.click()
                await asyncio.sleep(1.5)  # 等待新验证码加载
        except Exception as e:
            print(f"⚠️ 刷新验证码失败: {e}")
    
    async def check_login_success(self, page):
        """检查登录是否成功"""
        try:
            # 等待页面响应
            await asyncio.sleep(2)
            
            current_url = page.url
            print(f"📍 当前URL: {current_url}")
            
            # 检查URL变化
            if "login" not in current_url.lower():
                print("✅ 登录成功！URL已跳转")
                return True
            
            # 检查错误消息
            error_found = await self.check_error_messages(page)
            if error_found:
                return False
            
            # 如果没有明确的错误，可能需要等待更长时间
            print("⏳ 等待更长时间检查登录状态...")
            await asyncio.sleep(3)
            
            current_url = page.url
            if "login" not in current_url.lower():
                print("✅ 登录成功！")
                return True
            
            return False
            
        except Exception as e:
            print(f"❌ 检查登录状态时出错: {e}")
            return False
    
    async def check_error_messages(self, page):
        """检查错误消息"""
        try:
            # 等待可能的错误消息出现
            await asyncio.sleep(1)
            
            error_selectors = [
                '.el-message--error',
                '.el-message',
                '.error-message',
                '[class*="error"]'
            ]
            
            for selector in error_selectors:
                elements = await page.query_selector_all(selector)
                for element in elements:
                    text = await element.inner_text()
                    if text and text.strip():
                        print(f"❌ 发现错误信息: {text}")
                        return True
            
            return False
            
        except Exception as e:
            print(f"⚠️ 检查错误消息时出错: {e}")
            return False

    async def navigate_to_zhenxuan(self, page):
        """导航到甄选需求管理页面并保存cookie"""
        try:
            print("🧭 开始导航到甄选需求管理页面...")

            # 等待页面完全加载
            await asyncio.sleep(3)

            # 点击"合作伙伴库"菜单
            print("🖱️ 点击'合作伙伴库'菜单...")
            await self.click_menu_item(page, "合作伙伴库")
            await asyncio.sleep(1)  # 1秒间隔

            # 点击"合作伙伴甄选"菜单
            print("🖱️ 点击'合作伙伴甄选'菜单...")
            await self.click_menu_item(page, "合作伙伴甄选")
            await asyncio.sleep(1)  # 1秒间隔

            # 点击"甄选需求管理"菜单
            print("🖱️ 点击'甄选需求管理'菜单...")
            await self.click_menu_item(page, "甄选需求管理")
            await asyncio.sleep(1)  # 1秒间隔

            # 等待页面跳转到目标URL
            print("⏳ 等待页面跳转到项目列表...")
            target_url = "dict.gmcc.net:30722/ptn/main/selectDemand"

            # 等待最多30秒检查URL
            for i in range(30):
                current_url = page.url
                print(f"📍 当前URL: {current_url}")

                if target_url in current_url:
                    print("✅ 成功进入甄选需求管理页面！")
                    break

                await asyncio.sleep(1)
            else:
                print("⚠️ 未能在30秒内跳转到目标页面")

            # 保存cookie
            await self.save_cookies(page)

        except Exception as e:
            print(f"❌ 导航过程中出现错误: {e}")

    async def click_menu_item(self, page, menu_text):
        """点击指定文本的菜单项"""
        try:
            # 尝试多种选择器来查找菜单项
            selectors = [
                f'text="{menu_text}"',
                f'[title="{menu_text}"]',
                f'span:has-text("{menu_text}")',
                f'a:has-text("{menu_text}")',
                f'li:has-text("{menu_text}")',
                f'div:has-text("{menu_text}")',
                f'*:has-text("{menu_text}")'
            ]

            menu_element = None
            for selector in selectors:
                try:
                    menu_element = await page.wait_for_selector(selector, timeout=5000)
                    if menu_element:
                        # 检查元素是否可见和可点击
                        is_visible = await menu_element.is_visible()
                        if is_visible:
                            print(f"✅ 找到菜单项: {menu_text}")
                            break
                except:
                    continue

            if menu_element:
                # 滚动到元素位置
                await menu_element.scroll_into_view_if_needed()
                await asyncio.sleep(0.5)

                # 点击菜单项
                await menu_element.click()
                print(f"✅ 成功点击菜单: {menu_text}")
                return True
            else:
                print(f"❌ 未找到菜单项: {menu_text}")
                return False

        except Exception as e:
            print(f"❌ 点击菜单 '{menu_text}' 时出错: {e}")
            return False

    async def save_cookies(self, page):
        """保存当前页面的cookies到文件"""
        try:
            print("🍪 正在保存cookies...")

            # 获取当前页面的cookies
            cookies = await page.context.cookies()

            # 固定的cookie文件名
            cookie_filename = f"{self.cookie_dir}/cookies_dict_zhenxuan.json"

            # 保存cookies到JSON文件（覆盖原文件）
            with open(cookie_filename, 'w', encoding='utf-8') as f:
                json.dump(cookies, f, indent=2, ensure_ascii=False)

            print(f"✅ Cookies已保存到: {cookie_filename}")
            print(f"📊 共保存了 {len(cookies)} 个cookie")

            return True

        except Exception as e:
            print(f"❌ 保存cookies时出错: {e}")
            return False


async def main():
    """主函数"""
    bot = FinalDictLoginBot()
    await bot.login()


if __name__ == "__main__":
    print("🚀 启动最终版字典网站自动登录程序...")
    print("🔧 优化了验证码识别和登录流程")
    print("📸 验证码图片将保存到 captcha_images 目录")
    print("🍪 Cookies将保存到 cookies/cookies_dict_zhenxuan.json")
    print("🧭 登录成功后将自动导航到甄选需求管理页面")
    print("📋 执行流程：合作伙伴库 → 合作伙伴甄选 → 甄选需求管理")
    print("=" * 60)
    asyncio.run(main())
