-- 创建 v_zhenxuan_queryselectapplydetail 视图
-- 解析 raw_data 字段的 JSON 数据，提取指定字段

DROP VIEW IF EXISTS v_zhenxuan_queryselectapplydetail;

CREATE VIEW v_zhenxuan_queryselectapplydetail AS
SELECT 
    -- 基础字段（来自表字段，不是JSON）
    t.select_apply_id,
    t.request_params,
    
    -- 从 raw_data JSON 中提取的字段
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.selectRevId')) AS select_rev_id,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.selectRevName')) AS select_rev_name,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.projectName')) AS project_name,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.customerName')) AS customer_name,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.projectCode')) AS project_code,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.projectNo')) AS project_no,
    
    -- 甄选类型相关
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.selectType')) AS select_type,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.selectName')) AS select_name,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.selectTypeValue')) AS select_type_value,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.projectTypeValue')) AS project_type_value,
    
    -- 时间字段
    STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.createTime')), '%Y-%m-%d %H:%i:%s') AS create_time,
    STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.startTime')), '%Y-%m-%d') AS start_time,
    STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.endTime')), '%Y-%m-%d') AS end_time,
    
    -- 状态字段
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.applyStatusValue')) AS apply_status_value,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.applyReviewStatusValue')) AS apply_review_status_value,
    
    -- 业务ID字段
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.reviewFileBusinessId')) AS review_file_business_id,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.workOrderMsgId')) AS work_order_msg_id,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.scoreOrderMsgId')) AS score_order_msg_id,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.scoreRuleId')) AS score_rule_id,
    
    -- 验证和预算字段
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.isNeedVerification')) AS is_need_verification,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.isFinishVerification')) AS is_finish_verification,
    CAST(JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.nonTaxSelectBudget')) AS DECIMAL(15,2)) AS non_tax_select_budget,
    
    -- 备注和通知字段
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.actionRemark')) AS action_remark,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.pushNotice')) AS push_notice,
    
    -- 技术审核和投标字段
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.isTechnicalReview')) AS is_technical_review,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.bidFlagDesc')) AS bid_flag_desc,
    STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.bidOpeningTime')), '%Y-%m-%d %H:%i:%s') AS bid_opening_time,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.rating')) AS rating,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.isPreReview')) AS is_pre_review,
    
    -- 结果文档字段
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.selectResultDoc')) AS select_result_doc,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.resultInputType')) AS result_input_type,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.resultTitle')) AS result_title,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.resultContent')) AS result_content,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.docNumberSub')) AS doc_number_sub,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.docNumber')) AS doc_number,
    
    -- 会议结果字段
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.selectResultMeet')) AS select_result_meet,
    JSON_EXTRACT(t.raw_data, '$.resultBody.selectResultMeetList') AS select_result_meet_list,
    
    -- 消息ID字段
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.selectMsgId')) AS select_msg_id,
    
    -- 新增的时间字段
    STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.realEndTime')), '%Y-%m-%d %H:%i:%s') AS realEndTime,
    STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.systemEndSelectTime')), '%Y-%m-%d %H:%i:%s') AS systemEndSelectTime,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.selectMsgId')) AS selectMsgId,
    
    -- 额外有用的字段
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.businessArea')) AS business_area,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.businessAreaValue')) AS business_area_value,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.projectType')) AS project_type,
    CAST(JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.selectBudget')) AS DECIMAL(15,2)) AS select_budget,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.decideOpinion')) AS decide_opinion,
    STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.endSelectTime')), '%Y-%m-%d %H:%i:%s') AS end_select_time,
    
    -- 原始数据和元数据
    t.raw_data,
    t.created_at,
    t.updated_at,
    t.id
FROM 
    zhenxuan_queryselectapplydetail t
WHERE 
    t.raw_data IS NOT NULL
    AND JSON_VALID(t.raw_data) = 1;
