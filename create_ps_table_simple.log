2025-07-09 14:49:32,103 - INFO - ================================================================================
2025-07-09 14:49:32,103 - INFO - 🚀 甄选信息-评审小组数据表创建程序（简化版）
2025-07-09 14:49:32,103 - INFO - ================================================================================
2025-07-09 14:49:32,103 - INFO - 🚀 开始创建甄选信息-评审小组数据表...
2025-07-09 14:49:32,109 - INFO - ✅ 成功连接到数据库: zhenxuandb
2025-07-09 14:49:32,109 - INFO - 🔄 使用数据库 zhenxuandb...
2025-07-09 14:49:32,112 - INFO - 🔄 删除旧表（如果存在）...
2025-07-09 14:49:32,121 - INFO - 🔄 创建新表...
2025-07-09 14:49:32,351 - INFO - ✅ 表创建成功
2025-07-09 14:49:32,351 - INFO - 🔄 验证表创建...
2025-07-09 14:49:32,353 - INFO - ✅ 表验证成功: ('zhenxuan_querylocalaudittrackhistory_ps', '甄选信息-评审小组 本地审核跟踪历史数据表', 'utf8mb4_general_ci')
2025-07-09 14:49:32,354 - INFO - 🔄 查看表结构...
2025-07-09 14:49:32,357 - INFO - 📋 表结构 (20 个字段):
2025-07-09 14:49:32,357 - INFO -    id - bigint - auto_increment
2025-07-09 14:49:32,364 - INFO -    business_id - varchar(50) - 
2025-07-09 14:49:32,364 - INFO -    work_order_msg_id - varchar(100) - 
2025-07-09 14:49:32,366 - INFO -    step_name_filter - varchar(100) - 
2025-07-09 14:49:32,366 - INFO -    request_params - json - 
2025-07-09 14:49:32,366 - INFO -    busi_date - datetime - 
2025-07-09 14:49:32,366 - INFO -    code - varchar(20) - 
2025-07-09 14:49:32,367 - INFO -    message - text - 
2025-07-09 14:49:32,367 - INFO -    audit_process_track_id - varchar(50) - 
2025-07-09 14:49:32,367 - INFO -    step_name - varchar(100) - 
2025-07-09 14:49:32,367 - INFO -    create_time - datetime - 
2025-07-09 14:49:32,368 - INFO -    finish_time - datetime - 
2025-07-09 14:49:32,368 - INFO -    status - varchar(50) - 
2025-07-09 14:49:32,368 - INFO -    audit_handler - varchar(100) - 
2025-07-09 14:49:32,368 - INFO -    audit_remark - text - 
2025-07-09 14:49:32,369 - INFO -    score_rule_id - varchar(50) - 
2025-07-09 14:49:32,369 - INFO -    score_order_msg_id - varchar(50) - 
2025-07-09 14:49:32,369 - INFO -    raw_data - json - 
2025-07-09 14:49:32,369 - INFO -    created_at - timestamp - DEFAULT_GENERATED
2025-07-09 14:49:32,369 - INFO -    updated_at - timestamp - DEFAULT_GENERATED on update CURRENT_TIMESTAMP
2025-07-09 14:49:32,370 - INFO - ✅ 所有操作完成
2025-07-09 14:49:32,370 - INFO - 🔌 数据库连接已断开
2025-07-09 14:49:32,370 - INFO - ================================================================================
2025-07-09 14:49:32,371 - INFO - 🎉 数据表创建成功！
2025-07-09 14:49:32,371 - INFO - ⏱️ 总耗时: 0.27 秒
2025-07-09 14:49:32,371 - INFO - ================================================================================
