# 甄选需求管理页面数据字典

## 📋 页面概览

**页面信息**
- **页面标题**: 框架-主页面
- **页面URL**: https://dict.gmcc.net:30722/ptn/main/selectDemand
- **功能描述**: 甄选需求管理系统，用于管理项目甄选需求和方案
- **分析时间**: 2025-07-08

**页面结构统计**
- **主数据表格**: 1个（包含项目甄选数据）
- **操作按钮**: 7个主要功能按钮
- **搜索字段**: 6个搜索条件
- **数据记录**: 当前显示10条项目记录

## 🗃️ 核心数据表结构

### 主数据表：项目甄选需求列表

| 字段名称 | 字段类型 | 长度限制 | 是否必填 | 字段说明 | 示例值 |
|---------|---------|---------|---------|---------|--------|
| 序号 | Integer | - | 是 | 列表序号，从1开始递增 | 1, 2, 3 |
| 项目名称 | String | 50+ | 是 | 完整的项目名称 | "中山市坤鹏电子科技有限公司信息化建设项目" |
| 项目编码 | String | 20 | 是 | 项目唯一标识编码 | "CMGDZSICT20250707037" |
| 需求名称 | String | 30+ | 是 | 甄选需求的具体名称 | "中山移动某智慧园区项目" |
| 甄选方案数量 | Integer | - | 是 | 当前项目的甄选方案数量 | 0, 1, 2 |
| 需求编码 | BigInteger | 19 | 是 | 需求的唯一标识ID | "1942422593200898048" |
| 甄选类别 | String | 10 | 是 | 甄选的类别分类 | "项目甄选" |
| 归属地市 | String | 10 | 是 | 项目所属地市 | "中山" |
| 创建时间 | DateTime | - | 是 | 记录创建时间 | "2025-07-08 11:16:49" |
| 甄选需求状态 | String | 20 | 是 | 当前甄选需求的状态 | "审核通过", "审核通过(已制定方案)" |
| 操作 | Action | - | - | 操作按钮列 | "查看详情" |

## 🔍 搜索条件字段

### 查询表单字段

| 字段名称 | 字段类型 | 输入方式 | 字段说明 | 占位符提示 |
|---------|---------|---------|---------|-----------|
| 项目名称 | String | 文本输入框 | 支持模糊搜索项目名称 | "项目名称" |
| 项目编码 | String | 文本输入框 | 精确匹配项目编码 | "项目编码" |
| 需求名称 | String | 文本输入框 | 支持模糊搜索需求名称 | "需求名称" |
| 甄选类别 | String | 下拉选择框 | 选择甄选类别 | "请选择" |
| 归属地市 | String | 下拉选择框 | 选择归属地市 | "请选择" |
| 需求编码 | String | 文本输入框 | 精确匹配需求编码 | "需求编码" |
| 甄选需求状态 | String | 下拉选择框 | 选择状态 | "请选择" |

## 🎛️ 功能按钮

### 主要操作按钮

| 按钮名称 | 按钮类型 | 功能描述 | 权限要求 |
|---------|---------|---------|---------|
| 重置 | button | 清空所有搜索条件 | 无 |
| 查询 | button | 根据条件查询数据 | 无 |
| 发起项目甄选 | button | 创建新的项目甄选需求 | 需要权限 |
| 发起产品甄选 | button | 创建新的产品甄选需求 | 需要权限 |
| 发起算力项目甄选 | button | 创建算力相关项目甄选 | 需要权限 |
| 发起中移集成项目甄选补录 | button | 补录中移集成项目甄选 | 需要权限 |
| 甄选附件变更 | button | 修改甄选相关附件 | 需要权限 |
| 查看详情 | link | 查看具体项目详情 | 无 |

## 📊 数据类型定义

### 枚举值定义

**甄选类别 (甄选类别)**
- `项目甄选` - 项目类型的甄选
- `产品甄选` - 产品类型的甄选  
- `算力项目甄选` - 算力相关项目甄选

**甄选需求状态 (甄选需求状态)**
- `审核通过` - 需求已通过审核
- `审核通过(已制定方案)` - 已通过审核且制定了方案
- `待审核` - 等待审核中
- `审核不通过` - 审核未通过
- `草稿` - 草稿状态

**归属地市 (归属地市)**
- `中山` - 中山市
- `广州` - 广州市
- `深圳` - 深圳市
- `珠海` - 珠海市
- 其他广东省地市...

## 🔗 数据关系

### 主要关联关系

1. **项目编码 ↔ 需求编码**: 一对多关系，一个项目可以有多个甄选需求
2. **甄选需求 ↔ 甄选方案**: 一对多关系，一个需求可以有多个甄选方案
3. **归属地市 ↔ 项目**: 多对一关系，一个地市可以有多个项目

### 业务流程关系

```
项目创建 → 需求提出 → 甄选发起 → 方案制定 → 审核通过 → 方案实施
```

## 📝 数据样本

### 典型数据记录示例

```json
{
  "序号": 1,
  "项目名称": "中山市坤鹏电子科技有限公司信息化建设项目",
  "项目编码": "CMGDZSICT20250707037",
  "需求名称": "中山移动某智慧园区项目",
  "甄选方案数量": 0,
  "需求编码": "1942422593200898048",
  "甄选类别": "项目甄选",
  "归属地市": "中山",
  "创建时间": "2025-07-08 11:16:49",
  "甄选需求状态": "审核通过"
}
```

## 🔧 技术规范

### 编码规范

**项目编码格式**: `CMGD{地市代码}ICT{YYYYMMDD}{序号}`
- CMGD: 固定前缀
- 地市代码: ZS(中山), GZ(广州), SZ(深圳)等
- ICT: 固定标识
- YYYYMMDD: 创建日期
- 序号: 3位数字序号

**需求编码格式**: 19位数字ID（雪花算法生成）

### 数据约束

1. **项目编码**: 必须唯一，不可重复
2. **需求编码**: 必须唯一，系统自动生成
3. **创建时间**: 格式为 YYYY-MM-DD HH:mm:ss
4. **甄选方案数量**: 非负整数
5. **项目名称**: 不能为空，长度限制100字符以内

## 🚀 API接口推测

基于页面功能推测的可能API接口：

### 查询接口
```
GET /ptn/main/selectDemand/list
参数: projectName, projectCode, demandName, selectType, city, demandCode, status
返回: 分页的甄选需求列表
```

### 详情接口
```
GET /ptn/main/selectDemand/detail/{demandCode}
返回: 甄选需求详细信息
```

### 创建接口
```
POST /ptn/main/selectDemand/create
参数: 甄选需求创建信息
返回: 创建结果
```

---

**注意**: 本数据字典基于页面结构分析生成，实际的数据库结构和API接口可能与此有所差异。建议结合实际的系统文档和API文档进行确认。
