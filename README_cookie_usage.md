# Cookie访问器使用说明

## 概述
本项目提供了两个使用保存的cookies访问甄选需求管理页面的脚本：
- `visit_with_cookies.py` - 基础版本，用于简单访问和查看
- `advanced_cookie_visitor.py` - 高级版本，支持数据抓取和交互

## 前置条件
1. 首先运行 `login2zhenxuan.py` 完成登录并保存cookies
2. 确保 `cookies` 目录下有有效的cookie文件
3. 安装必要的依赖：
   ```bash
   pip install playwright
   playwright install chromium
   ```

## 基础版本使用 (visit_with_cookies.py)

### 功能特点
- 自动加载最新的cookies
- 访问甄选需求管理页面
- 基础的页面内容检查
- 页面截图功能

### 使用方法
```bash
python visit_with_cookies.py
```

### 输出示例
```
🚀 启动Cookie访问程序...
📂 加载最新cookies文件: cookies/cookies_latest.json
✅ 成功加载 15 个cookie
🍪 正在添加cookies到浏览器上下文...
✅ Cookies添加成功
🌐 正在访问目标页面: https://dict.gmcc.net:30722/ptn/main/selectDemand
📍 当前URL: https://dict.gmcc.net:30722/ptn/main/selectDemand
✅ 成功访问甄选需求管理页面！
```

## 高级版本使用 (advanced_cookie_visitor.py)

### 功能特点
- 三种操作模式：查看、抓取、交互
- 自动数据抓取并保存为CSV
- 页面结构分析
- 支持无头模式运行
- 完整的页面截图

### 操作模式

#### 1. 查看模式 (view)
- 分析页面结构
- 保存完整页面截图
- 统计页面元素数量

#### 2. 抓取模式 (scrape)
- 自动识别并抓取表格数据
- 保存数据为CSV格式
- 支持多表格数据合并

#### 3. 交互模式 (interact)
- 识别搜索框和按钮
- 分析可交互元素
- 为后续自动化操作做准备

### 使用方法

#### 修改操作模式
在 `advanced_cookie_visitor.py` 的 `main()` 函数中修改 `action` 参数：

```python
# 查看模式
action = "view"

# 抓取模式
action = "scrape"

# 交互模式
action = "interact"
```

#### 运行脚本
```bash
python advanced_cookie_visitor.py
```

#### 无头模式运行
修改 `main()` 函数中的 `headless` 参数：
```python
success = await visitor.visit_and_scrape(headless=True, action=action)
```

## 文件结构
```
项目目录/
├── login2zhenxuan.py              # 登录脚本
├── visit_with_cookies.py          # 基础Cookie访问器
├── advanced_cookie_visitor.py     # 高级Cookie访问器
├── cookies/                       # Cookie存储目录
│   ├── cookies_latest.json        # 最新cookies
│   └── cookies_zhenxuan_*.json    # 带时间戳的cookies
├── scraped_data/                  # 抓取数据存储目录
│   ├── screenshot_*.png           # 页面截图
│   └── scraped_data_*.csv         # 抓取的数据
└── captcha_images/                # 验证码图片目录
```

## 注意事项

### Cookie有效期
- Cookies有时效性，如果访问失败可能需要重新登录
- 建议定期运行登录脚本更新cookies

### 错误处理
如果遇到以下情况，请重新运行登录脚本：
- 被重定向到登录页面
- 页面显示"会话已过期"
- 无法访问目标页面

### 数据抓取注意事项
- 抓取数据时请遵守网站的使用条款
- 建议设置合理的访问间隔
- 大量数据抓取时建议使用无头模式

## 自定义扩展

### 添加新的抓取逻辑
在 `advanced_cookie_visitor.py` 中的 `scrape_data()` 方法中添加：

```python
# 抓取特定元素
specific_elements = await page.query_selector_all('.your-selector')
for element in specific_elements:
    text = await element.inner_text()
    # 处理数据
```

### 添加新的交互功能
在 `interactive_mode()` 方法中添加：

```python
# 点击特定按钮
button = await page.query_selector('button:has-text("查询")')
if button:
    await button.click()
    await page.wait_for_load_state('networkidle')
```

## 故障排除

### 常见问题
1. **Cookie加载失败**
   - 检查 `cookies` 目录是否存在
   - 确认有有效的cookie文件

2. **页面访问失败**
   - 检查网络连接
   - 确认目标URL是否正确
   - 重新运行登录脚本

3. **数据抓取为空**
   - 检查页面是否完全加载
   - 确认表格选择器是否正确
   - 增加等待时间

### 调试模式
设置 `headless=False` 可以看到浏览器操作过程，便于调试。

## 更新日志
- v1.0: 基础Cookie访问功能
- v1.1: 添加高级数据抓取功能
- v1.2: 支持多种操作模式和无头运行
