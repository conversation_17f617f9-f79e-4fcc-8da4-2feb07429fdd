#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 v_zhenxuan_queryselectapplydetai_partner 视图的使用
"""

import sys
import os
from database.db_config import DatabaseManager, ZHENXUAN_DB_CONFIG

def test_partner_view():
    """测试合作伙伴视图的各种查询用法"""
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    if not db_manager.connect():
        print("❌ 数据库连接失败")
        return
    
    try:
        print("🧪 测试 v_zhenxuan_queryselectapplydetai_partner 视图的使用")
        print("="*80)
        
        with db_manager.get_cursor() as cursor:
            
            # 测试1: 基本查询 - 获取所有目标字段
            print("📋 测试1: 基本查询 - 获取指定字段")
            sql1 = """
            SELECT 
                select_apply_id, project_no, selectApplyResultId, selectApplyId,
                partnerMsgId, partnerName, decideResultValue, bidMoneyValue
            FROM v_zhenxuan_queryselectapplydetai_partner 
            LIMIT 3
            """
            
            cursor.execute(sql1)
            results = cursor.fetchall()
            
            for i, row in enumerate(results, 1):
                print(f"\n记录 {i}:")
                print(f"  申请ID: {row['select_apply_id']}")
                print(f"  项目编号: {row['project_no']}")
                print(f"  结果ID: {row['selectApplyResultId']}")
                print(f"  申请ID(结果): {row['selectApplyId']}")
                print(f"  合作伙伴ID: {row['partnerMsgId']}")
                print(f"  合作伙伴名称: {row['partnerName']}")
                print(f"  决策结果: {row['decideResultValue']}")
                print(f"  投标金额: {row['bidMoneyValue']}")
            
            print("\n" + "="*80)
            
            # 测试2: 按合作伙伴统计
            print("📋 测试2: 按合作伙伴统计中选次数")
            sql2 = """
            SELECT 
                partnerName,
                COUNT(*) as selected_count,
                COUNT(CASE WHEN bidMoneyValue IS NOT NULL AND bidMoneyValue != '' THEN 1 END) as with_bid_amount
            FROM v_zhenxuan_queryselectapplydetai_partner 
            GROUP BY partnerName
            HAVING selected_count > 1
            ORDER BY selected_count DESC
            LIMIT 10
            """
            
            cursor.execute(sql2)
            results = cursor.fetchall()
            
            print("中选次数最多的合作伙伴:")
            for i, row in enumerate(results, 1):
                print(f"  {i}. {row['partnerName']} - 中选{row['selected_count']}次 (有金额:{row['with_bid_amount']}次)")
            
            print("\n" + "="*80)
            
            # 测试3: 按项目编号查询（简化版）
            print("📋 测试3: 按项目编号查询中选合作伙伴")
            sql3 = """
            SELECT
                project_no, partnerName, bidMoneyValue, contactsName, contactsPhone
            FROM v_zhenxuan_queryselectapplydetai_partner
            WHERE project_no LIKE 'CMGDZSICT2024%'
            LIMIT 5
            """

            cursor.execute(sql3)
            results = cursor.fetchall()

            print("2024年项目的中选合作伙伴:")
            for i, row in enumerate(results, 1):
                print(f"  {i}. 项目: {row['project_no']}")
                print(f"     合作伙伴: {row['partnerName']}")
                print(f"     投标金额: {row['bidMoneyValue']}")
                print(f"     联系人: {row['contactsName']} ({row['contactsPhone']})")
                print()

            print("="*80)
            
            # 测试4: 投标金额分析
            print("📋 测试4: 投标金额分析")
            sql4 = """
            SELECT 
                COUNT(*) as total_records,
                COUNT(CASE WHEN bidMoneyValue IS NOT NULL AND bidMoneyValue != '' THEN 1 END) as has_amount,
                COUNT(CASE WHEN bidMoneyValue IS NULL OR bidMoneyValue = '' THEN 1 END) as no_amount
            FROM v_zhenxuan_queryselectapplydetai_partner
            """
            
            cursor.execute(sql4)
            result = cursor.fetchone()
            
            print("投标金额统计:")
            print(f"  总记录数: {result['total_records']}")
            print(f"  有投标金额: {result['has_amount']}")
            print(f"  无投标金额: {result['no_amount']}")
            
            print("\n" + "="*80)
            
            # 测试5: 验证数据一致性
            print("📋 测试5: 验证数据一致性")
            sql5 = """
            SELECT 
                COUNT(CASE WHEN select_apply_id = selectApplyId THEN 1 END) as consistent_apply_id,
                COUNT(CASE WHEN select_apply_id != selectApplyId THEN 1 END) as inconsistent_apply_id,
                COUNT(CASE WHEN decideResultValue = '中选' THEN 1 END) as all_selected,
                COUNT(*) as total
            FROM v_zhenxuan_queryselectapplydetai_partner
            """
            
            cursor.execute(sql5)
            result = cursor.fetchone()
            
            print("数据一致性检查:")
            print(f"  申请ID一致: {result['consistent_apply_id']}")
            print(f"  申请ID不一致: {result['inconsistent_apply_id']}")
            print(f"  全部为中选: {result['all_selected']}")
            print(f"  总记录数: {result['total']}")
            
            print("\n" + "="*80)
            
            # 测试6: 联系信息完整性
            print("📋 测试6: 联系信息完整性检查")
            sql6 = """
            SELECT 
                COUNT(CASE WHEN contactsName IS NOT NULL AND contactsName != '' THEN 1 END) as has_contact_name,
                COUNT(CASE WHEN contactsPhone IS NOT NULL AND contactsPhone != '' THEN 1 END) as has_contact_phone,
                COUNT(CASE WHEN contactsEmail IS NOT NULL AND contactsEmail != '' THEN 1 END) as has_contact_email,
                COUNT(*) as total
            FROM v_zhenxuan_queryselectapplydetai_partner
            """
            
            cursor.execute(sql6)
            result = cursor.fetchone()
            
            print("联系信息完整性:")
            print(f"  有联系人姓名: {result['has_contact_name']}/{result['total']} ({result['has_contact_name']/result['total']*100:.1f}%)")
            print(f"  有联系电话: {result['has_contact_phone']}/{result['total']} ({result['has_contact_phone']/result['total']*100:.1f}%)")
            print(f"  有联系邮箱: {result['has_contact_email']}/{result['total']} ({result['has_contact_email']/result['total']*100:.1f}%)")
        
        print("\n✅ 所有测试完成！视图工作正常。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    test_partner_view()
