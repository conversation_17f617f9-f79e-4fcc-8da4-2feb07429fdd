-- 创建 v_zhenxuan_queryselectapplydetai_partner 视图
-- 解析 raw_data 字段中 decideResultValue 为 "中选" 的合作伙伴信息

DROP VIEW IF EXISTS v_zhenxuan_queryselectapplydetai_partner;

CREATE VIEW v_zhenxuan_queryselectapplydetai_partner AS
SELECT 
    -- 基础字段
    t.select_apply_id,
    JSON_UNQUOTE(JSON_EXTRACT(t.raw_data, '$.resultBody.projectNo')) AS project_no,
    
    -- 从 selectApplyResultDetailVos 数组中提取中选合作伙伴信息
    JSON_UNQUOTE(JSON_EXTRACT(partner_data.partner_info, '$.selectApplyResultId')) AS selectApplyResultId,
    JSON_UNQUOTE(JSON_EXTRACT(partner_data.partner_info, '$.selectApplyId')) AS selectApplyId,
    JSON_UNQUOTE(JSON_EXTRACT(partner_data.partner_info, '$.partnerMsgId')) AS partnerMsgId,
    JSON_UNQUOTE(JSON_EXTRACT(partner_data.partner_info, '$.partnerName')) AS partnerName,
    JSON_UNQUOTE(JSON_EXTRACT(partner_data.partner_info, '$.decideResultValue')) AS decideResultValue,
    JSON_UNQUOTE(JSON_EXTRACT(partner_data.partner_info, '$.bidMoneyValue')) AS bidMoneyValue,
    
    -- 额外有用的字段
    JSON_UNQUOTE(JSON_EXTRACT(partner_data.partner_info, '$.contactsName')) AS contactsName,
    JSON_UNQUOTE(JSON_EXTRACT(partner_data.partner_info, '$.contactsPhone')) AS contactsPhone,
    JSON_UNQUOTE(JSON_EXTRACT(partner_data.partner_info, '$.contactsEmail')) AS contactsEmail,
    JSON_UNQUOTE(JSON_EXTRACT(partner_data.partner_info, '$.partnerType')) AS partnerType,
    JSON_UNQUOTE(JSON_EXTRACT(partner_data.partner_info, '$.reviewScore')) AS reviewScore,
    JSON_UNQUOTE(JSON_EXTRACT(partner_data.partner_info, '$.businessScore')) AS businessScore,
    JSON_UNQUOTE(JSON_EXTRACT(partner_data.partner_info, '$.technologyScore')) AS technologyScore,
    JSON_UNQUOTE(JSON_EXTRACT(partner_data.partner_info, '$.selectResultNumber')) AS selectResultNumber,
    
    -- 元数据
    t.id,
    t.created_at,
    t.updated_at
FROM 
    zhenxuan_queryselectapplydetail t
CROSS JOIN (
    -- 使用 JSON_TABLE 来展开数组并筛选中选的合作伙伴
    SELECT 
        t2.select_apply_id,
        partner_info
    FROM 
        zhenxuan_queryselectapplydetail t2,
        JSON_TABLE(
            t2.raw_data,
            '$.resultBody.selectApplyResultDetailVos[*]' 
            COLUMNS (
                partner_info JSON PATH '$'
            )
        ) AS jt
    WHERE 
        t2.raw_data IS NOT NULL
        AND JSON_VALID(t2.raw_data) = 1
        AND JSON_CONTAINS_PATH(t2.raw_data, 'one', '$.resultBody.selectApplyResultDetailVos')
        AND JSON_UNQUOTE(JSON_EXTRACT(jt.partner_info, '$.decideResultValue')) = '中选'
) AS partner_data
WHERE 
    t.select_apply_id = partner_data.select_apply_id;
