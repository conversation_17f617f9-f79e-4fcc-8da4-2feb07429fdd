#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
甄选申请详情数据获取和入库程序
基于 querySelectApplyDetail 接口
"""

import requests
import json
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional, List
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_config import DatabaseManager, ZHENXUAN_DB_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('zhenxuan_querySelectApplyDetail_fetch.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ZhenxuanApplyDetailFetcher:
    """甄选申请详情数据获取器"""

    def __init__(self, cookie_file_path=None):
        """初始化"""
        self.base_url = "https://dict.gmcc.net:30722"
        self.db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
        self.session = requests.Session()

        # 默认请求头
        self.headers = {
            'Host': "dict.gmcc.net:30722",
            'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            'Accept': "application/json, text/plain, */*",
            'Accept-Encoding': "gzip, deflate, br, zstd",
            'sec-ch-ua-platform': '"Windows"',
            'Authorization': "Bearer d25c514c-e026-4ddf-b455-9929dfcd3cfb",
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': "?0",
            'Sec-Fetch-Site': "same-origin",
            'Sec-Fetch-Mode': "cors",
            'Sec-Fetch-Dest': "empty",
            'Referer': "https://dict.gmcc.net:30722/ptn/main/selectDemand/detail",
            'Accept-Language': "zh-CN,zh;q=0.9,ee;q=0.8",
        }

        # 初始化Cookie
        self.cookies = {}

        # 设置默认Cookie文件路径
        if cookie_file_path is None:
            # 查找项目根目录下的cookies文件
            project_root = os.path.dirname(os.path.abspath(__file__))
            cookie_file_path = os.path.join(project_root, 'cookies', 'cookies_dict_zhenxuan.json')

        # 加载Cookie
        self.load_cookies_from_file(cookie_file_path)

    def load_cookies_from_file(self, cookie_file_path: str):
        """
        从JSON文件加载Cookie，转换为Cookie字符串格式
        保留所有Cookie，包括同名但不同path的Cookie

        Args:
            cookie_file_path: Cookie文件路径
        """
        try:
            if os.path.exists(cookie_file_path):
                with open(cookie_file_path, 'r', encoding='utf-8') as f:
                    cookie_data = json.load(f)

                # 生成Cookie字符串（保留所有Cookie，包括同名的）
                cookie_pairs = []
                cookie_dict = {}  # 用于显示和统计

                for cookie in cookie_data:
                    name = cookie['name']
                    value = cookie['value']
                    path = cookie.get('path', '/')

                    # 添加到Cookie字符串（所有Cookie都要包含）
                    cookie_pairs.append(f"{name}={value}")

                    # 用于显示的字典（同名Cookie显示最后一个，但实际都会发送）
                    if name not in cookie_dict:
                        cookie_dict[name] = []
                    cookie_dict[name].append({'value': value, 'path': path})

                self.cookie_string = "; ".join(cookie_pairs)
                self.cookies = {name: cookies[-1]['value'] for name, cookies in cookie_dict.items()}  # 显示用

                # 更新headers中的Cookie
                self.headers['Cookie'] = self.cookie_string

                logger.info(f"✅ 成功加载Cookie文件: {cookie_file_path}")
                logger.info(f"📋 加载了 {len(cookie_data)} 个Cookie项")

                # 显示重复Cookie统计
                for name, cookies in cookie_dict.items():
                    if len(cookies) > 1:
                        logger.info(f"  - {name}: {len(cookies)}个 (不同path)")
                        for i, cookie in enumerate(cookies):
                            logger.info(f"    [{i+1}] {cookie['value'][:20]}... (path: {cookie['path']})")
                    else:
                        logger.info(f"  - {name}: {cookies[0]['value'][:20]}... (path: {cookies[0]['path']})")

                # 显示Cookie字符串长度
                logger.info(f"📋 完整Cookie字符串长度: {len(self.cookie_string)} 字符")

            else:
                logger.warning(f"⚠️ Cookie文件不存在: {cookie_file_path}")
                logger.info("🔄 使用默认Cookie配置")
                self._set_default_cookies()

        except Exception as e:
            logger.error(f"❌ 加载Cookie文件失败: {e}")
            logger.info("🔄 使用默认Cookie配置")
            self._set_default_cookies()

    def _set_default_cookies(self):
        """设置默认Cookie"""
        self.cookies = {
            'BSS-SESSION': 'NjU3M2VhYjYtNWRlYi00NGMzLTg1OWQtZDg2YzNiMjdkZThi',
            'NewoaAppToDones': '',
            'NewoaAppToReads': '',
            'isLogin': 'ImlzTG9naW4i',
            'requestId': 'eb0331f0-5bec-11f0-b2ab-d7d92ba136f7',
            'systemUserCode': 'InpoZW5nZGV3ZW4i',
            'jsession_id_4_boss': 'n603BE9BD8C39CA4832672F7B13F2B61E-1'
        }

        # 生成默认Cookie字符串
        cookie_pairs = []
        for name, value in self.cookies.items():
            cookie_pairs.append(f"{name}={value}")

        self.cookie_string = "; ".join(cookie_pairs)
        self.headers['Cookie'] = self.cookie_string

    def update_cookies(self, cookie_string: str):
        """
        更新Cookie

        Args:
            cookie_string: Cookie字符串，格式如 "key1=value1; key2=value2"
        """
        try:
            cookie_pairs = cookie_string.split('; ')
            for pair in cookie_pairs:
                if '=' in pair:
                    key, value = pair.split('=', 1)
                    self.cookies[key] = value

            # 重新生成Cookie字符串并更新headers
            new_cookie_pairs = []
            for name, value in self.cookies.items():
                new_cookie_pairs.append(f"{name}={value}")

            self.cookie_string = "; ".join(new_cookie_pairs)
            self.headers['Cookie'] = self.cookie_string

            logger.info("✅ Cookie更新成功")
        except Exception as e:
            logger.error(f"❌ Cookie更新失败: {e}")
    
    def fetch_apply_detail(self, select_apply_id: str) -> Optional[Dict[str, Any]]:
        """
        获取甄选申请详情数据

        Args:
            select_apply_id: 甄选申请ID（来源于zhenxuan_querySelectProjectList表的select_apply_id字段）

        Returns:
            Dict: API响应数据，失败返回None
        """
        url = f"{self.base_url}/partner/materialManage/pnrSelect/querySelectApplyDetail"

        # 构造请求参数（select_apply_id作为API的selectApplyId参数）
        params = {
            "selectApplyId": select_apply_id
        }

        try:
            logger.info(f"🔄 开始获取甄选申请详情，select_apply_id: {select_apply_id}")

            # 发送GET请求（Cookie已在headers中设置）
            response = self.session.get(
                url,
                params=params,
                headers=self.headers,
                timeout=30,
                verify=False  # 忽略SSL证书验证
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析JSON响应
            data = response.json()
            
            # 检查业务状态码
            if data.get('code') != '000000':
                logger.error(f"❌ API返回错误: {data.get('message', '未知错误')}")
                return None
            
            logger.info(f"✅ 数据获取成功，响应码: {data.get('code')}")
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 网络请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON解析失败: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ 获取数据失败: {e}")
            return None

    def transform_data(self,
                      response_data: Dict[str, Any],
                      select_apply_id: str) -> Dict[str, Any]:
        """
        转换响应数据为数据库格式

        Args:
            response_data: API响应数据
            select_apply_id: 甄选申请ID（来源于zhenxuan_querySelectProjectList表的select_apply_id字段）

        Returns:
            Dict: 转换后的数据库记录
        """
        result_body = response_data.get('resultBody', {})

        # 解析时间字段
        def parse_datetime(date_str):
            if not date_str:
                return None
            try:
                return datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
            except:
                try:
                    return datetime.strptime(date_str, '%Y-%m-%d')
                except:
                    return None

        def parse_date(date_str):
            if not date_str:
                return None
            try:
                return datetime.strptime(date_str, '%Y-%m-%d').date()
            except:
                return None

        return {
            # API请求参数（源字段select_apply_id）
            'select_apply_id': select_apply_id,
            'request_params': json.dumps({"selectApplyId": select_apply_id}),

            # 响应基础信息（根级字段）
            'busi_date': parse_datetime(response_data.get('busiDate')),
            'code': response_data.get('code'),
            'message': response_data.get('message'),

            # resultBody核心信息
            'select_rev_id': result_body.get('selectRevId'),
            'select_rev_name': result_body.get('selectRevName'),
            'project_name': result_body.get('projectName'),
            'customer_name': result_body.get('customerName'),
            'project_code': result_body.get('projectCode'),
            'project_no': result_body.get('projectNo'),

            # 甄选信息
            'select_type': result_body.get('selectType'),
            'select_name': result_body.get('selectName'),
            'select_type_value': result_body.get('selectTypeValue'),

            # 业务区域和项目类型
            'business_area': result_body.get('businessArea'),
            'business_area_value': result_body.get('businessAreaValue'),
            'initiate_department': result_body.get('initiateDepartment'),
            'project_type': result_body.get('projectType'),
            'project_type_value': result_body.get('projectTypeValue'),

            # 时间信息
            'create_time': parse_datetime(result_body.get('createTime')),
            'start_time': parse_date(result_body.get('startTime')),
            'end_time': parse_date(result_body.get('endTime')),

            # 状态信息
            'apply_status_value': result_body.get('applyStatusValue'),
            'apply_review_status_value': result_body.get('applyReviewStatusValue'),

            # 工单和评分信息
            'review_file_business_id': result_body.get('reviewFileBusinessId'),
            'work_order_msg_id': result_body.get('workOrderMsgId'),
            'score_order_msg_id': result_body.get('scoreOrderMsgId'),
            'score_rule_id': result_body.get('scoreRuleId'),
            'rule_item_ave_score': result_body.get('ruleItemAveScore'),

            # 验证和预算信息
            'is_need_verification': result_body.get('isNeedVerification', '0'),
            'is_finish_verification': result_body.get('isFinishVerification'),
            'non_tax_select_budget': result_body.get('nonTaxSelectBudget'),

            # 操作备注和推送信息
            'action_remark': result_body.get('actionRemark'),
            'push_notice': result_body.get('pushNotice'),

            # 技术审核和投标信息
            'is_technical_review': result_body.get('isTechnicalReview'),
            'bid_flag_desc': result_body.get('bidFlagDesc'),
            'bid_opening_time': parse_datetime(result_body.get('bidOpeningTime')),
            'rating': result_body.get('rating'),
            'is_pre_review': result_body.get('isPreReview'),

            # 结果文档信息
            'select_result_doc': result_body.get('selectResultDoc'),
            'result_input_type': result_body.get('resultInputType'),
            'result_title': result_body.get('resultTitle'),
            'result_content': result_body.get('resultContent'),
            'doc_number_sub': result_body.get('docNumberSub'),
            'doc_number': result_body.get('docNumber'),

            # 会议信息
            'select_result_meet': result_body.get('selectResultMeet'),
            'select_result_meet_list': json.dumps(result_body.get('selectResultMeetList'), ensure_ascii=False) if result_body.get('selectResultMeetList') else None,

            # 关联信息
            'select_msg_id': result_body.get('selectMsgId'),
            'dpcs_select_second_negotiate': result_body.get('dpcsSelectSecondNegotiate'),

            # 项目需求详情（嵌套对象）
            'select_project_demand_detail_vo': json.dumps(result_body.get('selectProjectDemandDetailVo'), ensure_ascii=False) if result_body.get('selectProjectDemandDetailVo') else None,

            # 原始数据
            'raw_data': json.dumps(response_data, ensure_ascii=False)
        }

    def insert_record(self, record_data: Dict[str, Any]) -> bool:
        """
        插入单条记录到数据库

        Args:
            record_data: 记录数据

        Returns:
            bool: 插入是否成功
        """
        sql = """
        INSERT INTO zhenxuan_querySelectApplyDetail (
            select_apply_id, request_params, busi_date, code, message,
            select_rev_id, select_rev_name, project_name, customer_name, project_code, project_no,
            select_type, select_name, select_type_value,
            business_area, business_area_value, initiate_department, project_type, project_type_value,
            create_time, start_time, end_time,
            apply_status_value, apply_review_status_value,
            review_file_business_id, work_order_msg_id, score_order_msg_id, score_rule_id, rule_item_ave_score,
            is_need_verification, is_finish_verification, non_tax_select_budget,
            action_remark, push_notice,
            is_technical_review, bid_flag_desc, bid_opening_time, rating, is_pre_review,
            select_result_doc, result_input_type, result_title, result_content, doc_number_sub, doc_number,
            select_result_meet, select_result_meet_list,
            select_msg_id, dpcs_select_second_negotiate,
            select_project_demand_detail_vo, raw_data
        ) VALUES (
            %(select_apply_id)s, %(request_params)s, %(busi_date)s, %(code)s, %(message)s,
            %(select_rev_id)s, %(select_rev_name)s, %(project_name)s, %(customer_name)s, %(project_code)s, %(project_no)s,
            %(select_type)s, %(select_name)s, %(select_type_value)s,
            %(business_area)s, %(business_area_value)s, %(initiate_department)s, %(project_type)s, %(project_type_value)s,
            %(create_time)s, %(start_time)s, %(end_time)s,
            %(apply_status_value)s, %(apply_review_status_value)s,
            %(review_file_business_id)s, %(work_order_msg_id)s, %(score_order_msg_id)s, %(score_rule_id)s, %(rule_item_ave_score)s,
            %(is_need_verification)s, %(is_finish_verification)s, %(non_tax_select_budget)s,
            %(action_remark)s, %(push_notice)s,
            %(is_technical_review)s, %(bid_flag_desc)s, %(bid_opening_time)s, %(rating)s, %(is_pre_review)s,
            %(select_result_doc)s, %(result_input_type)s, %(result_title)s, %(result_content)s, %(doc_number_sub)s, %(doc_number)s,
            %(select_result_meet)s, %(select_result_meet_list)s,
            %(select_msg_id)s, %(dpcs_select_second_negotiate)s,
            %(select_project_demand_detail_vo)s, %(raw_data)s
        ) ON DUPLICATE KEY UPDATE
            request_params = VALUES(request_params),
            busi_date = VALUES(busi_date),
            code = VALUES(code),
            message = VALUES(message),
            select_rev_id = VALUES(select_rev_id),
            select_rev_name = VALUES(select_rev_name),
            project_name = VALUES(project_name),
            customer_name = VALUES(customer_name),
            project_code = VALUES(project_code),
            project_no = VALUES(project_no),
            select_type = VALUES(select_type),
            select_name = VALUES(select_name),
            select_type_value = VALUES(select_type_value),
            business_area = VALUES(business_area),
            business_area_value = VALUES(business_area_value),
            initiate_department = VALUES(initiate_department),
            project_type = VALUES(project_type),
            project_type_value = VALUES(project_type_value),
            create_time = VALUES(create_time),
            start_time = VALUES(start_time),
            end_time = VALUES(end_time),
            apply_status_value = VALUES(apply_status_value),
            apply_review_status_value = VALUES(apply_review_status_value),
            review_file_business_id = VALUES(review_file_business_id),
            work_order_msg_id = VALUES(work_order_msg_id),
            score_order_msg_id = VALUES(score_order_msg_id),
            score_rule_id = VALUES(score_rule_id),
            rule_item_ave_score = VALUES(rule_item_ave_score),
            is_need_verification = VALUES(is_need_verification),
            is_finish_verification = VALUES(is_finish_verification),
            non_tax_select_budget = VALUES(non_tax_select_budget),
            action_remark = VALUES(action_remark),
            push_notice = VALUES(push_notice),
            is_technical_review = VALUES(is_technical_review),
            bid_flag_desc = VALUES(bid_flag_desc),
            bid_opening_time = VALUES(bid_opening_time),
            rating = VALUES(rating),
            is_pre_review = VALUES(is_pre_review),
            select_result_doc = VALUES(select_result_doc),
            result_input_type = VALUES(result_input_type),
            result_title = VALUES(result_title),
            result_content = VALUES(result_content),
            doc_number_sub = VALUES(doc_number_sub),
            doc_number = VALUES(doc_number),
            select_result_meet = VALUES(select_result_meet),
            select_result_meet_list = VALUES(select_result_meet_list),
            select_msg_id = VALUES(select_msg_id),
            dpcs_select_second_negotiate = VALUES(dpcs_select_second_negotiate),
            select_project_demand_detail_vo = VALUES(select_project_demand_detail_vo),
            raw_data = VALUES(raw_data),
            updated_at = CURRENT_TIMESTAMP
        """

        try:
            with self.db_manager.get_cursor() as cursor:
                cursor.execute(sql, record_data)
                self.db_manager.connection.commit()
                return True
        except Exception as e:
            logger.error(f"❌ 插入记录失败: {e}")
            logger.error(f"记录数据: {record_data.get('select_apply_id', 'Unknown')}")
            return False

    def get_select_apply_ids(self) -> List[str]:
        """
        从 zhenxuan_querySelectProjectList 表获取所有 select_apply_id

        Returns:
            List[str]: select_apply_id 列表
        """
        if not self.db_manager.connect():
            return []

        try:
            sql = """
            SELECT DISTINCT select_apply_id
            FROM zhenxuan_querySelectProjectList
            WHERE select_apply_id IS NOT NULL
            AND select_apply_id != ''
            ORDER BY select_apply_id
            """

            with self.db_manager.get_cursor() as cursor:
                cursor.execute(sql)
                results = cursor.fetchall()
                return [row['select_apply_id'] for row in results]

        except Exception as e:
            logger.error(f"❌ 获取select_apply_id列表失败: {e}")
            return []
        finally:
            self.db_manager.disconnect()

    def sync_all_data(self) -> int:
        """
        同步所有甄选申请详情数据

        Returns:
            int: 同步成功的记录数
        """
        logger.info("🚀 开始同步甄选申请详情数据...")
        logger.info("📊 数据来源：zhenxuan_querySelectProjectList表的select_apply_id字段")

        if not self.db_manager.connect():
            logger.error("❌ 数据库连接失败")
            return 0

        # 获取所有select_apply_id
        select_apply_ids = self.get_select_apply_ids()

        if not select_apply_ids:
            logger.warning("⚠️ 没有找到可用的select_apply_id")
            logger.warning("💡 请确保zhenxuan_querySelectProjectList表中有数据")
            return 0

        logger.info(f"📋 从zhenxuan_querySelectProjectList表找到 {len(select_apply_ids)} 个select_apply_id需要处理")

        total_synced = 0
        total_failed = 0

        try:
            for i, select_apply_id in enumerate(select_apply_ids, 1):
                try:
                    logger.info(f"🔄 处理第 {i}/{len(select_apply_ids)} 个: {select_apply_id}")

                    # 获取申请详情数据（select_apply_id作为API的selectApplyId参数）
                    response_data = self.fetch_apply_detail(select_apply_id)

                    if not response_data:
                        logger.error(f"❌ 获取申请详情失败: {select_apply_id}")
                        total_failed += 1
                        continue

                    # 转换数据格式
                    record_data = self.transform_data(response_data, select_apply_id)

                    # 插入数据库
                    if self.insert_record(record_data):
                        total_synced += 1
                        logger.info(f"✅ 成功同步: {select_apply_id}")
                    else:
                        total_failed += 1
                        logger.error(f"❌ 插入失败: {select_apply_id}")

                except Exception as e:
                    logger.error(f"❌ 处理记录失败: {select_apply_id}, 错误: {e}")
                    total_failed += 1

                # 添加延迟避免请求过快
                import time
                time.sleep(1)

            logger.info(f"🎉 数据同步完成！")
            logger.info(f"✅ 成功同步: {total_synced} 条记录")
            logger.info(f"❌ 失败记录: {total_failed} 条记录")
            return total_synced

        except Exception as e:
            logger.error(f"❌ 数据同步异常: {e}")
            return total_synced
        finally:
            self.db_manager.disconnect()

    def query_data(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        查询数据库中的数据

        Args:
            limit: 查询记录数限制

        Returns:
            List[Dict]: 查询结果
        """
        if not self.db_manager.connect():
            return []

        try:
            sql = """
            SELECT
                id, select_apply_id, select_rev_id, project_name, customer_name,
                project_code, project_no, business_area_value, apply_status_value,
                apply_review_status_value, rating, create_time, start_time, end_time, created_at
            FROM zhenxuan_querySelectApplyDetail
            ORDER BY created_at DESC
            LIMIT %s
            """

            with self.db_manager.get_cursor() as cursor:
                cursor.execute(sql, (limit,))
                return cursor.fetchall()

        except Exception as e:
            logger.error(f"❌ 查询数据失败: {e}")
            return []
        finally:
            self.db_manager.disconnect()


def main():
    """主程序入口"""
    import argparse

    parser = argparse.ArgumentParser(description='甄选申请详情数据获取和入库程序')
    parser.add_argument('--select-apply-id', type=str, help='指定单个甄选申请ID（select_apply_id字段值）')
    parser.add_argument('--cookie-file', type=str, help='Cookie文件路径，默认使用cookies/cookies_dict_zhenxuan.json')
    parser.add_argument('--cookie', type=str, help='更新Cookie字符串')
    parser.add_argument('--query', action='store_true', help='查询已同步的数据')
    parser.add_argument('--limit', type=int, default=10, help='查询记录数限制，默认10')
    parser.add_argument('--all', action='store_true', help='查询全部数据模式，轮询入库所有数据')

    args = parser.parse_args()

    # 创建数据获取器（使用指定的Cookie文件）
    fetcher = ZhenxuanApplyDetailFetcher(cookie_file_path=args.cookie_file)

    # 更新Cookie（如果提供）
    if args.cookie:
        fetcher.update_cookies(args.cookie)

    # 查询模式
    if args.query:
        logger.info("🔍 查询数据库中的数据...")
        results = fetcher.query_data(args.limit)

        if results:
            logger.info(f"📋 查询到 {len(results)} 条记录:")
            for i, record in enumerate(results, 1):
                logger.info(f"  {i}. {record['project_name']} ({record['project_code']})")
                logger.info(f"     申请ID: {record['select_apply_id']}")
                logger.info(f"     状态: {record['apply_status_value']}")
                logger.info(f"     评级: {record['rating']}")
                logger.info(f"     创建: {record['create_time']}")
                logger.info(f"     入库: {record['created_at']}")
                logger.info("")
        else:
            logger.info("📋 没有查询到数据")
        return

    # 单个申请ID处理模式
    if args.select_apply_id:
        logger.info(f"🔄 处理单个甄选申请ID（select_apply_id）: {args.select_apply_id}")

        # 获取申请详情数据
        response_data = fetcher.fetch_apply_detail(args.select_apply_id)

        if response_data:
            # 转换数据格式
            record_data = fetcher.transform_data(response_data, args.select_apply_id)

            # 插入数据库
            if fetcher.insert_record(record_data):
                logger.info(f"✅ 成功同步申请详情: {args.select_apply_id}")
            else:
                logger.error(f"❌ 同步失败: {args.select_apply_id}")
        else:
            logger.error(f"❌ 获取申请详情失败: {args.select_apply_id}")
        return

    # 全部数据同步模式
    if args.all:
        logger.info("🌍 全部数据同步模式启动...")
        logger.info("📋 将查询zhenxuan_querySelectProjectList表的select_apply_id字段，并入库所有可用的甄选申请详情数据")

        # 执行全部数据同步
        synced_count = fetcher.sync_all_data()

        if synced_count > 0:
            logger.info(f"🎉 全部数据同步成功！共同步 {synced_count} 条记录")
        else:
            logger.error("❌ 全部数据同步失败或没有新数据")
        return

    # 默认显示帮助信息
    parser.print_help()


if __name__ == "__main__":
    main()
