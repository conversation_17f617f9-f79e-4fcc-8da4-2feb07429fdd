"""
验证甄选信息-评审小组数据是否正确入库
"""

import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

from database.db_config import ZHENXUAN_DB_CONFIG, DatabaseManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('verify_ps_data.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def verify_ps_data():
    """验证甄选信息-评审小组数据"""
    
    logger.info("🚀 开始验证甄选信息-评审小组数据...")
    
    # 连接数据库
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    if not db_manager.connect():
        logger.error("❌ 数据库连接失败")
        return False
    
    try:
        with db_manager.get_connection() as conn:
            with conn.cursor() as cursor:
                
                # 1. 检查表是否存在
                logger.info("🔄 检查表是否存在...")
                cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.TABLES 
                WHERE TABLE_SCHEMA = 'zhenxuandb' 
                AND TABLE_NAME = 'zhenxuan_queryLocalAuditTrackHistory_ps'
                """)
                
                table_exists = cursor.fetchone()[0]
                if table_exists:
                    logger.info("✅ 表存在")
                else:
                    logger.error("❌ 表不存在")
                    return False
                
                # 2. 检查数据总数
                logger.info("🔄 检查数据总数...")
                cursor.execute("SELECT COUNT(*) FROM zhenxuan_queryLocalAuditTrackHistory_ps")
                total_count = cursor.fetchone()[0]
                logger.info(f"📊 数据总数: {total_count} 条")
                
                if total_count == 0:
                    logger.warning("⚠️ 表中没有数据")
                    return True
                
                # 3. 查看最新的几条记录
                logger.info("🔄 查看最新的记录...")
                cursor.execute("""
                SELECT 
                    id,
                    business_id,
                    work_order_msg_id,
                    step_name,
                    status,
                    audit_handler,
                    create_time,
                    finish_time,
                    score_rule_id,
                    score_order_msg_id,
                    created_at
                FROM zhenxuan_queryLocalAuditTrackHistory_ps
                ORDER BY created_at DESC
                LIMIT 10
                """)
                
                records = cursor.fetchall()
                logger.info(f"📋 最新 {len(records)} 条记录:")
                
                for i, record in enumerate(records, 1):
                    logger.info(f"   记录 {i}:")
                    logger.info(f"     ID: {record[0]}")
                    logger.info(f"     业务ID: {record[1]}")
                    logger.info(f"     工单消息ID: {record[2]}")
                    logger.info(f"     步骤名称: {record[3]}")
                    logger.info(f"     状态: {record[4]}")
                    logger.info(f"     审核处理人: {record[5]}")
                    logger.info(f"     创建时间: {record[6]}")
                    logger.info(f"     完成时间: {record[7]}")
                    logger.info(f"     评分规则ID: {record[8]}")
                    logger.info(f"     评分工单消息ID: {record[9]}")
                    logger.info(f"     入库时间: {record[10]}")
                    logger.info("")
                
                # 4. 检查字段完整性
                logger.info("🔄 检查字段完整性...")
                cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    COUNT(business_id) as has_business_id,
                    COUNT(work_order_msg_id) as has_work_order_msg_id,
                    COUNT(score_rule_id) as has_score_rule_id,
                    COUNT(score_order_msg_id) as has_score_order_msg_id,
                    COUNT(audit_process_track_id) as has_audit_process_track_id,
                    COUNT(step_name) as has_step_name,
                    COUNT(status) as has_status,
                    COUNT(audit_handler) as has_audit_handler
                FROM zhenxuan_queryLocalAuditTrackHistory_ps
                """)
                
                stats = cursor.fetchone()
                logger.info(f"📊 字段完整性统计:")
                logger.info(f"   总记录数: {stats[0]}")
                logger.info(f"   有业务ID: {stats[1]} ({stats[1]/stats[0]*100:.1f}%)")
                logger.info(f"   有工单消息ID: {stats[2]} ({stats[2]/stats[0]*100:.1f}%)")
                logger.info(f"   有评分规则ID: {stats[3]} ({stats[3]/stats[0]*100:.1f}%)")
                logger.info(f"   有评分工单消息ID: {stats[4]} ({stats[4]/stats[0]*100:.1f}%)")
                logger.info(f"   有审核流程跟踪ID: {stats[5]} ({stats[5]/stats[0]*100:.1f}%)")
                logger.info(f"   有步骤名称: {stats[6]} ({stats[6]/stats[0]*100:.1f}%)")
                logger.info(f"   有状态: {stats[7]} ({stats[7]/stats[0]*100:.1f}%)")
                logger.info(f"   有审核处理人: {stats[8]} ({stats[8]/stats[0]*100:.1f}%)")
                
                # 5. 检查不同的步骤名称
                logger.info("🔄 检查步骤名称分布...")
                cursor.execute("""
                SELECT 
                    step_name,
                    COUNT(*) as count
                FROM zhenxuan_queryLocalAuditTrackHistory_ps
                WHERE step_name IS NOT NULL
                GROUP BY step_name
                ORDER BY count DESC
                """)
                
                step_stats = cursor.fetchall()
                logger.info(f"📊 步骤名称分布 ({len(step_stats)} 种):")
                for step_name, count in step_stats:
                    logger.info(f"   {step_name}: {count} 条")
                
                # 6. 检查状态分布
                logger.info("🔄 检查状态分布...")
                cursor.execute("""
                SELECT 
                    status,
                    COUNT(*) as count
                FROM zhenxuan_queryLocalAuditTrackHistory_ps
                WHERE status IS NOT NULL
                GROUP BY status
                ORDER BY count DESC
                """)
                
                status_stats = cursor.fetchall()
                logger.info(f"📊 状态分布 ({len(status_stats)} 种):")
                for status, count in status_stats:
                    logger.info(f"   {status}: {count} 条")
                
                logger.info("✅ 数据验证完成")
                return True
                
    except Exception as e:
        logger.error(f"❌ 数据验证失败: {e}")
        return False
    finally:
        db_manager.disconnect()

def main():
    """主函数"""
    logger.info("=" * 80)
    logger.info("🚀 甄选信息-评审小组数据验证程序")
    logger.info("=" * 80)
    
    start_time = datetime.now()
    
    try:
        success = verify_ps_data()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if success:
            logger.info("=" * 80)
            logger.info("🎉 数据验证成功！")
            logger.info(f"⏱️ 总耗时: {duration:.2f} 秒")
            logger.info("=" * 80)
        else:
            logger.error("=" * 80)
            logger.error("❌ 数据验证失败！")
            logger.error(f"⏱️ 总耗时: {duration:.2f} 秒")
            logger.error("=" * 80)
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("\n⚠️ 程序被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 程序执行异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
