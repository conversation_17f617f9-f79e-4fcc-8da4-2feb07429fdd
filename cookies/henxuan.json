## 01、分析json数据结构，在mysql8数据库里创建数据表zhenxuan_querySelectStage
## 02、特别说明：取数据表zhenxuan_zhenxuan_querySelectProjectList的 projectMsgId 字段的值，作为 projectMsgId 的值 
## 03、参照如下curl脚本，创建数据库表同名的py程序，带cookie访问url获取json数据（注意cookis文件的数据为json格式，提交的cookie为字符串格式，文件里多个同name的项都要带上），轮询将数据入库，入参 projectMsgId 也入库：


zhenxuan_zhenxuan_querySelectProjectList

{
  "busiDate": "2025-07-09 02:01:52",
  "code": "000000",
  "message": null,
  "resultBody": {
    "total": 1,
    "size": 10,
    "current": 1,
    "records": [
      {
        "projectMsgId": "1812755334174785536",
        "workOrderMsgId": null,
        "shutOrderMsgId": null,
        "selectMsgId": "1903979207947370496",
        "selectApplyId": "1904468953890996224",
        "projectName": "中山市小榄分局车载警务法眼系统项目",
        "selectName": "中山移动小榄镇区车载警务法眼系统项目",
        "count": "1",
        "projectNo": "CMGDZSICT20240715013",
        "selectType": null,
        "selectTypeValue": null,
        "projectType": "10",
        "projectLabel": "54",
        "businessArea": "760",
        "businessAreaValue": "中山",
        "startTime": "2024-07-22 00:00:00",
        "selectStatus": "1002",
        "selectStatusValue": "审核通过(已制定方案)",
        "initiateDepartment": null,
        "createTime": "2024-07-15 15:45:25",
        "isFixedSoftness": "0",
        "createStaff": "hehaoming",
        "createStaffValue": "何浩明",
        "nextTodoHandler": "hehaoming",
        "nextTodoHandlerValue": "何浩明",
        "isOperable": "0",
        "changeType1": null,
        "changeType2": null,
        "isTerminable": "0",
        "isAllowSecond": "1",
        "selectCategory": "1",
        "selectCategoryValue": "项目甄选",
        "dpcsSelectSecondNegotiate": null
      }
    ],
    "pages": 1
  }
}



=======================================================================================================
001----------------------------
{
	
## 01、分析json数据结构，在mysql8数据库里创建数据表 zhenxuan_querySelectStage

{
  "busiDate": "2025-07-09 02:25:38",
  "code": "000000",
  "message": null,
  "resultBody": {
    "selectStage": "1005",
    "selectStageValue": "甄选评审",
    "selectApplyId": "1904468953890996224",
    "selectApplyStatus": "1004",
    "selectMsgId": "1903979207947370496",
    "selectPlanStatus": "1003",
    "projectMsgId": "1812755334174785536",
    "selectDemandStatus": "1002",
    "noticeId": "19196",
    "selectClarifyId": null,
    "reviewTeamMsgId": "1904471482624294912",
    "clarifyWorkOrderMsgId": null,
    "secondNegotiateId": null,
    "workOrderMsgId": null,
    "shutOrderMsgId": null
  }
}

[
## 02、特别说明：取数据表 zhenxuan_querySelectProjectList 的 projectMsgId 字段的值，作为 projectMsgId 的值，注意有个入参selectRevId 

## 03、参照如下curl脚本，创建数据库表同名的py程序，带cookie访问url获取json数据（注意cookis文件的数据为json格式，提交的cookie为字符串格式，文件里多个同name的项都要带上），轮询将数据入库，入参 projectMsgId= 也入库：
curl -X GET 'https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectStage?projectMsgId=1812755334174785536&selectRevId' -H 'Host: dict.gmcc.net:30722' -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' -H 'Accept: application/json, text/plain, */*' -H 'Accept-Encoding: gzip, deflate, br, zstd' -H 'sec-ch-ua-platform: "Windows"' -H 'Authorization: Bearer d25c514c-e026-4ddf-b455-9929dfcd3cfb' -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' -H 'sec-ch-ua-mobile: ?0' -H 'Sec-Fetch-Site: same-origin' -H 'Sec-Fetch-Mode: cors' -H 'Sec-Fetch-Dest: empty' -H 'Referer: https://dict.gmcc.net:30722/ptn/main/selectDemand' -H 'Accept-Language: zh-CN,zh;q=0.9,ee;q=0.8' -H 'Cookie: BSS-SESSION=NjU3M2VhYjYtNWRlYi00NGMzLTg1OWQtZDg2YzNiMjdkZThi; NewoaAppToDones=; NewoaAppToReads=; BSS-SESSION=OTkyMWJiNmMtMWNjMi00OGM0LTllYjAtNmI0M2Q0ODA2NTEy; isLogin=ImlzTG9naW4i; requestId=eb0331f0-5bec-11f0-b2ab-d7d92ba136f7; systemUserCode=InpoZW5nZGV3ZW4i; jsession_id_4_boss=n603BE9BD8C39CA4832672F7B13F2B61E-1' -k

]

}


002
{
##需求信息	
## 01、分析json数据结构，在mysql8数据库里创建数据表 zhenxuan_queryLocalAuditTrackHistory ，需要加projectMsgId字段
{
  "busiDate": "2025-07-09 02:10:54",
  "code": "000000",
  "message": null,
  "resultBody": [
    {
      "auditProcessTrackId": "1812755334174785537",
      "businessId": "1812755334174785536",
      "stepName": "发起甄选需求",
      "createTime": "2024-07-15 15:45:25",
      "finishTime": "2024-07-15 15:45:25",
      "status": "已处理",
      "auditHandler": "hehaoming",
      "auditRemark": null
    },
    {
      "auditProcessTrackId": "1812755334178979840",
      "businessId": "1812755334174785536",
      "stepName": "甄选需求发起审批-一级",
      "createTime": "2024-07-15 15:45:25",
      "finishTime": "2024-07-15 17:11:39",
      "status": "已处理-通过",
      "auditHandler": "huangbo7",
      "auditRemark": "同意"
    },
    {
      "auditProcessTrackId": "1812777033935011840",
      "businessId": "1812755334174785536",
      "stepName": "甄选需求发起审批-二级",
      "createTime": "2024-07-15 17:11:39",
      "finishTime": "2024-07-15 17:11:50",
      "status": "已处理-通过",
      "auditHandler": "huangbo7",
      "auditRemark": "同意"
    }
  ]
}

## 02、特别说明：取数据表zhenxuan_querySelectProjectList的 projectMsgId 字段的值，作为入参 businessId 的值 ，入参 workOrderMsgId 的值为null，即为空。入参"stepName"值为""。
      【  "projectMsgId": "1877647623078199296",  "workOrderMsgId": "GD76020250110171223690832"】

## 03、参照如下curl脚本，创建数据库表同名的py程序，带cookie访问url获取json数据（注意cookis文件的数据为json格式，提交的cookie为字符串格式，文件里多个同name的项都要带上），轮询将数据入库轮询速度不限制，projectMsgId 也入库：
curl -X POST 'https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/queryLocalAuditTrackHistory' -H 'Host: dict.gmcc.net:30722' -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' -H 'Accept: application/json, text/plain, */*' -H 'Accept-Encoding: gzip, deflate, br, zstd' -H 'Content-Type: application/json' -H 'sec-ch-ua-platform: "Windows"' -H 'Authorization: Bearer d25c514c-e026-4ddf-b455-9929dfcd3cfb' -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' -H 'Content-Type: application/json;charset=UTF-8' -H 'sec-ch-ua-mobile: ?0' -H 'Origin: https://dict.gmcc.net:30722' -H 'Sec-Fetch-Site: same-origin' -H 'Sec-Fetch-Mode: cors' -H 'Sec-Fetch-Dest: empty' -H 'Referer: https://dict.gmcc.net:30722/ptn/main/selectDemand/detail' -H 'Accept-Language: zh-CN,zh;q=0.9,ee;q=0.8' -H 'Cookie: BSS-SESSION=NjU3M2VhYjYtNWRlYi00NGMzLTg1OWQtZDg2YzNiMjdkZThi; NewoaAppToDones=; NewoaAppToReads=; BSS-SESSION=OTkyMWJiNmMtMWNjMi00OGM0LTllYjAtNmI0M2Q0ODA2NTEy; isLogin=ImlzTG9naW4i; requestId=eb0331f0-5bec-11f0-b2ab-d7d92ba136f7; systemUserCode=InpoZW5nZGV3ZW4i; jsession_id_4_boss=n603BE9BD8C39CA4832672F7B13F2B61E-1' -d '{"businessId":"1812755334174785536","workOrderMsgId":null,"stepName":""}' -k	

}




00
{
## 01、分析json数据结构，在mysql8数据库里创建数据表zhenxuan_querySelectProjectDetail
	{
  "busiDate": "2025-07-08 23:09:56",
  "code": "000000",
  "message": null,
  "resultBody": {
    "projectMsgId": "1877647623078199296",
    "workOrderMsgId": "GD76020250110171223690832",
    "shutOrderMsgId": null,
    "projectName": "银马染厂二期厂区监控与网络安装工程",
    "projectCode": "CMGDZSICT***********",
    "groupProjectCode": null,
    "projectType": "10",
    "projectTypeValue": "DICT项目",
    "industry": "GN",
    "industryValue": "工业能源",
    "isPmoManage": "0",
    "isPmoManageValue": "否",
    "isSubSign": "3000",
    "isSubSignValue": "",
    "isInvestmentProject": "0",
    "isInvestmentProjectValue": "否",
    "engineeringProjectCode": null,
    "projectLabel": "10",
    "projectLabelValue": "ICT业务-IOT",
    "selectName": "某厂区监控与网络安装工程（项目编号：***********）",
    "selectStatus": "1002",
    "selectStatusValue": "审核通过(已制定方案)",
    "selectType": null,
    "selectTypeValue": null,
    "startTime": "2025-01-10",
    "endTime": "2025-01-26",
    "selectDemandContent": "某厂区监控与网络安装工程（项目编号：***********）已由中国移动通信集团广东有限公司中山分公司批准实施，甄选人为中国移动通信集团广东有限公司中山分公司。已入围《中山分公司2019年-2025年ICT业务提供商入围招募项目（互联网行业）》的供应商均可前来参加应答。",
    "selectBudget": "150000.00",
    "selectBudgetValue": "150,000",
    "nonTaxSelectBudget": "137614.68",
    "nonTaxSelectBudgetValue": "137,614.68",
    "isFixedSoftness": "0",
    "isFixedSoftnessValue": "否",
    "selectBasis": "1877644632950489089",
    "selectBasis2": "1877644632950489088",
    "selectBasis3": "1877644632971460608",
    "selectBasis4": null,
    "selectNoticeType": null,
    "selectNoticeTypeValue": null,
    "cityDecideFile": null,
    "cityDecideFileVo": null,
    "selectBasisVo": [],
    "selectBasis2Vo": [],
    "selectBasis3Vo": [
      {
        "fileId": "1877647527628423168",
        "busineesType": "partner",
        "fileName": "项目需求书.pdf",
        "folder": null,
        "sortCode": null,
        "fileCreateTime": "2025-01-10 17:23:50",
        "businessId": "1877644632971460608",
        "userName": null,
        "busineesId": null
      }
    ],
    "selectBasis4Vo": null,
    "businessArea": "760",
    "businessAreaValue": "中山",
    "createTime": "2025-01-10 17:24:13",
    "createStaff": "廖旸",
    "initiateDepartment": null,
    "customerName": "中山市银马纺织印染有限公司",
    "firstScene": "GN_ZHYQ",
    "firstSceneValue": "智慧园区",
    "secondScene": "202305171246420004",
    "secondSceneValue": "",
    "projectNo": "CMGDZSICT***********",
    "iprojectId": null,
    "terminationReason": null,
    "terminationReasonValue": null,
    "otherExplain": null,
    "terminationExplain": null,
    "phone": "***********",
    "employeeName": "郑德文",
    "email": "<EMAIL>",
    "nextTodoHandler": "廖旸",
    "nextTodoHandlerValue": "liaoyang",
    "nextAuditHandler": "zhuhongzhuan",
    "nextShutdownHandler": null,
    "shutdownBasis": null,
    "shutdownBasisVo": null,
    "currentAuditStep": "1",
    "currentAuditStep1": null,
    "selectCategory": "1",
    "selectCategoryValue": "项目甄选",
    "selectDemandType": "2",
    "selectDemandTypeValue": "安装服务",
    "isTSolution": "否",
    "isNonPresale": null,
    "nonPresaleReason": "",
    "nonPresaleFile": "",
    "nonPresaleFileList": null,
    "projectStage": null,
    "projectProgress": null,
    "projectScope": "1001",
    "docNumberSub": "中山移集客DICT纪要【2025】号"
  }
}
	
	
## 02、特别说明：取数据表zhenxuan_querySelectProjectList的 projectMsgId 字段的值，作为 projectMsgId 的值 


## 03、参照如下curl脚本，创建数据库表同名的py程序，带cookie访问url获取json数据（注意cookis文件的数据为json格式，提交的cookie为字符串格式，文件里多个同name的项都要带上），轮询将数据入库，入参 projectMsgId= 也入库：
curl -X GET 'https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectDetail?projectMsgId=1877647623078199296' -H 'Host: dict.gmcc.net:30722' -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' -H 'Accept: application/json, text/plain, */*' -H 'Accept-Encoding: gzip, deflate, br, zstd' -H 'sec-ch-ua-platform: "Windows"' -H 'Authorization: Bearer d25c514c-e026-4ddf-b455-9929dfcd3cfb' -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' -H 'sec-ch-ua-mobile: ?0' -H 'Sec-Fetch-Site: same-origin' -H 'Sec-Fetch-Mode: cors' -H 'Sec-Fetch-Dest: empty' -H 'Referer: https://dict.gmcc.net:30722/ptn/main/selectDemand/detail' -H 'Accept-Language: zh-CN,zh;q=0.9,ee;q=0.8' -H 'Cookie: BSS-SESSION=NjA5MjkxODktMTUwMi00MWRjLThmZDYtM2E5MDhkMjQ3YzJm; NewoaAppToDones=; NewoaAppToReads=; BSS-SESSION=OTkyMWJiNmMtMWNjMi00OGM0LTllYjAtNmI0M2Q0ODA2NTEy; isLogin=ImlzTG9naW4i; requestId=eb0331f0-5bec-11f0-b2ab-d7d92ba136f7; systemUserCode=InpoZW5nZGV3ZW4i; jsession_id_4_boss=n603BE9BD8C39CA4832672F7B13F2B61E-1' -k
	
}


0
{

## 01、分析json数据结构，在mysql8数据库里创建数据表zhenxuan_querySelectApplyDetail
{
  "busiDate": "2025-07-09 00:06:53",
  "code": "000000",
  "message": null,
  "resultBody": {
    "selectApplyId": "1881721476049977344",
    "selectRevId": "1880819193661538304",
    "selectRevName": "某厂区监控与网络安装工程（项目编号：***********）",
    "projectName": "银马染厂二期厂区监控与网络安装工程",
    "customerName": "中山市银马纺织印染有限公司",
    "projectCode": "CMGDZSICT***********",
    "projectNo": "CMGDZSICT***********",
    "selectType": "1001",
    "selectName": "某厂区监控与网络安装工程（项目编号：***********）",
    "selectTypeValue": "公开甄选",
    "businessArea": "760",
    "businessAreaValue": "中山",
    "initiateDepartment": null,
    "projectType": "10",
    "projectTypeValue": "DICT项目",
    "createTime": "2025-01-21 11:12:15",
    "startTime": "2025-01-10",
    "endTime": "2025-01-26",
    "applyStatusValue": "已结束",
    "applyReviewStatusValue": "已反馈结果",
    "reviewFileBusinessId": "1882616398932262912",    
    "workOrderMsgId": "GD76020250124171237139654",
    "scoreOrderMsgId": "GD76020250124110116980490",
    "selectProjectDemandDetailVo": null,
    "scoreRuleId": "1880819171058434048",
    "ruleItemAveScore": null,
    "isNeedVerification": "0",
    "isFinishVerification": null,
    "nonTaxSelectBudget": "137614.68",
    "actionRemark": "",
    "pushNotice": null,
    "isTechnicalReview": null,
    "bidFlagDesc": "否",
    "bidOpeningTime": null,
    "rating": "D",
    "isPreReview": null,
    "selectResultDoc": null,
    "resultInputType": null,
    "resultTitle": null,
    "resultContent": null,
    "docNumberSub": null,
    "docNumber": null,
    "selectResultMeet": null,
    "selectResultMeetList": null,
    "selectMsgId": "1880819193661538304",
    "dpcsSelectSecondNegotiate": null
  }
}

	
## 02、特别说明：取数据表zhenxuan_querySelectProjectList的 selectApplyId 字段的值，作为入参 selectApplyId 的值 


## 03、参照如下curl脚本，创建数据库表同名的py程序，带cookie访问url获取json数据（注意cookis文件的数据为json格式，提交的cookie为字符串格式，文件里多个同name的项都要带上），轮询将数据入库，入参 projectMsgId= 也入库：

curl -X GET 'https://dict.gmcc.net:30722/partner/materialManage/pnrSelect/querySelectApplyDetail?selectApplyId=1881721476049977344' -H 'Host: dict.gmcc.net:30722' -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' -H 'Accept: application/json, text/plain, */*' -H 'Accept-Encoding: gzip, deflate, br, zstd' -H 'sec-ch-ua-platform: "Windows"' -H 'Authorization: Bearer d25c514c-e026-4ddf-b455-9929dfcd3cfb' -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' -H 'sec-ch-ua-mobile: ?0' -H 'Sec-Fetch-Site: same-origin' -H 'Sec-Fetch-Mode: cors' -H 'Sec-Fetch-Dest: empty' -H 'Referer: https://dict.gmcc.net:30722/ptn/main/selectDemand/detail' -H 'Accept-Language: zh-CN,zh;q=0.9,ee;q=0.8' -H 'Cookie: BSS-SESSION=NjA5MjkxODktMTUwMi00MWRjLThmZDYtM2E5MDhkMjQ3YzJm; NewoaAppToDones=; NewoaAppToReads=; BSS-SESSION=OTkyMWJiNmMtMWNjMi00OGM0LTllYjAtNmI0M2Q0ODA2NTEy; isLogin=ImlzTG9naW4i; requestId=eb0331f0-5bec-11f0-b2ab-d7d92ba136f7; systemUserCode=InpoZW5nZGV3ZW4i; jsession_id_4_boss=n603BE9BD8C39CA4832672F7B13F2B61E-1' -k
}









004
{

## 01、分析json数据结构，在mysql8数据库里创建数据表zhenxuan_queryPartnerSelectDetail，要增加字段selectMsgId
{
  "busiDate": "2025-07-09 03:01:26",
  "code": "000000",
  "message": null,
  "resultBody": {
    "projectMsgId": "1812755334174785536",
    "projectName": "中山市小榄分局车载警务法眼系统项目",
    "projectCode": "CMGDZSICT20240715013",
    "projectNo": "CMGDZSICT20240715013",
    "iprojectId": null,
    "groupProjectCode": null,
    "projectType": "10",
    "selectNoticeType": null,
    "projectTypeValue": "DICT项目",
    "noticeId": null,
    "noticeVo": null,
    "industry": "ZF",
    "industryValue": "执法",
    "isPmoManage": "0",
    "isPmoManageValue": "否",
    "isSubSign": "3000",
    "isSubSignValue": "",
    "isInvestmentProject": null,
    "isInvestmentProjectValue": null,
    "engineeringProjectCode": null,
    "projectLabel": "54",
    "projectLabelValue": "ICT业务-其他",
    "selectName": "中山移动小榄镇区车载警务法眼系统项目",
    "selectStatus": "1002",
    "selectStatusValue": "审核通过(已制定方案)",
    "selectType": "1001",
    "selectTypeValue": "公开甄选",
    "selectIndustry": null,
    "selectIndustryValue": null,
    "startTime": "2024-07-22",
    "endTime": "2025-03-27",
    "initiateDepartment": null,
    "customerName": "中山市公安局交通警察支队小榄大队",
    "firstScene": "ZF_OTHER",
    "firstSceneValue": "其他",
    "secondScene": "ZF_OTHER_QT",
    "secondSceneValue": "",
    "selectDemandContent": "乙方为甲方巡逻车辆开发“车载警务法眼”系统，利用北斗高精定位技术，为甲方巡逻车辆开发车载警务法眼系统，帮助警员在巡逻执勤过程中，对过往车辆快速感知信息、迅速比对数据，实现高效动态的实时精准缉查、执法抓拍，提高执法效率；同时通过“车载警务法眼”系统勤务管理功能，帮助主管部门实时掌握警车巡逻动态、警员出警执法量，提升车辆配置和警员考核管理水平，革新执法管理模式。",
    "selectBudget": "216000.00",
    "nonTaxSelectBudget": null,
    "isFixedSoftness": "0",
    "isFixedSoftnessValue": "否",
    "createStaff": "何浩明",
    "planCreateStaff": "何浩明",
    "nextTodoHandler": "hehaoming",
    "nextTodoHandlerValue": "何浩明",
    "selectBasis": "1812754903310712833",
    "selectBasisVo": [
      {
        "fileId": "1812755299567583232",
        "busineesType": "partner",
        "fileName": "（业主合同）中山市公安局小榄分局“车载警务法眼”系统项目(2).docx",
        "folder": null,
        "sortCode": null,
        "fileCreateTime": "2024-07-15 15:45:17",
        "businessId": "1812754903310712833",
        "userName": null,
        "busineesId": null
      }
    ],
    "selectBasis2": "1812754903306518528",
    "selectBasis2Vo": [],
    "selectBasis3": "1812754903310712832",
    "selectBasis3Vo": [
      {
        "fileId": "1812755286703652864",
        "busineesType": "partner",
        "fileName": "（业主合同）中山市公安局小榄分局“车载警务法眼”系统项目(2).docx",
        "folder": null,
        "sortCode": null,
        "fileCreateTime": "2024-07-15 15:45:14",
        "businessId": "1812754903310712832",
        "userName": null,
        "busineesId": null
      }
    ],
    "businessArea": "760",
    "businessAreaValue": "中山",
    "selectRevId": "1903979207947370496",
    "selectMsgId": "1903979207947370496",
    "selectRevName": " 中山移动小榄镇区车载警务法眼系统项目",
    "selectRevDescribe": "方巡逻车辆开发“车载警务法眼”系统，利用北斗高精定位技术，为甲方巡逻车辆开发车载警务法眼系统，帮助警员在巡逻执勤过程中，对过往车辆快速感知信息、迅速比对数据，实现高效动态的实时精准缉查、执法抓拍，提高执法效率；同时通过“车载警务法眼”系统勤务管理功能，帮助主管部门实时掌握警车巡逻动态、警员出警执法量，提升车辆配置和警员考核管理水平，革新执法管理模式。",
    "operatorContact": "何浩明",
    "operatorContactPhone": "***********",
    "operatorContactEmail": "<EMAIL>",
    "fileId": "1903976770381463552",
    "fileId1": "1903976770385657856",
    "fileId2": "1903976770394046464",
    "fileId3": "1903976770473738240",
    "fileIdVo": [
      {
        "fileId": "1904350553202147328",
        "busineesType": "otherPlanSelectFile",
        "fileName": "中山-CMGDZSICT20240715013-其他文件.pdf",
        "folder": null,
        "sortCode": null,
        "fileCreateTime": "2025-03-25 09:52:07",
        "businessId": "1903976770394046464",
        "userName": null,
        "busineesId": null
      },
      {
        "fileId": "1904350439695892480",
        "busineesType": "selectionPlanSelectFile",
        "fileName": "中山-CMGDZSICT20240715013-甄选文件.pdf",
        "folder": null,
        "sortCode": null,
        "fileCreateTime": "2025-03-25 09:51:40",
        "businessId": "1903976770381463552",
        "userName": null,
        "busineesId": null
      }
    ],
    "workOrderMsgId": "GD76020250325095234501317",
    "selectPartnerRelDtoList": null,
    "phone": null,
    "employeeName": null,
    "email": null,
    "selectTime": null,
    "selectAddress": null,
    "terminationReason": null,
    "otherExplain": null,
    "terminationExplain": null,
    "shutdownBasis": null,
    "nextAuditHandler": "huangzhenguo",
    "shutdownBasisVo": null,
    "productMeetingBasis": null,
    "productMeetingBasisVo": null,
    "productMeetingBasis2": null,
    "productMeetingBasisVo2": null,
    "currentAuditStep": null,
    "selectCategory": "1",
    "selectCategoryValue": "项目甄选",
    "selectReviewStatus": "1003",
    "passTime": null,
    "selectDemandType": null,
    "selectDemandTypeValue": null,
    "isOnlineApply": "1",
    "selectTitle": "关于《 中山市小榄分局车载警务法眼系统项目》的甄选方案评审会议纪要",
    "selectContent": "<p>\t根据ICT业务管理要求，系统集成公司于2024年7月17日组织集成、北部分公司部门在全球通大厦10楼会议室，对《 中山移动小榄镇区车载警务法眼系统项目》甄选方案进行评审，现将评审内容纪要如下：</p><p>\t<strong>一、评审内容</strong></p><p>\t<strong>1、资格条件设置：</strong>根据甄选模块工作内容，将按客户招标文件中对应模块的资格条件进行设置。</p><p>\t<strong>2、评审方法：</strong>本次甄选采用综合评分法，评标按照《中国移动广东公司ICT业务合作管理办法》评分规定，技术评分分值为70分，价格评分分值为30分，综合得分=技术评分+价格评分。</p><p>\t<strong>3、技术评分：</strong>主要从综合资质、项目团队、服务方案三大方面对应答方进行打分，具体评分项包括专业资质认证、管理体系认证、主营业务收入、同类项目经验、团队素质以及与客户招标评分一致的服务方案要求等，满分100分，各项分值设置合理。最终技术评分=技术打分/100*70，技术打分按照技术打分表来计算得分。</p><p>\t<strong>4、价格评分：</strong>价格评分=（基准价/应答人报价）*30，以满足甄选文件要求且有效应答人报价中价格最低的甄选报价为基准价。</p><p>\t<strong>5、模块划分及限价：</strong>综合评估项目交付需求，甄选模块按横向/纵向进行划分，划分为模块二集成服务、模块二安装服务、模块三硬件设备（含套软）、模块四运营服务、模块五软件开发，各模块甄选限价已经设计院进行造价审核，具体如下：</p><p><br></p><table><tbody><tr><td data-row=\"1\" class=\"ql-align-center\">模块序号</td><td data-row=\"1\" class=\"ql-align-center\">模块名称</td><td data-row=\"1\" class=\"ql-align-center\">模块限价（不含税/元）</td></tr><tr><td data-row=\"2\" class=\"ql-align-center\">1</td><td data-row=\"2\" class=\"ql-align-center\">集成服务</td><td data-row=\"2\" class=\"ql-align-center\">203773.58元</td></tr><tr><td data-row=\"3\" class=\"ql-align-center\">2</td><td data-row=\"3\" class=\"ql-align-center\">安装服务</td><td data-row=\"3\" class=\"ql-align-center\">0元</td></tr><tr><td data-row=\"4\" class=\"ql-align-center\">3</td><td data-row=\"4\" class=\"ql-align-center\">硬件设备</td><td data-row=\"4\" class=\"ql-align-center\">0元</td></tr><tr><td data-row=\"5\" class=\"ql-align-center\">4</td><td data-row=\"5\" class=\"ql-align-center\">运营服务</td><td data-row=\"5\" class=\"ql-align-center\">0元</td></tr></tbody></table><p>\t<strong>6、其他：无</strong></p><p>\t<strong>二、审议意见</strong></p><p>\t各单位与会代表围绕的《 中山市小榄分局车载警务法眼系统项目》甄选方案进行深入讨论，一致同意该方案。</p><p>\t</p><p>\t特此纪要。</p><p>\t</p><p>\t与会人员：</p><p>\t集成部门：谢明明</p><p>\t北部分公司：刘烨、唐晓芳</p><p><br></p><p><br></p><p><br></p>",
    "auditTeamIds": "1000024091,1000020277,1000032013",
    "auditTeamHandler": "刘烨,唐晓芳,谢明明",
    "documentNo": "10",
    "docNumberSub": "中山移政企DICT纪要【2025】10号",
    "isTSolution": null,
    "selectRuleVo": {
      "scoreRuleId": "1904002654635212800",
      "createTime": "2025-03-24",
      "selectMsgId": null,
      "projectMsgId": "1812755334174785536",
      "isCriteria": "1",
      "isWeight": "1",
      "priceFullMarks": "100",
      "priceWeight": "30",
      "status": "1",
      "ruleReviewList": [
        {
          "ruleReviewId": "1904002654643601408",
          "ruleReviewName": "技术评分项",
          "mark": "100",
          "reviewLevel": "三层",
          "weight": "70",
          "scoreRuleId": "1904002654635212800",
          "score": "100",
          "expanded": true,
          "itemList": [
            {
              "ruleReviewItemsId": "1904002654647795712",
              "firstLevel": "综合资质（30分）",
              "secondLevel": "主体资格（9分）",
              "thirdLevel": "专业资质认证",
              "reviewDescribe": "/",
              "maxScore": "5",
              "ruleReviewId": "1904002654643601408",
              "itemNumber": "1"
            },
            {
              "ruleReviewItemsId": "1904002654651990016",
              "firstLevel": "综合资质（30分）",
              "secondLevel": "主体资格（9分）",
              "thirdLevel": "管理体系认证",
              "reviewDescribe": "/",
              "maxScore": "4",
              "ruleReviewId": "1904002654643601408",
              "itemNumber": "2"
            },
            {
              "ruleReviewItemsId": "1904002654656184320",
              "firstLevel": "综合资质（30分）",
              "secondLevel": "人力资源（2分）",
              "thirdLevel": "管理人员学历",
              "reviewDescribe": "/",
              "maxScore": "2",
              "ruleReviewId": "1904002654643601408",
              "itemNumber": "3"
            },
            {
              "ruleReviewItemsId": "1904002654660378624",
              "firstLevel": "综合资质（30分）",
              "secondLevel": "项目经验（3分）",
              "thirdLevel": "同类项目经验",
              "reviewDescribe": "/",
              "maxScore": "3",
              "ruleReviewId": "1904002654643601408",
              "itemNumber": "4"
            },
            {
              "ruleReviewItemsId": "1904002654660378625",
              "firstLevel": "综合资质（30分）",
              "secondLevel": "响应能力（16分）",
              "thirdLevel": "项目响应能力",
              "reviewDescribe": "/",
              "maxScore": "16",
              "ruleReviewId": "1904002654643601408",
              "itemNumber": "5"
            },
            {
              "ruleReviewItemsId": "1904002654664572928",
              "firstLevel": "项目团队（30分）",
              "secondLevel": "团队素质（25分）",
              "thirdLevel": "项目经理素质",
              "reviewDescribe": "/",
              "maxScore": "10",
              "ruleReviewId": "1904002654643601408",
              "itemNumber": "6"
            },
            {
              "ruleReviewItemsId": "1904002654668767232",
              "firstLevel": "项目团队（30分）",
              "secondLevel": "团队素质（25分）",
              "thirdLevel": "团队成员素质",
              "reviewDescribe": "/",
              "maxScore": "15",
              "ruleReviewId": "1904002654643601408",
              "itemNumber": "7"
            },
            {
              "ruleReviewItemsId": "1904002654668767233",
              "firstLevel": "项目团队（30分）",
              "secondLevel": "设备投入及其他（5分）",
              "thirdLevel": "工器具投入",
              "reviewDescribe": "/",
              "maxScore": "5",
              "ruleReviewId": "1904002654643601408",
              "itemNumber": "8"
            },
            {
              "ruleReviewItemsId": "1904002654672961536",
              "firstLevel": "服务方案（40分）（可要求述标）",
              "secondLevel": "实施方案（35分）",
              "thirdLevel": "项目理解",
              "reviewDescribe": "/",
              "maxScore": "20",
              "ruleReviewId": "1904002654643601408",
              "itemNumber": "9"
            },
            {
              "ruleReviewItemsId": "1904002654677155840",
              "firstLevel": "服务方案（40分）（可要求述标）",
              "secondLevel": "实施方案（35分）",
              "thirdLevel": "质量控制",
              "reviewDescribe": "/",
              "maxScore": "15",
              "ruleReviewId": "1904002654643601408",
              "itemNumber": "10"
            },
            {
              "ruleReviewItemsId": "1904002654681350144",
              "firstLevel": "服务方案（40分）（可要求述标）",
              "secondLevel": "服务承诺（5分）",
              "thirdLevel": "额外服务承诺",
              "reviewDescribe": "/",
              "maxScore": "5",
              "ruleReviewId": "1904002654643601408",
              "itemNumber": "11"
            },
            {
              "ruleReviewItemsId": null,
              "firstLevel": null,
              "secondLevel": null,
              "thirdLevel": null,
              "reviewDescribe": "合计",
              "maxScore": "100",
              "ruleReviewId": null,
              "itemNumber": null
            }
          ]
        }
      ]
    },
    "selectLevel": "普通项目甄选",
    "pushNotice": null,
    "createYear": "2025",
    "reviewTeamMsgId": "1904471482624294912",
    "dpcsExpertVos": [
      {
        "expertId": "1000024091",
        "expertName": "刘烨",
        "region": "760",
        "username": "liuye2",
        "userId": null,
        "regionName": "中山",
        "applicableIndustry": "ALL",
        "applicableIndustryName": "全行业",
        "roleCode": null,
        "roleName": null,
        "qualifications": null,
        "qualificationsName": null,
        "departmentId": null,
        "departmentName": "政企客户中心",
        "newDepartmentName": "政企客户中心",
        "officeName": "DICT项目拓展室",
        "phone": "13549818279",
        "email": "<EMAIL>",
        "states": null,
        "remark": null,
        "postRank": "006",
        "postRankName": "6级",
        "title": "0000",
        "titleName": "无",
        "serviceYears": "3",
        "serviceYearsLabel": "3年",
        "createDate": "2024-09-03 17:07:13",
        "selectMode": null,
        "isChangeTitle": null,
        "titleChangeReason": null,
        "titleChangeFile": null,
        "titleChangeFileVo": null,
        "isDisabled": null
      },
      {
        "expertId": "1000020277",
        "expertName": "唐晓芳",
        "region": "760",
        "username": "tangxiaofang",
        "userId": null,
        "regionName": "中山",
        "applicableIndustry": "ALL",
        "applicableIndustryName": "全行业",
        "roleCode": null,
        "roleName": null,
        "qualifications": null,
        "qualificationsName": null,
        "departmentId": "",
        "departmentName": "政企客户中心",
        "newDepartmentName": "政企客户中心",
        "officeName": "DICT项目拓展室",
        "phone": "13822706762",
        "email": "<EMAIL>",
        "states": null,
        "remark": null,
        "postRank": "007",
        "postRankName": "7级",
        "title": "无",
        "titleName": "",
        "serviceYears": "10",
        "serviceYearsLabel": "10年及以上",
        "createDate": "2022-11-04 10:57:00",
        "selectMode": null,
        "isChangeTitle": null,
        "titleChangeReason": null,
        "titleChangeFile": null,
        "titleChangeFileVo": null,
        "isDisabled": null
      },
      {
        "expertId": "1000032013",
        "expertName": "谢明明",
        "region": "760",
        "username": "xiemingming",
        "userId": null,
        "regionName": "中山",
        "applicableIndustry": "ALL",
        "applicableIndustryName": "全行业",
        "roleCode": null,
        "roleName": null,
        "qualifications": null,
        "qualificationsName": null,
        "departmentId": "",
        "departmentName": "项目集成室",
        "newDepartmentName": "中山DICT中心（数智化中心）",
        "officeName": "行业支撑一室",
        "phone": "13928119811",
        "email": "<EMAIL>",
        "states": null,
        "remark": null,
        "postRank": "008",
        "postRankName": "8级",
        "title": "无",
        "titleName": "",
        "serviceYears": "10",
        "serviceYearsLabel": "10年及以上",
        "createDate": "2022-11-04 10:57:00",
        "selectMode": null,
        "isChangeTitle": null,
        "titleChangeReason": null,
        "titleChangeFile": null,
        "titleChangeFileVo": null,
        "isDisabled": null
      }
    ]
  }
}
	
## 02、特别说明：取数据表 zhenxuan_querySelectProjectList的 selectMsgId 字段的值，作为 selectRevId 的值 


## 03、参照如下curl脚本，创建数据库表同名的py程序，带cookie访问url获取json数据（注意cookis文件的数据为json格式，提交的cookie为字符串格式，文件里多个同name的项都要带上），轮询将数据入库，selectMsgId要入库：
curl -X GET 'https://dict.gmcc.net:30722/partner/materialManage/pnrSelect/queryPartnerSelectDetail?selectRevId=1903979207947370496' -H 'Host: dict.gmcc.net:30722' -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' -H 'Accept: application/json, text/plain, */*' -H 'Accept-Encoding: gzip, deflate, br, zstd' -H 'sec-ch-ua-platform: "Windows"' -H 'Authorization: Bearer d25c514c-e026-4ddf-b455-9929dfcd3cfb' -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' -H 'sec-ch-ua-mobile: ?0' -H 'Sec-Fetch-Site: same-origin' -H 'Sec-Fetch-Mode: cors' -H 'Sec-Fetch-Dest: empty' -H 'Referer: https://dict.gmcc.net:30722/ptn/main/selectDemand/detail' -H 'Accept-Language: zh-CN,zh;q=0.9,ee;q=0.8' -H 'Cookie: BSS-SESSION=NjU3M2VhYjYtNWRlYi00NGMzLTg1OWQtZDg2YzNiMjdkZThi; NewoaAppToDones=; NewoaAppToReads=; BSS-SESSION=OTkyMWJiNmMtMWNjMi00OGM0LTllYjAtNmI0M2Q0ODA2NTEy; isLogin=ImlzTG9naW4i; requestId=eb0331f0-5bec-11f0-b2ab-d7d92ba136f7; systemUserCode=InpoZW5nZGV3ZW4i; jsession_id_4_boss=n603BE9BD8C39CA4832672F7B13F2B61E-1' -k
}


0041==
{

## 01、分析json数据结构，在mysql8数据库里创建数据表zhenxuan_querySelectAuditTrackHistory
{
	"busiDate":"2025-07-09 01:44:46","code":"000000","message":null,"resultBody":{"ownOpinion":null,"docTitle":null,"lastApprovalEndTime":null,"DRAFTING_UNIT":"政企客户中心","DISTRIBUTE_UNIT":["政企客户中心","广东移动系统集成中山公司"],"CUR_REVIEW_LEVEL_LIST":[{"TOTAL_SEQ":"1","CUR_REVIEW_ID":null,"CUR_REVIEW_NAME":null,"CUR_RIVIEW_ROLE_ID":"APPLYROLE","CUR_RIVIEW_ROLE_NAME":"提交申请","CUR_RIVIEW_OPER_ID":"hehaoming","CUR_RIVIEW_OPER_NAME":"何浩明","CUR_RIVIEW_OPER_CLASS":"政企客户中心","RIVIEW_STATE":1,"END_DATE":"2025-03-25 09:52:35","FLOW_DEFINE_ID":"GD00020241221171717760000","FLOW_NAME":"虚拟开始子流程","CUR_REVIEW_LEVEL":0,"CUR_REVIEW_LEVEL_NAME":"提交申请","COMMENT":"提交申请"},{"TOTAL_SEQ":"2","CUR_REVIEW_ID":null,"CUR_REVIEW_NAME":null,"CUR_RIVIEW_ROLE_ID":"10275","CUR_RIVIEW_ROLE_NAME":"甄选方案评委小组成员","CUR_RIVIEW_OPER_ID":"tangxiaofang","CUR_RIVIEW_OPER_NAME":"唐晓芳","CUR_RIVIEW_OPER_CLASS":"政企客户中心","RIVIEW_STATE":1,"END_DATE":"2025-03-25 09:58:19","FLOW_DEFINE_ID":"GD99920220630103940063018","FLOW_NAME":"制定甄选方案审核-在线填写","CUR_REVIEW_LEVEL":1,"CUR_REVIEW_LEVEL_NAME":"制定甄选方案审核-一级","COMMENT":"通过"},{"TOTAL_SEQ":"3","CUR_REVIEW_ID":null,"CUR_REVIEW_NAME":null,"CUR_RIVIEW_ROLE_ID":"10275","CUR_RIVIEW_ROLE_NAME":"甄选方案评委小组成员","CUR_RIVIEW_OPER_ID":"liuye2","CUR_RIVIEW_OPER_NAME":"刘烨","CUR_RIVIEW_OPER_CLASS":"政企客户中心","RIVIEW_STATE":1,"END_DATE":"2025-03-25 09:56:50","FLOW_DEFINE_ID":"GD99920220630103940063018","FLOW_NAME":"制定甄选方案审核-在线填写","CUR_REVIEW_LEVEL":1,"CUR_REVIEW_LEVEL_NAME":"制定甄选方案审核-一级","COMMENT":"同意"},{"TOTAL_SEQ":"4","CUR_REVIEW_ID":null,"CUR_REVIEW_NAME":null,"CUR_RIVIEW_ROLE_ID":"10275","CUR_RIVIEW_ROLE_NAME":"甄选方案评委小组成员","CUR_RIVIEW_OPER_ID":"xiemingming","CUR_RIVIEW_OPER_NAME":"谢明明","CUR_RIVIEW_OPER_CLASS":"广东移动系统集成中山公司","RIVIEW_STATE":1,"END_DATE":"2025-03-25 11:09:10","FLOW_DEFINE_ID":"GD99920220630103940063018","FLOW_NAME":"制定甄选方案审核-在线填写","CUR_REVIEW_LEVEL":1,"CUR_REVIEW_LEVEL_NAME":"制定甄选方案审核-一级","COMMENT":"同意"},{"TOTAL_SEQ":"5","CUR_REVIEW_ID":null,"CUR_REVIEW_NAME":null,"CUR_RIVIEW_ROLE_ID":"SYS_NOTRELACURDIRECTLEADER","CUR_RIVIEW_ROLE_NAME":"甄选方案评审小组成员对应主任","CUR_RIVIEW_OPER_ID":"lanxiaoming","CUR_RIVIEW_OPER_NAME":"蓝小明","CUR_RIVIEW_OPER_CLASS":"政企客户中心","RIVIEW_STATE":1,"END_DATE":"2025-03-25 11:12:36","FLOW_DEFINE_ID":"GD99920220630103940063018","FLOW_NAME":"制定甄选方案审核-在线填写","CUR_REVIEW_LEVEL":2,"CUR_REVIEW_LEVEL_NAME":"制定甄选方案审核-二级","COMMENT":"同意"},{"TOTAL_SEQ":"6","CUR_REVIEW_ID":null,"CUR_REVIEW_NAME":null,"CUR_RIVIEW_ROLE_ID":"SYS_NOTRELACURDIRECTLEADER","CUR_RIVIEW_ROLE_NAME":"甄选方案评审小组成员对应主任","CUR_RIVIEW_OPER_ID":"huangxiaodong8","CUR_RIVIEW_OPER_NAME":"黄晓东","CUR_RIVIEW_OPER_CLASS":"广东移动系统集成中山公司","RIVIEW_STATE":1,"END_DATE":"2025-03-25 11:12:32","FLOW_DEFINE_ID":"GD99920220630103940063018","FLOW_NAME":"制定甄选方案审核-在线填写","CUR_REVIEW_LEVEL":2,"CUR_REVIEW_LEVEL_NAME":"制定甄选方案审核-二级","COMMENT":"同意"},{"TOTAL_SEQ":"7","CUR_REVIEW_ID":null,"CUR_REVIEW_NAME":null,"CUR_RIVIEW_ROLE_ID":"SYS_NOTRELACURDEPARTMENTMGR","CUR_RIVIEW_ROLE_NAME":"甄选方案评审小组成员对应部门领导","CUR_RIVIEW_OPER_ID":"zhaomingwei","CUR_RIVIEW_OPER_NAME":"赵明伟","CUR_RIVIEW_OPER_CLASS":"广东移动系统集成中山公司","RIVIEW_STATE":1,"END_DATE":"2025-03-25 11:15:09","FLOW_DEFINE_ID":"GD99920220630103940063018","FLOW_NAME":"制定甄选方案审核-在线填写","CUR_REVIEW_LEVEL":3,"CUR_REVIEW_LEVEL_NAME":"制定甄选方案审核-三级","COMMENT":"同意"},{"TOTAL_SEQ":"8","CUR_REVIEW_ID":null,"CUR_REVIEW_NAME":null,"CUR_RIVIEW_ROLE_ID":"SYS_NOTRELACURDEPARTMENTMGR","CUR_RIVIEW_ROLE_NAME":"甄选方案评审小组成员对应部门领导","CUR_RIVIEW_OPER_ID":"mengyongliang","CUR_RIVIEW_OPER_NAME":"孟永亮","CUR_RIVIEW_OPER_CLASS":"政企客户中心","RIVIEW_STATE":1,"END_DATE":"2025-03-25 02:01:42","FLOW_DEFINE_ID":"GD99920220630103940063018","FLOW_NAME":"制定甄选方案审核-在线填写","CUR_REVIEW_LEVEL":3,"CUR_REVIEW_LEVEL_NAME":"制定甄选方案审核-三级","COMMENT":"同意"},{"TOTAL_SEQ":"9","CUR_REVIEW_ID":null,"CUR_REVIEW_NAME":null,"CUR_RIVIEW_ROLE_ID":"10171","CUR_RIVIEW_ROLE_NAME":"交付经理的部门室主任","CUR_RIVIEW_OPER_ID":"huangzhenguo","CUR_RIVIEW_OPER_NAME":"黄振国","CUR_RIVIEW_OPER_CLASS":"广东移动系统集成中山公司","RIVIEW_STATE":1,"END_DATE":"2025-03-25 02:26:38","FLOW_DEFINE_ID":"GD99920220630103940063018","FLOW_NAME":"制定甄选方案审核-在线填写","CUR_REVIEW_LEVEL":4,"CUR_REVIEW_LEVEL_NAME":"制定甄选方案审核-四级","COMMENT":"同意"}],"DRAFTING_STAFF_NAME":"何浩明","DRAFTING_STAFF_TEL":"***********","APPROVAL_END_TIME":"2025年03月25日"}}

	
## 02、特别说明：取数据表 zhenxuan_queryPartnerSelectDetail 的 workOrderMsgId 字段的值，作为 workOrderMsgId 的值 


## 03、参照如下curl脚本，创建数据库表同名的py程序，带cookie访问url获取json数据（注意cookis文件的数据为json格式，提交的cookie为字符串格式，文件里多个同name的项都要带上），轮询将数据入库，入参 workOrderMsgId 也入库：
curl -X POST 'https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectAuditTrackHistory' -H 'Host: dict.gmcc.net:30722' -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' -H 'Accept: application/json, text/plain, */*' -H 'Accept-Encoding: gzip, deflate, br, zstd' -H 'Content-Type: application/json' -H 'sec-ch-ua-platform: "Windows"' -H 'Authorization: Bearer d25c514c-e026-4ddf-b455-9929dfcd3cfb' -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' -H 'Content-Type: application/json;charset=UTF-8' -H 'sec-ch-ua-mobile: ?0' -H 'Origin: https://dict.gmcc.net:30722' -H 'Sec-Fetch-Site: same-origin' -H 'Sec-Fetch-Mode: cors' -H 'Sec-Fetch-Dest: empty' -H 'Referer: https://dict.gmcc.net:30722/ptn/main/selectDemand/detail' -H 'Accept-Language: zh-CN,zh;q=0.9,ee;q=0.8' -H 'Cookie: BSS-SESSION=NjU3M2VhYjYtNWRlYi00NGMzLTg1OWQtZDg2YzNiMjdkZThi; NewoaAppToDones=; NewoaAppToReads=; BSS-SESSION=OTkyMWJiNmMtMWNjMi00OGM0LTllYjAtNmI0M2Q0ODA2NTEy; isLogin=ImlzTG9naW4i; requestId=eb0331f0-5bec-11f0-b2ab-d7d92ba136f7; systemUserCode=InpoZW5nZGV3ZW4i; jsession_id_4_boss=n603BE9BD8C39CA4832672F7B13F2B61E-1' -d '{"workOrderMsgId":"GD76020250325095234501317"}' -k

}



04042==
{

## 01、分析json数据结构，在mysql8数据库里创建数据表zhenxuan_queryLocalAuditTrackHistory3，增加字段selectRevId
{
	"busiDate":"2025-07-09 01:44:46","code":"000000","message":null,"resultBody":[{"auditProcessTrackId":"1942641021614473216","businessId":"1903979207947370496","stepName":"虚拟开始子流程","createTime":"2025-03-25 09:52:34","finishTime":"2025-03-25 09:52:35","status":"通过","auditHandler":"何浩明(hehaoming)","auditRemark":"提交申请"},{"auditProcessTrackId":"1942641021601890304","businessId":"1903979207947370496","stepName":"制定甄选方案审核-在线填写","createTime":"2025-03-25 09:52:35","finishTime":"2025-03-25 09:58:19","status":"通过","auditHandler":"唐晓芳(tangxiaofang)","auditRemark":"通过"},{"auditProcessTrackId":"1942641021606084608","businessId":"1903979207947370496","stepName":"制定甄选方案审核-在线填写","createTime":"2025-03-25 09:52:35","finishTime":"2025-03-25 09:56:50","status":"通过","auditHandler":"刘烨(liuye2)","auditRemark":"同意"},{"auditProcessTrackId":"1942641021610278912","businessId":"1903979207947370496","stepName":"制定甄选方案审核-在线填写","createTime":"2025-03-25 09:52:35","finishTime":"2025-03-25 11:09:10","status":"通过","auditHandler":"谢明明(xiemingming)","auditRemark":"同意"},{"auditProcessTrackId":"1942641021593501696","businessId":"1903979207947370496","stepName":"制定甄选方案审核-在线填写","createTime":"2025-03-25 11:09:10","finishTime":"2025-03-25 11:12:36","status":"通过","auditHandler":"蓝小明(lanxiaoming)","auditRemark":"同意"},{"auditProcessTrackId":"1942641021597696000","businessId":"1903979207947370496","stepName":"制定甄选方案审核-在线填写","createTime":"2025-03-25 11:09:10","finishTime":"2025-03-25 11:12:32","status":"通过","auditHandler":"黄晓东(huangxiaodong8)","auditRemark":"同意"},{"auditProcessTrackId":"1942641021585113088","businessId":"1903979207947370496","stepName":"制定甄选方案审核-在线填写","createTime":"2025-03-25 11:12:36","finishTime":"2025-03-25 11:15:09","status":"通过","auditHandler":"赵明伟(zhaomingwei)","auditRemark":"同意"},{"auditProcessTrackId":"1942641021589307392","businessId":"1903979207947370496","stepName":"制定甄选方案审核-在线填写","createTime":"2025-03-25 11:12:36","finishTime":"2025-03-25 14:01:42","status":"通过","auditHandler":"孟永亮(mengyongliang)","auditRemark":"同意"},{"auditProcessTrackId":"1942641021580918785","businessId":"1903979207947370496","stepName":"制定甄选方案审核-在线填写","createTime":"2025-03-25 14:01:42","finishTime":"2025-03-25 14:26:38","status":"通过","auditHandler":"黄振国(huangzhenguo)","auditRemark":"同意"}]}

	
## 02、特别说明：取数据表 zhenxuan_queryPartnerSelectDetail  的 selectRevId 字段的值，作为入参 businessId 的值，workOrderMsgId 字段的值，作为入参 workOrderMsgId 的值 ；入参"stepName":""


## 03、参照如下curl脚本，创建数据库表同名的py程序，带cookie访问url获取json数据（注意cookis文件的数据为json格式，提交的cookie为字符串格式，文件里多个同name的项都要带上），轮询将数据入库，selectRevId 也入库：
curl -X POST 'https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/queryLocalAuditTrackHistory' -H 'Host: dict.gmcc.net:30722' -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' -H 'Accept: application/json, text/plain, */*' -H 'Accept-Encoding: gzip, deflate, br, zstd' -H 'Content-Type: application/json' -H 'sec-ch-ua-platform: "Windows"' -H 'Authorization: Bearer d25c514c-e026-4ddf-b455-9929dfcd3cfb' -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' -H 'Content-Type: application/json;charset=UTF-8' -H 'sec-ch-ua-mobile: ?0' -H 'Origin: https://dict.gmcc.net:30722' -H 'Sec-Fetch-Site: same-origin' -H 'Sec-Fetch-Mode: cors' -H 'Sec-Fetch-Dest: empty' -H 'Referer: https://dict.gmcc.net:30722/ptn/main/selectDemand/detail' -H 'Accept-Language: zh-CN,zh;q=0.9,ee;q=0.8' -H 'Cookie: BSS-SESSION=NjU3M2VhYjYtNWRlYi00NGMzLTg1OWQtZDg2YzNiMjdkZThi; NewoaAppToDones=; NewoaAppToReads=; BSS-SESSION=OTkyMWJiNmMtMWNjMi00OGM0LTllYjAtNmI0M2Q0ODA2NTEy; isLogin=ImlzTG9naW4i; requestId=eb0331f0-5bec-11f0-b2ab-d7d92ba136f7; systemUserCode=InpoZW5nZGV3ZW4i; jsession_id_4_boss=n603BE9BD8C39CA4832672F7B13F2B61E-1' -d '{"businessId":"1903979207947370496","workOrderMsgId":"GD76020250325095234501317","stepName":""}'

}





00
{

## 01、分析json数据结构，在mysql8数据库里创建数据表zhenxuan_queryNoticeHistoryBySelectId
{"busiDate":"2025-07-09 01:44:51","code":"000000","message":null,"resultBody":[{"selectNoticeId":"19196","noticeName":"已完成线下甄选","selectType":"1001","selectTypeValue":"公开甄选","selectTime":"2025-03-21 00:00:00.0","realPublishTime":"2025-03-25 17:42:38.442","selectIndustry":"HLW","noticeRowNum":"1","noticesVersion":"甄选方案（一）第1次公告","industryValue":"互联网","sendStatus":"1","sendStatusValue":"成功","emailPartnerIds":"","isClarify":false}]}
	
## 02、特别说明：取数据表zhenxuan_querySelectProjectList的 projectNo 字段的值，作为入参 projectCode 的值，selectMsgId 字段的值，作为入参 selectMsgId 的值 


## 03、参照如下curl脚本，创建数据库表同名的py程序，带cookie访问url获取json数据（注意cookis文件的数据为json格式，提交的cookie为字符串格式，文件里多个同name的项都要带上），轮询将数据入库，projectNo、入参projectCode、selectMsgId 也入库：
curl -X GET 'https://dict.gmcc.net:30722/partner/materialManage/pnrSelect/queryNoticeHistoryBySelectId?selectMsgId=1903979207947370496&projectCode=CMGDZSICT20240715013' -H 'Host: dict.gmcc.net:30722' -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' -H 'Accept: application/json, text/plain, */*' -H 'Accept-Encoding: gzip, deflate, br, zstd' -H 'sec-ch-ua-platform: "Windows"' -H 'Authorization: Bearer d25c514c-e026-4ddf-b455-9929dfcd3cfb' -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' -H 'sec-ch-ua-mobile: ?0' -H 'Sec-Fetch-Site: same-origin' -H 'Sec-Fetch-Mode: cors' -H 'Sec-Fetch-Dest: empty' -H 'Referer: https://dict.gmcc.net:30722/ptn/main/selectDemand/detail' -H 'Accept-Language: zh-CN,zh;q=0.9,ee;q=0.8' -H 'Cookie: BSS-SESSION=NjU3M2VhYjYtNWRlYi00NGMzLTg1OWQtZDg2YzNiMjdkZThi; NewoaAppToDones=; NewoaAppToReads=; BSS-SESSION=OTkyMWJiNmMtMWNjMi00OGM0LTllYjAtNmI0M2Q0ODA2NTEy; isLogin=ImlzTG9naW4i; requestId=eb0331f0-5bec-11f0-b2ab-d7d92ba136f7; systemUserCode=InpoZW5nZGV3ZW4i; jsession_id_4_boss=n603BE9BD8C39CA4832672F7B13F2B61E-1'

}







































