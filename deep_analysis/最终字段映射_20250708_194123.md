# 甄选需求管理页面最终字段映射分析

生成时间: 2025-07-08 19:41:23

## 🎯 推断的字段映射

| 中文字段名 | 推断的英文名 | 置信度 | 来源 |
|-----------|-------------|--------|------|
| - | 未发现明确的英文字段映射 | - | - |

## 📜 JavaScript字段分析

未发现JavaScript中的字段定义

## 🔧 Vue组件分析

未发现Vue组件数据

## 🌐 API字段分析

| 类型 | 英文键名 | 值 | URL |
|------|---------|----|----- |
| api_url |  |  | https://dict.gmcc.net:30722/ptn/main/selectDemand |
| api_url |  |  | https://dict.gmcc.net:30722/ptn/main/selectDemand |

## 📋 分析结论

⚠️ **未发现明确映射**: 该页面可能使用了以下技术特征:

- 使用Element UI框架，字段名直接使用中文
- Vue.js组件化开发，字段映射可能在组件内部
- 可能使用了编译后的代码，原始字段名被混淆
- 后端API可能直接接受中文字段名

**建议**:
- 查看网络请求的实际参数名
- 检查Vue DevTools中的组件数据
- 联系开发团队获取API文档
