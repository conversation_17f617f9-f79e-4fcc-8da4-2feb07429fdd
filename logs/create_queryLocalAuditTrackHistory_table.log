2025-07-08 23:59:07,304 - INFO - ============================================================
2025-07-08 23:59:07,306 - INFO - 🚀 本地审核跟踪历史数据表创建程序
2025-07-08 23:59:07,308 - INFO - ============================================================
2025-07-08 23:59:07,308 - INFO - 🚀 开始创建本地审核跟踪历史数据表...
2025-07-08 23:59:07,317 - INFO - ✅ 成功连接到数据库: zhenxuandb
2025-07-08 23:59:07,317 - INFO - ✅ 数据库连接成功
2025-07-08 23:59:07,319 - INFO - 📖 读取SQL文件: D:\0回集成\dict爬虫\spider_dict 甄选\database\create_zhenxuan_queryLocalAuditTrackHistory.sql
2025-07-08 23:59:07,320 - INFO - 📊 解析到 9 个SQL语句
2025-07-08 23:59:07,320 - INFO - ⚡ 执行第 1/9 个SQL语句...
2025-07-08 23:59:07,320 - INFO - ⚡ 执行第 2/9 个SQL语句...
2025-07-08 23:59:07,331 - INFO - 🗑️ 删除旧表（如果存在）
2025-07-08 23:59:07,332 - INFO - ⚡ 执行第 3/9 个SQL语句...
2025-07-08 23:59:07,435 - INFO - 📊 创建新表结构
2025-07-08 23:59:07,436 - INFO - ⚡ 执行第 4/9 个SQL语句...
2025-07-08 23:59:07,444 - INFO - 👁️ 创建视图
2025-07-08 23:59:07,444 - INFO - ⚡ 执行第 5/9 个SQL语句...
2025-07-08 23:59:07,445 - ERROR - ❌ 执行SQL语句失败: (1054, "Unknown column 's.business_id' in 'on clause'")
2025-07-08 23:59:07,445 - ERROR -    SQL: CREATE OR REPLACE VIEW `v_zhenxuan_stage_audit_relation` AS SELECT s.id as stage_id, s.project_msg_i...
2025-07-08 23:59:07,446 - INFO - 🔌 数据库连接已断开
2025-07-08 23:59:07,446 - ERROR - ============================================================
2025-07-08 23:59:07,446 - ERROR - ❌ 程序执行失败！
2025-07-08 23:59:07,446 - ERROR - ⏱️ 总耗时: 0.14 秒
2025-07-08 23:59:07,446 - ERROR - ============================================================
2025-07-09 00:01:02,810 - INFO - ============================================================
2025-07-09 00:01:02,811 - INFO - 🚀 本地审核跟踪历史数据表创建程序
2025-07-09 00:01:02,811 - INFO - ============================================================
2025-07-09 00:01:02,811 - INFO - 🚀 开始创建本地审核跟踪历史数据表...
2025-07-09 00:01:02,815 - INFO - ✅ 成功连接到数据库: zhenxuandb
2025-07-09 00:01:02,816 - INFO - ✅ 数据库连接成功
2025-07-09 00:01:02,816 - INFO - 📖 读取SQL文件: D:\0回集成\dict爬虫\spider_dict 甄选\database\create_zhenxuan_queryLocalAuditTrackHistory.sql
2025-07-09 00:01:02,816 - INFO - 📊 解析到 9 个SQL语句
2025-07-09 00:01:02,816 - INFO - ⚡ 执行第 1/9 个SQL语句...
2025-07-09 00:01:02,817 - INFO - ⚡ 执行第 2/9 个SQL语句...
2025-07-09 00:01:02,857 - INFO - 🗑️ 删除旧表（如果存在）
2025-07-09 00:01:02,857 - INFO - ⚡ 执行第 3/9 个SQL语句...
2025-07-09 00:01:02,954 - INFO - 📊 创建新表结构
2025-07-09 00:01:02,954 - INFO - ⚡ 执行第 4/9 个SQL语句...
2025-07-09 00:01:02,977 - INFO - 👁️ 创建视图
2025-07-09 00:01:02,978 - INFO - ⚡ 执行第 5/9 个SQL语句...
2025-07-09 00:01:02,984 - INFO - 👁️ 创建视图
2025-07-09 00:01:02,984 - INFO - ⚡ 执行第 6/9 个SQL语句...
2025-07-09 00:01:02,985 - INFO - ✅ 验证表创建
2025-07-09 00:01:02,986 - ERROR - ❌ 执行SQL语句失败: 0
2025-07-09 00:01:02,986 - ERROR -    SQL: SELECT TABLE_NAME, TABLE_COMMENT, TABLE_COLLATION FROM information_schema.TABLES WHERE TABLE_SCHEMA ...
2025-07-09 00:01:02,986 - INFO - 🔌 数据库连接已断开
2025-07-09 00:01:02,987 - ERROR - ============================================================
2025-07-09 00:01:02,987 - ERROR - ❌ 程序执行失败！
2025-07-09 00:01:02,987 - ERROR - ⏱️ 总耗时: 0.18 秒
2025-07-09 00:01:02,987 - ERROR - ============================================================
2025-07-09 00:01:23,469 - INFO - ============================================================
2025-07-09 00:01:23,469 - INFO - 🚀 本地审核跟踪历史数据表创建程序
2025-07-09 00:01:23,470 - INFO - ============================================================
2025-07-09 00:01:23,470 - INFO - 🚀 开始创建本地审核跟踪历史数据表...
2025-07-09 00:01:23,480 - INFO - ✅ 成功连接到数据库: zhenxuandb
2025-07-09 00:01:23,480 - INFO - ✅ 数据库连接成功
2025-07-09 00:01:23,480 - INFO - 📖 读取SQL文件: D:\0回集成\dict爬虫\spider_dict 甄选\database\create_zhenxuan_queryLocalAuditTrackHistory.sql
2025-07-09 00:01:23,481 - INFO - 📊 解析到 9 个SQL语句
2025-07-09 00:01:23,481 - INFO - ⚡ 执行第 1/9 个SQL语句...
2025-07-09 00:01:23,481 - INFO - ⚡ 执行第 2/9 个SQL语句...
2025-07-09 00:01:23,535 - INFO - 🗑️ 删除旧表（如果存在）
2025-07-09 00:01:23,535 - INFO - ⚡ 执行第 3/9 个SQL语句...
2025-07-09 00:01:23,645 - INFO - 📊 创建新表结构
2025-07-09 00:01:23,645 - INFO - ⚡ 执行第 4/9 个SQL语句...
2025-07-09 00:01:23,652 - INFO - 👁️ 创建视图
2025-07-09 00:01:23,653 - INFO - ⚡ 执行第 5/9 个SQL语句...
2025-07-09 00:01:23,662 - INFO - 👁️ 创建视图
2025-07-09 00:01:23,662 - INFO - ⚡ 执行第 6/9 个SQL语句...
2025-07-09 00:01:23,665 - INFO - ✅ 验证表创建
2025-07-09 00:01:23,665 - WARNING - ⚠️ 获取查询结果失败: 0
2025-07-09 00:01:23,666 - INFO - ⚡ 执行第 7/9 个SQL语句...
2025-07-09 00:01:23,670 - INFO - 🔍 验证表结构
2025-07-09 00:01:23,670 - INFO - 📋 表结构验证结果:
2025-07-09 00:01:23,670 - INFO -    {'Field': 'id', 'Type': 'bigint', 'Null': 'NO', 'Key': 'PRI', 'Default': None, 'Extra': 'auto_increment'}
2025-07-09 00:01:23,671 - INFO -    {'Field': 'business_id', 'Type': 'varchar(50)', 'Null': 'NO', 'Key': 'MUL', 'Default': None, 'Extra': ''}
2025-07-09 00:01:23,671 - INFO -    {'Field': 'work_order_msg_id', 'Type': 'varchar(100)', 'Null': 'NO', 'Key': 'MUL', 'Default': None, 'Extra': ''}
2025-07-09 00:01:23,671 - INFO -    {'Field': 'step_name_filter', 'Type': 'varchar(100)', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 00:01:23,672 - INFO -    {'Field': 'request_params', 'Type': 'json', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 00:01:23,676 - INFO -    {'Field': 'busi_date', 'Type': 'datetime', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 00:01:23,677 - INFO -    {'Field': 'code', 'Type': 'varchar(20)', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 00:01:23,678 - INFO -    {'Field': 'message', 'Type': 'text', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 00:01:23,678 - INFO -    {'Field': 'audit_process_track_id', 'Type': 'varchar(50)', 'Null': 'YES', 'Key': 'MUL', 'Default': None, 'Extra': ''}
2025-07-09 00:01:23,679 - INFO -    {'Field': 'step_name', 'Type': 'varchar(100)', 'Null': 'YES', 'Key': 'MUL', 'Default': None, 'Extra': ''}
2025-07-09 00:01:23,681 - INFO -    {'Field': 'create_time', 'Type': 'datetime', 'Null': 'YES', 'Key': 'MUL', 'Default': None, 'Extra': ''}
2025-07-09 00:01:23,681 - INFO -    {'Field': 'finish_time', 'Type': 'datetime', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 00:01:23,681 - INFO -    {'Field': 'status', 'Type': 'varchar(20)', 'Null': 'YES', 'Key': 'MUL', 'Default': None, 'Extra': ''}
2025-07-09 00:01:23,682 - INFO -    {'Field': 'audit_handler', 'Type': 'varchar(100)', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 00:01:23,682 - INFO -    {'Field': 'audit_remark', 'Type': 'text', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 00:01:23,682 - INFO -    {'Field': 'raw_data', 'Type': 'json', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 00:01:23,682 - INFO -    {'Field': 'created_at', 'Type': 'timestamp', 'Null': 'YES', 'Key': 'MUL', 'Default': 'CURRENT_TIMESTAMP', 'Extra': 'DEFAULT_GENERATED'}
2025-07-09 00:01:23,683 - INFO -    {'Field': 'updated_at', 'Type': 'timestamp', 'Null': 'YES', 'Key': '', 'Default': 'CURRENT_TIMESTAMP', 'Extra': 'DEFAULT_GENERATED on update CURRENT_TIMESTAMP'}
2025-07-09 00:01:23,683 - INFO - ⚡ 执行第 8/9 个SQL语句...
2025-07-09 00:01:23,686 - INFO - ✅ 验证表创建
2025-07-09 00:01:23,686 - WARNING - ⚠️ 获取查询结果失败: 0
2025-07-09 00:01:23,686 - INFO - ⚡ 执行第 9/9 个SQL语句...
2025-07-09 00:01:23,687 - INFO - ✅ 验证表创建
2025-07-09 00:01:23,688 - WARNING - ⚠️ 获取查询结果失败: 0
2025-07-09 00:01:23,689 - INFO - ✅ 所有SQL语句执行成功
2025-07-09 00:01:23,689 - INFO - 🔍 执行最终验证...
2025-07-09 00:01:23,691 - INFO - ✅ 表验证成功:
2025-07-09 00:01:23,693 - INFO -    📊 表名: TABLE_NAME
2025-07-09 00:01:23,693 - INFO -    📝 注释: TABLE_COMMENT
2025-07-09 00:01:23,694 - INFO -    🔤 排序规则: TABLE_COLLATION
2025-07-09 00:01:23,699 - ERROR - ❌ 创建表失败: 0
2025-07-09 00:01:23,700 - INFO - 🔌 数据库连接已断开
2025-07-09 00:01:23,701 - ERROR - ============================================================
2025-07-09 00:01:23,701 - ERROR - ❌ 程序执行失败！
2025-07-09 00:01:23,701 - ERROR - ⏱️ 总耗时: 0.23 秒
2025-07-09 00:01:23,701 - ERROR - ============================================================
2025-07-09 00:02:02,749 - INFO - ============================================================
2025-07-09 00:02:02,749 - INFO - 🚀 本地审核跟踪历史数据表创建程序
2025-07-09 00:02:02,750 - INFO - ============================================================
2025-07-09 00:02:02,750 - INFO - 🚀 开始创建本地审核跟踪历史数据表...
2025-07-09 00:02:02,754 - INFO - ✅ 成功连接到数据库: zhenxuandb
2025-07-09 00:02:02,754 - INFO - ✅ 数据库连接成功
2025-07-09 00:02:02,755 - INFO - 📖 读取SQL文件: D:\0回集成\dict爬虫\spider_dict 甄选\database\create_zhenxuan_queryLocalAuditTrackHistory.sql
2025-07-09 00:02:02,755 - INFO - 📊 解析到 9 个SQL语句
2025-07-09 00:02:02,755 - INFO - ⚡ 执行第 1/9 个SQL语句...
2025-07-09 00:02:02,755 - INFO - ⚡ 执行第 2/9 个SQL语句...
2025-07-09 00:02:02,802 - INFO - 🗑️ 删除旧表（如果存在）
2025-07-09 00:02:02,802 - INFO - ⚡ 执行第 3/9 个SQL语句...
2025-07-09 00:02:03,098 - INFO - 📊 创建新表结构
2025-07-09 00:02:03,099 - INFO - ⚡ 执行第 4/9 个SQL语句...
2025-07-09 00:02:03,120 - INFO - 👁️ 创建视图
2025-07-09 00:02:03,121 - INFO - ⚡ 执行第 5/9 个SQL语句...
2025-07-09 00:02:03,133 - INFO - 👁️ 创建视图
2025-07-09 00:02:03,134 - INFO - ⚡ 执行第 6/9 个SQL语句...
2025-07-09 00:02:03,137 - INFO - ✅ 验证表创建
2025-07-09 00:02:03,138 - WARNING - ⚠️ 获取查询结果失败: 0
2025-07-09 00:02:03,138 - INFO - ⚡ 执行第 7/9 个SQL语句...
2025-07-09 00:02:03,150 - INFO - 🔍 验证表结构
2025-07-09 00:02:03,151 - INFO - 📋 表结构验证结果:
2025-07-09 00:02:03,151 - INFO -    {'Field': 'id', 'Type': 'bigint', 'Null': 'NO', 'Key': 'PRI', 'Default': None, 'Extra': 'auto_increment'}
2025-07-09 00:02:03,151 - INFO -    {'Field': 'business_id', 'Type': 'varchar(50)', 'Null': 'NO', 'Key': 'MUL', 'Default': None, 'Extra': ''}
2025-07-09 00:02:03,152 - INFO -    {'Field': 'work_order_msg_id', 'Type': 'varchar(100)', 'Null': 'NO', 'Key': 'MUL', 'Default': None, 'Extra': ''}
2025-07-09 00:02:03,154 - INFO -    {'Field': 'step_name_filter', 'Type': 'varchar(100)', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 00:02:03,155 - INFO -    {'Field': 'request_params', 'Type': 'json', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 00:02:03,156 - INFO -    {'Field': 'busi_date', 'Type': 'datetime', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 00:02:03,156 - INFO -    {'Field': 'code', 'Type': 'varchar(20)', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 00:02:03,156 - INFO -    {'Field': 'message', 'Type': 'text', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 00:02:03,157 - INFO -    {'Field': 'audit_process_track_id', 'Type': 'varchar(50)', 'Null': 'YES', 'Key': 'MUL', 'Default': None, 'Extra': ''}
2025-07-09 00:02:03,157 - INFO -    {'Field': 'step_name', 'Type': 'varchar(100)', 'Null': 'YES', 'Key': 'MUL', 'Default': None, 'Extra': ''}
2025-07-09 00:02:03,157 - INFO -    {'Field': 'create_time', 'Type': 'datetime', 'Null': 'YES', 'Key': 'MUL', 'Default': None, 'Extra': ''}
2025-07-09 00:02:03,158 - INFO -    {'Field': 'finish_time', 'Type': 'datetime', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 00:02:03,158 - INFO -    {'Field': 'status', 'Type': 'varchar(20)', 'Null': 'YES', 'Key': 'MUL', 'Default': None, 'Extra': ''}
2025-07-09 00:02:03,158 - INFO -    {'Field': 'audit_handler', 'Type': 'varchar(100)', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 00:02:03,160 - INFO -    {'Field': 'audit_remark', 'Type': 'text', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 00:02:03,161 - INFO -    {'Field': 'raw_data', 'Type': 'json', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 00:02:03,162 - INFO -    {'Field': 'created_at', 'Type': 'timestamp', 'Null': 'YES', 'Key': 'MUL', 'Default': 'CURRENT_TIMESTAMP', 'Extra': 'DEFAULT_GENERATED'}
2025-07-09 00:02:03,162 - INFO -    {'Field': 'updated_at', 'Type': 'timestamp', 'Null': 'YES', 'Key': '', 'Default': 'CURRENT_TIMESTAMP', 'Extra': 'DEFAULT_GENERATED on update CURRENT_TIMESTAMP'}
2025-07-09 00:02:03,163 - INFO - ⚡ 执行第 8/9 个SQL语句...
2025-07-09 00:02:03,167 - INFO - ✅ 验证表创建
2025-07-09 00:02:03,168 - WARNING - ⚠️ 获取查询结果失败: 0
2025-07-09 00:02:03,168 - INFO - ⚡ 执行第 9/9 个SQL语句...
2025-07-09 00:02:03,170 - INFO - ✅ 验证表创建
2025-07-09 00:02:03,170 - WARNING - ⚠️ 获取查询结果失败: 0
2025-07-09 00:02:03,172 - INFO - ✅ 所有SQL语句执行成功
2025-07-09 00:02:03,173 - INFO - 🔍 执行最终验证...
2025-07-09 00:02:03,178 - INFO - ✅ 表验证成功: zhenxuan_querylocalaudittrackhistory
2025-07-09 00:02:03,179 - INFO -    📋 字段数量: 18
2025-07-09 00:02:03,180 - INFO -    🔍 索引数量: 15
2025-07-09 00:02:03,182 - INFO -    👁️ 相关视图数量: 3
2025-07-09 00:02:03,182 - INFO - 🎉 本地审核跟踪历史数据表创建完成！
2025-07-09 00:02:03,183 - INFO - 🔌 数据库连接已断开
2025-07-09 00:02:03,184 - INFO - ============================================================
2025-07-09 00:02:03,184 - INFO - 🎉 程序执行成功！
2025-07-09 00:02:03,184 - INFO - ⏱️ 总耗时: 0.43 秒
2025-07-09 00:02:03,184 - INFO - ============================================================
2025-07-09 02:55:53,802 - INFO - ============================================================
2025-07-09 02:55:53,802 - INFO - 🚀 本地审核跟踪历史数据表创建程序
2025-07-09 02:55:53,802 - INFO - ============================================================
2025-07-09 02:55:53,803 - INFO - 🚀 开始创建本地审核跟踪历史数据表...
2025-07-09 02:55:53,812 - INFO - ✅ 成功连接到数据库: zhenxuandb
2025-07-09 02:55:53,812 - INFO - ✅ 数据库连接成功
2025-07-09 02:55:53,813 - INFO - 📖 读取SQL文件: D:\0回集成\dict爬虫\spider_dict 甄选\database\create_zhenxuan_queryLocalAuditTrackHistory.sql
2025-07-09 02:55:53,813 - INFO - 📊 解析到 9 个SQL语句
2025-07-09 02:55:53,813 - INFO - ⚡ 执行第 1/9 个SQL语句...
2025-07-09 02:55:53,813 - INFO - ⚡ 执行第 2/9 个SQL语句...
2025-07-09 02:55:53,852 - INFO - 🗑️ 删除旧表（如果存在）
2025-07-09 02:55:53,852 - INFO - ⚡ 执行第 3/9 个SQL语句...
2025-07-09 02:55:53,958 - INFO - 📊 创建新表结构
2025-07-09 02:55:53,958 - INFO - ⚡ 执行第 4/9 个SQL语句...
2025-07-09 02:55:53,968 - INFO - 👁️ 创建视图
2025-07-09 02:55:53,968 - INFO - ⚡ 执行第 5/9 个SQL语句...
2025-07-09 02:55:53,979 - INFO - 👁️ 创建视图
2025-07-09 02:55:53,979 - INFO - ⚡ 执行第 6/9 个SQL语句...
2025-07-09 02:55:53,981 - INFO - ✅ 验证表创建
2025-07-09 02:55:53,982 - WARNING - ⚠️ 获取查询结果失败: 0
2025-07-09 02:55:53,982 - INFO - ⚡ 执行第 7/9 个SQL语句...
2025-07-09 02:55:53,985 - INFO - 🔍 验证表结构
2025-07-09 02:55:53,985 - INFO - 📋 表结构验证结果:
2025-07-09 02:55:53,985 - INFO -    {'Field': 'id', 'Type': 'bigint', 'Null': 'NO', 'Key': 'PRI', 'Default': None, 'Extra': 'auto_increment'}
2025-07-09 02:55:53,985 - INFO -    {'Field': 'project_msg_id', 'Type': 'varchar(50)', 'Null': 'NO', 'Key': 'MUL', 'Default': None, 'Extra': ''}
2025-07-09 02:55:53,986 - INFO -    {'Field': 'business_id', 'Type': 'varchar(50)', 'Null': 'NO', 'Key': 'MUL', 'Default': None, 'Extra': ''}
2025-07-09 02:55:53,986 - INFO -    {'Field': 'work_order_msg_id', 'Type': 'varchar(100)', 'Null': 'YES', 'Key': 'MUL', 'Default': None, 'Extra': ''}
2025-07-09 02:55:53,986 - INFO -    {'Field': 'step_name_filter', 'Type': 'varchar(100)', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 02:55:53,986 - INFO -    {'Field': 'request_params', 'Type': 'json', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 02:55:53,986 - INFO -    {'Field': 'busi_date', 'Type': 'datetime', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 02:55:53,986 - INFO -    {'Field': 'code', 'Type': 'varchar(20)', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 02:55:53,987 - INFO -    {'Field': 'message', 'Type': 'text', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 02:55:53,987 - INFO -    {'Field': 'audit_process_track_id', 'Type': 'varchar(50)', 'Null': 'YES', 'Key': 'MUL', 'Default': None, 'Extra': ''}
2025-07-09 02:55:53,987 - INFO -    {'Field': 'step_name', 'Type': 'varchar(100)', 'Null': 'YES', 'Key': 'MUL', 'Default': None, 'Extra': ''}
2025-07-09 02:55:53,987 - INFO -    {'Field': 'create_time', 'Type': 'datetime', 'Null': 'YES', 'Key': 'MUL', 'Default': None, 'Extra': ''}
2025-07-09 02:55:53,987 - INFO -    {'Field': 'finish_time', 'Type': 'datetime', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 02:55:53,987 - INFO -    {'Field': 'status', 'Type': 'varchar(50)', 'Null': 'YES', 'Key': 'MUL', 'Default': None, 'Extra': ''}
2025-07-09 02:55:53,988 - INFO -    {'Field': 'audit_handler', 'Type': 'varchar(100)', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 02:55:53,988 - INFO -    {'Field': 'audit_remark', 'Type': 'text', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 02:55:53,988 - INFO -    {'Field': 'raw_data', 'Type': 'json', 'Null': 'YES', 'Key': '', 'Default': None, 'Extra': ''}
2025-07-09 02:55:53,988 - INFO -    {'Field': 'created_at', 'Type': 'timestamp', 'Null': 'YES', 'Key': 'MUL', 'Default': 'CURRENT_TIMESTAMP', 'Extra': 'DEFAULT_GENERATED'}
2025-07-09 02:55:53,988 - INFO -    {'Field': 'updated_at', 'Type': 'timestamp', 'Null': 'YES', 'Key': '', 'Default': 'CURRENT_TIMESTAMP', 'Extra': 'DEFAULT_GENERATED on update CURRENT_TIMESTAMP'}
2025-07-09 02:55:53,989 - INFO - ⚡ 执行第 8/9 个SQL语句...
2025-07-09 02:55:53,990 - INFO - ✅ 验证表创建
2025-07-09 02:55:53,991 - WARNING - ⚠️ 获取查询结果失败: 0
2025-07-09 02:55:53,991 - INFO - ⚡ 执行第 9/9 个SQL语句...
2025-07-09 02:55:53,992 - INFO - ✅ 验证表创建
2025-07-09 02:55:53,992 - WARNING - ⚠️ 获取查询结果失败: 0
2025-07-09 02:55:53,993 - INFO - ✅ 所有SQL语句执行成功
2025-07-09 02:55:53,993 - INFO - 🔍 执行最终验证...
2025-07-09 02:55:53,994 - INFO - ✅ 表验证成功: zhenxuan_querylocalaudittrackhistory
2025-07-09 02:55:53,995 - INFO -    📋 字段数量: 19
2025-07-09 02:55:53,997 - INFO -    🔍 索引数量: 16
2025-07-09 02:55:53,998 - INFO -    👁️ 相关视图数量: 4
2025-07-09 02:55:53,998 - INFO - 🎉 本地审核跟踪历史数据表创建完成！
2025-07-09 02:55:53,999 - INFO - 🔌 数据库连接已断开
2025-07-09 02:55:54,000 - INFO - ============================================================
2025-07-09 02:55:54,002 - INFO - 🎉 程序执行成功！
2025-07-09 02:55:54,006 - INFO - ⏱️ 总耗时: 0.20 秒
2025-07-09 02:55:54,006 - INFO - ============================================================
