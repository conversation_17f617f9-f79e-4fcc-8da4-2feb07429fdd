"""
改进的数据抓取器
正确识别表头和数据，生成标准化的CSV文件
"""

import asyncio
import json
import os
import csv
import pandas as pd
from datetime import datetime
from playwright.async_api import async_playwright


class ImprovedDataScraper:
    def __init__(self):
        self.target_url = "https://dict.gmcc.net:30722/ptn/main/selectDemand"
        self.cookie_dir = "cookies"
        self.output_dir = "structured_data"
        self.cookies = None
        
        # 创建输出目录
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
        
    def load_cookies(self):
        """加载cookies"""
        try:
            latest_cookie_file = os.path.join(self.cookie_dir, "cookies_dict_zhenxuan.json")
            
            if os.path.exists(latest_cookie_file):
                with open(latest_cookie_file, 'r', encoding='utf-8') as f:
                    self.cookies = json.load(f)
                print(f"✅ 成功加载 {len(self.cookies)} 个cookie")
                return True
            else:
                print("❌ 未找到cookies文件")
                return False
                
        except Exception as e:
            print(f"❌ 加载cookies失败: {e}")
            return False
    
    async def scrape_structured_data(self):
        """抓取结构化数据"""
        print("🕷️ 开始抓取结构化数据...")
        
        if not self.load_cookies():
            return False
            
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False, slow_mo=300)
            context = await browser.new_context(ignore_https_errors=True)
            
            try:
                await context.add_cookies(self.cookies)
                page = await context.new_page()
                
                print(f"🌐 访问页面: {self.target_url}")
                await page.goto(self.target_url, wait_until='networkidle')
                await asyncio.sleep(3)
                
                # 抓取主数据表
                main_data = await self.extract_main_table_data(page)
                
                if main_data:
                    # 保存为CSV
                    await self.save_to_csv(main_data)
                    
                    # 保存为Excel
                    await self.save_to_excel(main_data)
                    
                    # 生成数据报告
                    await self.generate_data_report(main_data)
                    
                    print("✅ 数据抓取和保存完成")
                else:
                    print("❌ 未能抓取到有效数据")
                
                await asyncio.sleep(3)
                return True
                
            except Exception as e:
                print(f"❌ 抓取过程中出错: {e}")
                return False
                
            finally:
                await browser.close()
    
    async def extract_main_table_data(self, page):
        """提取主数据表的数据"""
        print("📊 提取主数据表...")
        
        try:
            # 等待表格加载
            await page.wait_for_selector('table', timeout=10000)
            
            # 查找包含实际数据的表格
            tables = await page.query_selector_all('table')
            
            # 定义标准表头
            standard_headers = [
                "序号", "项目名称", "项目编码", "需求名称", "甄选方案数量", 
                "需求编码", "甄选类别", "归属地市", "创建时间", "甄选需求状态"
            ]
            
            main_data = []
            
            # 遍历表格寻找主数据表
            for table_index, table in enumerate(tables):
                print(f"  检查表格 {table_index + 1}...")
                
                # 获取所有行
                rows = await table.query_selector_all('tr')
                if len(rows) < 2:  # 至少要有表头和一行数据
                    continue
                
                # 检查是否包含项目数据
                has_project_data = False
                for row in rows:
                    cells = await row.query_selector_all('td, th')
                    for cell in cells:
                        text = await cell.inner_text()
                        if "CMGD" in text or "项目甄选" in text:
                            has_project_data = True
                            break
                    if has_project_data:
                        break
                
                if not has_project_data:
                    continue
                
                print(f"  ✅ 找到主数据表 (表格 {table_index + 1})")
                
                # 提取数据行
                for row_index, row in enumerate(rows):
                    cells = await row.query_selector_all('td, th')
                    
                    if len(cells) < 5:  # 数据行应该有足够的列
                        continue
                    
                    row_data = []
                    for cell in cells:
                        text = await cell.inner_text()
                        row_data.append(text.strip())
                    
                    # 检查是否为有效的数据行
                    if self.is_valid_data_row(row_data):
                        # 标准化数据行
                        standardized_row = self.standardize_data_row(row_data, standard_headers)
                        if standardized_row:
                            main_data.append(standardized_row)
                
                if main_data:
                    break  # 找到数据后退出循环
            
            if main_data:
                print(f"✅ 成功提取 {len(main_data)} 条记录")
                return {
                    "headers": standard_headers,
                    "data": main_data
                }
            else:
                print("❌ 未找到有效的数据记录")
                return None
                
        except Exception as e:
            print(f"❌ 提取数据时出错: {e}")
            return None
    
    def is_valid_data_row(self, row_data):
        """判断是否为有效的数据行"""
        if not row_data or len(row_data) < 5:
            return False
        
        # 检查是否包含项目编码模式
        for cell in row_data:
            if cell and "CMGD" in cell and "ICT" in cell:
                return True
        
        # 检查是否包含日期模式
        for cell in row_data:
            if cell and ("2025-" in cell or "2024-" in cell):
                return True
        
        return False
    
    def standardize_data_row(self, row_data, headers):
        """标准化数据行"""
        try:
            # 创建标准化的数据字典
            standardized = {}
            
            # 根据数据特征映射到标准字段
            for i, cell in enumerate(row_data):
                if not cell:
                    continue
                
                # 序号 - 通常是第一列的数字
                if i == 0 and cell.isdigit():
                    standardized["序号"] = int(cell)
                
                # 项目编码 - 包含CMGD和ICT的字符串
                elif "CMGD" in cell and "ICT" in cell:
                    standardized["项目编码"] = cell
                
                # 创建时间 - 日期时间格式
                elif "2025-" in cell or "2024-" in cell:
                    standardized["创建时间"] = cell
                
                # 甄选类别 - 包含"甄选"的字段
                elif "甄选" in cell and len(cell) < 10:
                    standardized["甄选类别"] = cell
                
                # 甄选需求状态 - 包含"审核"的字段
                elif "审核" in cell:
                    standardized["甄选需求状态"] = cell
                
                # 归属地市 - 短字符串，通常是地名
                elif len(cell) <= 4 and cell in ["中山", "广州", "深圳", "珠海", "东莞", "佛山"]:
                    standardized["归属地市"] = cell
                
                # 需求编码 - 长数字字符串
                elif cell.isdigit() and len(cell) > 15:
                    standardized["需求编码"] = cell
                
                # 甄选方案数量 - 单个数字
                elif cell.isdigit() and len(cell) == 1:
                    standardized["甄选方案数量"] = int(cell)
            
            # 处理项目名称和需求名称（通常是较长的文本）
            long_texts = [cell for cell in row_data if cell and len(cell) > 10 and 
                         "CMGD" not in cell and "2025-" not in cell and "审核" not in cell]
            
            if len(long_texts) >= 2:
                standardized["项目名称"] = long_texts[0]
                standardized["需求名称"] = long_texts[1]
            elif len(long_texts) == 1:
                standardized["项目名称"] = long_texts[0]
            
            # 确保所有必要字段都有值
            for header in headers:
                if header not in standardized:
                    standardized[header] = ""
            
            # 只有包含关键字段才返回
            if standardized.get("项目编码") and standardized.get("项目名称"):
                return standardized
            else:
                return None
                
        except Exception as e:
            print(f"⚠️ 标准化数据行时出错: {e}")
            return None
    
    async def save_to_csv(self, data):
        """保存为CSV文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_file = f"{self.output_dir}/甄选需求数据_{timestamp}.csv"
        
        with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.DictWriter(f, fieldnames=data["headers"])
            writer.writeheader()
            writer.writerows(data["data"])
        
        print(f"💾 CSV文件已保存: {csv_file}")
    
    async def save_to_excel(self, data):
        """保存为Excel文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        excel_file = f"{self.output_dir}/甄选需求数据_{timestamp}.xlsx"
        
        df = pd.DataFrame(data["data"])
        df.to_excel(excel_file, index=False, sheet_name="甄选需求数据")
        
        print(f"📊 Excel文件已保存: {excel_file}")
    
    async def generate_data_report(self, data):
        """生成数据报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"{self.output_dir}/数据报告_{timestamp}.md"
        
        df = pd.DataFrame(data["data"])
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 甄选需求数据报告\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## 数据概览\n\n")
            f.write(f"- 总记录数: {len(data['data'])}\n")
            f.write(f"- 字段数: {len(data['headers'])}\n\n")
            
            f.write("## 字段统计\n\n")
            
            # 甄选类别统计
            if "甄选类别" in df.columns:
                category_counts = df["甄选类别"].value_counts()
                f.write("### 甄选类别分布\n")
                for category, count in category_counts.items():
                    f.write(f"- {category}: {count}条\n")
                f.write("\n")
            
            # 状态统计
            if "甄选需求状态" in df.columns:
                status_counts = df["甄选需求状态"].value_counts()
                f.write("### 甄选需求状态分布\n")
                for status, count in status_counts.items():
                    f.write(f"- {status}: {count}条\n")
                f.write("\n")
            
            # 地市统计
            if "归属地市" in df.columns:
                city_counts = df["归属地市"].value_counts()
                f.write("### 归属地市分布\n")
                for city, count in city_counts.items():
                    f.write(f"- {city}: {count}条\n")
                f.write("\n")
            
            f.write("## 样本数据\n\n")
            f.write("前5条记录:\n\n")
            for i, row in enumerate(data["data"][:5]):
                f.write(f"### 记录 {i+1}\n")
                for header in data["headers"]:
                    value = row.get(header, "")
                    f.write(f"- **{header}**: {value}\n")
                f.write("\n")
        
        print(f"📋 数据报告已生成: {report_file}")


async def main():
    """主函数"""
    scraper = ImprovedDataScraper()
    success = await scraper.scrape_structured_data()
    
    if success:
        print("🎉 结构化数据抓取完成！")
    else:
        print("❌ 数据抓取失败")


if __name__ == "__main__":
    print("🚀 改进的数据抓取器")
    print("📊 将抓取结构化数据并生成标准化文件")
    print("=" * 60)
    asyncio.run(main())
