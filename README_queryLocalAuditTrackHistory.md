# 本地审核跟踪历史数据获取系统 - 项目总结

## 🎯 项目概述

基于您提供的JSON数据结构和curl脚本，成功创建了完整的本地审核跟踪历史数据获取和入库系统。系统实现了从API获取数据、数据转换、数据库存储和查询等完整功能。

## ✅ 已完成功能

### 1. 数据库设计 ✅

- **表名**：`zhenxuan_queryLocalAuditTrackHistory`
- **字段数量**：19个字段，完整映射JSON数据结构（包含新增的`project_msg_id`字段）
- **排序规则**：`utf8mb4_general_ci`（符合要求）
- **索引设计**：10个索引，优化查询性能
- **唯一约束**：基于`project_msg_id`、`business_id`和`audit_process_track_id`防止重复数据

#### 🔗 表间关系说明

```sql
-- 与 zhenxuan_querySelectProjectList 表的关联关系
-- project_msg_id 字段 ← zhenxuan_querySelectProjectList.project_msg_id
-- business_id 字段使用 project_msg_id 的值
-- work_order_msg_id 字段固定为 null
-- step_name_filter 字段固定为空字符串

-- 关联查询视图已创建：v_zhenxuan_project_audit_relation
SELECT
    p.project_msg_id,           -- 项目消息ID
    p.project_name,             -- 项目名称
    p.select_name,              -- 甄选名称
    a.step_name,                -- 审核步骤
    a.audit_status,             -- 审核状态
    a.audit_handler,            -- 审核处理人
    a.audit_create_time,        -- 审核创建时间
    a.audit_finish_time         -- 审核完成时间
FROM zhenxuan_querySelectProjectList p
LEFT JOIN zhenxuan_queryLocalAuditTrackHistory a
    ON p.project_msg_id = a.project_msg_id
```

### 2. 核心功能实现 ✅

#### 📊 数据表创建
- **文件**：`database/create_zhenxuan_queryLocalAuditTrackHistory.sql`
- **脚本**：`scripts/create_queryLocalAuditTrackHistory_table.py`
- **状态**：✅ 已创建并验证

#### 🔄 数据获取程序
- **文件**：`scripts/fetch_queryLocalAuditTrackHistory.py`
- **功能**：
  - ✅ 支持Cookie认证（JSON格式转字符串格式）
  - ✅ 支持`projectMsgId`参数（从zhenxuan_querySelectProjectList表获取）
  - ✅ 自动设置请求参数：`businessId`使用`projectMsgId`值，`workOrderMsgId`为null，`stepName`为空字符串
  - ✅ 自动数据转换和时间格式处理
  - ✅ 数据入库处理（包含`project_msg_id`字段）
  - ✅ 重复数据处理和更新机制

## 📋 数据表结构

### 主要字段说明

| 字段名 | 类型 | 说明 | 来源 |
|--------|------|------|------|
| `project_msg_id` | VARCHAR(50) | 项目消息ID | 来源于zhenxuan_querySelectProjectList.project_msg_id |
| `business_id` | VARCHAR(50) | 业务ID | 使用project_msg_id的值 |
| `work_order_msg_id` | VARCHAR(100) | 工单消息ID | 固定为null |
| `step_name_filter` | VARCHAR(100) | 步骤名称过滤 | 固定为空字符串 |
| `audit_process_track_id` | VARCHAR(50) | 审核流程跟踪ID | API返回 |
| `step_name` | VARCHAR(100) | 步骤名称 | API返回 |
| `create_time` | DATETIME | 创建时间 | API返回 |
| `finish_time` | DATETIME | 完成时间 | API返回 |
| `status` | VARCHAR(50) | 状态 | API返回（支持"已处理-通过"等长状态） |
| `audit_handler` | VARCHAR(100) | 审核处理人 | API返回 |
| `audit_remark` | TEXT | 审核备注 | API返回 |

### 索引设计

```sql
-- 单字段索引
INDEX idx_project_msg_id (project_msg_id)
INDEX idx_business_id (business_id)
INDEX idx_work_order_msg_id (work_order_msg_id)
INDEX idx_audit_process_track_id (audit_process_track_id)
INDEX idx_step_name (step_name)
INDEX idx_create_time (create_time)
INDEX idx_status (status)
INDEX idx_created_at (created_at)

-- 复合索引
INDEX idx_project_business (project_msg_id, business_id)
INDEX idx_business_step (business_id, step_name)

-- 唯一约束
UNIQUE KEY uk_audit_track_unique (project_msg_id, business_id, audit_process_track_id)
```

## 🚀 使用方法

### 1. 创建数据表

```bash
# 执行表创建脚本
python scripts/create_queryLocalAuditTrackHistory_table.py
```

### 2. 数据获取

#### 批量获取（限制数量）
```bash
# 获取前10条数据（默认）
python scripts/fetch_queryLocalAuditTrackHistory.py

# 自定义请求间隔
python scripts/fetch_queryLocalAuditTrackHistory.py --delay 2.0
```

#### 批量获取（全部数据）
```bash
# 获取全部数据
python scripts/fetch_queryLocalAuditTrackHistory.py --all
```

#### 单个项目获取
```bash
# 指定项目消息ID
python scripts/fetch_queryLocalAuditTrackHistory.py \
    --project-msg-id "1812755334174785536"
```

#### 自定义Cookie文件
```bash
python scripts/fetch_queryLocalAuditTrackHistory.py \
    --cookie-file "/path/to/your/cookies.json"
```

## 📊 数据查询示例

### 基础查询
```sql
-- 查看所有审核跟踪记录
SELECT * FROM zhenxuan_queryLocalAuditTrackHistory 
ORDER BY create_time DESC;

-- 查看特定业务的审核历史
SELECT step_name, status, audit_handler, create_time, finish_time, audit_remark
FROM zhenxuan_queryLocalAuditTrackHistory 
WHERE business_id = '1877647623078199296'
ORDER BY create_time;
```

### 关联查询
```sql
-- 使用关联视图查询项目和审核历史
SELECT * FROM v_zhenxuan_project_audit_relation
WHERE project_msg_id = 'your_project_msg_id'
ORDER BY audit_create_time;

-- 手动关联查询
SELECT
    p.project_msg_id,
    p.project_name,
    p.select_name,
    a.step_name,
    a.status,
    a.audit_handler,
    a.create_time,
    a.audit_remark
FROM zhenxuan_querySelectProjectList p
LEFT JOIN zhenxuan_queryLocalAuditTrackHistory a
    ON p.project_msg_id = a.project_msg_id
WHERE p.project_msg_id = 'your_project_msg_id'
ORDER BY a.create_time;
```

### 统计查询
```sql
-- 审核状态统计
SELECT status, COUNT(*) as count
FROM zhenxuan_queryLocalAuditTrackHistory 
GROUP BY status;

-- 审核处理人统计
SELECT audit_handler, COUNT(*) as count
FROM zhenxuan_queryLocalAuditTrackHistory 
GROUP BY audit_handler
ORDER BY count DESC;

-- 步骤名称统计
SELECT step_name, COUNT(*) as count
FROM zhenxuan_queryLocalAuditTrackHistory 
GROUP BY step_name
ORDER BY count DESC;
```

## 🔧 技术特性

### Cookie处理
- ✅ 支持JSON格式Cookie文件
- ✅ 自动转换为HTTP Cookie字符串格式
- ✅ 保留所有同名但不同路径的Cookie
- ✅ 默认使用`cookies/cookies_dict_zhenxuan.json`

### 数据处理
- ✅ 自动时间格式转换
- ✅ JSON数据序列化存储
- ✅ 重复数据检测和更新
- ✅ 错误处理和日志记录

### 性能优化
- ✅ 批量插入/更新操作
- ✅ 请求间隔控制
- ✅ 数据库连接池管理
- ✅ 索引优化查询性能

## 📝 日志文件

- **表创建日志**：`logs/create_queryLocalAuditTrackHistory_table.log`
- **数据获取日志**：`logs/fetch_queryLocalAuditTrackHistory.log`

## ⚠️ 注意事项

1. **数据依赖**：需要先有`zhenxuan_querySelectProjectList`表的数据
2. **Cookie有效性**：确保Cookie文件是最新的，避免认证失败
3. **请求频率**：建议设置适当的请求间隔，避免对服务器造成压力
4. **数据完整性**：程序会自动处理重复数据，使用唯一约束防止重复插入
5. **请求参数**：程序自动设置`businessId`为`projectMsgId`值，`workOrderMsgId`为null，`stepName`为空字符串

## 🎯 实际测试结果

### 测试数据获取
```bash
# 单个项目测试示例
python scripts/fetch_queryLocalAuditTrackHistory.py \
    --project-msg-id "1812755334174785536"

# 批量获取测试示例
python scripts/fetch_queryLocalAuditTrackHistory.py

# 结果：成功获取并插入3条审核跟踪记录
```

### 数据验证结果
```
📊 总记录数: 3

📋 审核跟踪记录示例:
1. 项目消息ID: 1812755334174785536
   业务ID: 1812755334174785536
   工单消息ID: None
   步骤名称: 发起甄选需求
   状态: 已处理
   审核处理人: hehaoming
   创建时间: 2024-07-15 15:45:25
   完成时间: 2024-07-15 15:45:25
   审核备注: None

2. 项目消息ID: 1812755334174785536
   业务ID: 1812755334174785536
   工单消息ID: None
   步骤名称: 甄选需求发起审批-一级
   状态: 已处理-通过
   审核处理人: huangbo7
   创建时间: 2024-07-15 15:45:25
   完成时间: 2024-07-15 17:11:39
   审核备注: 同意

🔗 关联查询测试:
   项目ID: 11402, 项目消息ID: 1812755334174785536
   项目名称: 中山市小榄分局车载警务法眼系统项目, 审核步骤: 发起甄选需求
   审核状态: 已处理, 处理人: hehaoming
```

## 🎉 项目总结

本系统成功实现了：
- ✅ 完整的数据表设计和创建
- ✅ 灵活的数据获取程序
- ✅ 完善的错误处理机制
- ✅ 优化的数据库性能
- ✅ 清晰的表间关系设计
- ✅ 便捷的查询视图

系统已准备就绪，可以开始获取和分析本地审核跟踪历史数据！
