"""
认证信息辅助工具
用于读取和处理保存的cookies和headers信息
"""

import json
import os
from datetime import datetime


class AuthHelper:
    def __init__(self, cookie_file="cookies/cookies_dict_zhenxuan.json"):
        self.cookie_file = cookie_file
        self.auth_data = None
        
    def load_auth_data(self):
        """加载认证数据"""
        try:
            if not os.path.exists(self.cookie_file):
                print(f"❌ 认证文件不存在: {self.cookie_file}")
                return False
                
            with open(self.cookie_file, 'r', encoding='utf-8') as f:
                self.auth_data = json.load(f)
                
            print(f"✅ 成功加载认证数据")
            print(f"📅 保存时间: {self.auth_data.get('timestamp', 'Unknown')}")
            print(f"🌐 URL: {self.auth_data.get('url', 'Unknown')}")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载认证数据失败: {e}")
            return False
    
    def get_cookies_dict(self):
        """获取cookies字典格式"""
        if not self.auth_data:
            return {}
            
        cookies_dict = {}
        for cookie in self.auth_data.get('cookies', []):
            cookies_dict[cookie['name']] = cookie['value']
            
        return cookies_dict
    
    def get_cookies_string(self):
        """获取cookies字符串格式（用于HTTP请求头）"""
        if not self.auth_data:
            return ""
            
        cookie_pairs = []
        for cookie in self.auth_data.get('cookies', []):
            cookie_pairs.append(f"{cookie['name']}={cookie['value']}")
            
        return "; ".join(cookie_pairs)
    
    def get_headers(self):
        """获取headers字典"""
        if not self.auth_data:
            return {}
            
        headers = self.auth_data.get('headers', {}).copy()
        
        # 添加cookies到headers中
        cookie_string = self.get_cookies_string()
        if cookie_string:
            headers['Cookie'] = cookie_string
            
        return headers
    
    def get_requests_session_config(self):
        """获取requests session配置"""
        return {
            'cookies': self.get_cookies_dict(),
            'headers': self.get_headers()
        }
    
    def print_auth_info(self):
        """打印认证信息摘要"""
        if not self.auth_data:
            print("❌ 没有加载认证数据")
            return
            
        print("\n" + "="*50)
        print("🔐 认证信息摘要")
        print("="*50)
        
        # 基本信息
        print(f"📅 保存时间: {self.auth_data.get('timestamp', 'Unknown')}")
        print(f"🌐 URL: {self.auth_data.get('url', 'Unknown')}")
        print(f"🖥️ User Agent: {self.auth_data.get('user_agent', 'Unknown')[:80]}...")
        
        # Cookies信息
        cookies = self.auth_data.get('cookies', [])
        print(f"\n🍪 Cookies ({len(cookies)} 个):")
        for cookie in cookies[:5]:  # 只显示前5个
            print(f"  - {cookie['name']}: {cookie['value'][:20]}...")
        if len(cookies) > 5:
            print(f"  ... 还有 {len(cookies) - 5} 个cookies")
        
        # Headers信息
        headers = self.auth_data.get('headers', {})
        print(f"\n📋 Headers ({len(headers)} 个):")
        for key, value in headers.items():
            if key == 'Authorization':
                print(f"  - {key}: {str(value)[:30]}...")
            else:
                print(f"  - {key}: {str(value)[:50]}...")
        
        print("="*50)


def main():
    """测试函数"""
    helper = AuthHelper()
    
    if helper.load_auth_data():
        helper.print_auth_info()
        
        print("\n🔧 使用示例:")
        print("# 获取cookies字符串")
        print(f"cookies_string = '{helper.get_cookies_string()[:100]}...'")
        
        print("\n# 获取headers字典")
        headers = helper.get_headers()
        print("headers = {")
        for key, value in list(headers.items())[:3]:
            print(f"    '{key}': '{str(value)[:50]}...',")
        print("    ...")
        print("}")
        
    else:
        print("❌ 无法加载认证数据，请先运行登录脚本")


if __name__ == "__main__":
    main()
