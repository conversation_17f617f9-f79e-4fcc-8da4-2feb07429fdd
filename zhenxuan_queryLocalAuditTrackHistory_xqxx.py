"""
本地审核跟踪历史数据获取程序（需求信息版本）
根据 queryLocalAuditTrackHistory 接口获取审核跟踪历史数据并入库
特别说明：使用 projectMsgId 作为 businessId 入参，workOrderMsgId 为 null
"""

import os
import sys
import json
import requests
import logging
import time
import argparse
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from database.db_config import ZHENXUAN_DB_CONFIG, DatabaseManager

# 确保logs目录存在
logs_dir = project_root / "logs"
logs_dir.mkdir(exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(logs_dir / 'zhenxuan_queryLocalAuditTrackHistory_xqxx.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class QueryLocalAuditTrackHistoryXqxxFetcher:
    """本地审核跟踪历史数据获取器（需求信息版本）"""
    
    def __init__(self, cookie_file_path: Optional[str] = None):
        """
        初始化数据获取器
        
        Args:
            cookie_file_path: Cookie文件路径，默认使用cookies/cookies_dict_zhenxuan.json
        """
        self.base_url = "https://dict.gmcc.net:30722"
        self.api_endpoint = "/partner/materialManage/pnrSelectProject/queryLocalAuditTrackHistory"
        self.db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
        self.session = requests.Session()
        
        # 禁用SSL证书验证
        self.session.verify = False
        # 禁用SSL警告
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        # 默认请求头
        self.headers = {
            'Host': "dict.gmcc.net:30722",
            'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            'Accept': "application/json, text/plain, */*",
            'Accept-Encoding': "gzip, deflate, br, zstd",
            'Content-Type': "application/json;charset=UTF-8",
            'sec-ch-ua-platform': '"Windows"',
            'Authorization': "Bearer d25c514c-e026-4ddf-b455-9929dfcd3cfb",
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': "?0",
            'Origin': "https://dict.gmcc.net:30722",
            'Sec-Fetch-Site': "same-origin",
            'Sec-Fetch-Mode': "cors",
            'Sec-Fetch-Dest': "empty",
            'Referer': "https://dict.gmcc.net:30722/ptn/main/selectDemand/detail",
            'Accept-Language': "zh-CN,zh;q=0.9,ee;q=0.8",
        }
        
        # 初始化Cookie
        self.cookies = {}
        self.cookie_string = ""

        # 设置默认Cookie文件路径
        if cookie_file_path is None:
            # 查找项目根目录下的cookies文件
            cookie_file_path = project_root / "cookies" / "cookies_dict_zhenxuan.json"

        # 加载Cookie
        self.load_cookies_from_file(cookie_file_path)

    def load_cookies_from_file(self, cookie_file_path: Path):
        """
        从JSON文件加载Cookie，转换为Cookie字符串格式
        保留所有Cookie，包括同名但不同path的Cookie

        Args:
            cookie_file_path: Cookie文件路径
        """
        try:
            if cookie_file_path.exists():
                with open(cookie_file_path, 'r', encoding='utf-8') as f:
                    cookie_data = json.load(f)

                # 生成Cookie字符串（保留所有Cookie，包括同名的）
                cookie_pairs = []
                cookie_dict = {}  # 用于显示和统计

                for cookie in cookie_data:
                    name = cookie['name']
                    value = cookie['value']
                    cookie_pairs.append(f"{name}={value}")
                    cookie_dict[name] = value

                self.cookie_string = "; ".join(cookie_pairs)
                self.cookies = cookie_dict
                self.headers['Cookie'] = self.cookie_string

                logger.info(f"✅ 成功加载Cookie文件: {cookie_file_path}")
                logger.info(f"📊 Cookie统计: {len(cookie_data)} 个原始Cookie，{len(cookie_dict)} 个去重Cookie")
            else:
                logger.warning(f"⚠️ Cookie文件不存在: {cookie_file_path}")
                logger.warning("⚠️ 将使用默认Cookie配置")
        except Exception as e:
            logger.error(f"❌ 加载Cookie文件失败: {e}")
            logger.warning("⚠️ 将使用默认Cookie配置")

    def update_cookies(self, cookie_string: str):
        """
        更新Cookie字符串
        
        Args:
            cookie_string: Cookie字符串，格式如 "key1=value1; key2=value2"
        """
        self.cookie_string = cookie_string
        self.headers['Cookie'] = cookie_string
        
        # 解析为字典格式
        self.cookies = {}
        for pair in cookie_string.split(';'):
            if '=' in pair:
                key, value = pair.strip().split('=', 1)
                self.cookies[key] = value
        
        logger.info(f"✅ 已更新Cookie: {len(self.cookies)} 个Cookie")

    def get_project_msg_ids(self) -> List[str]:
        """
        从 zhenxuan_querySelectProjectList 表获取所有 projectMsgId
        
        Returns:
            List[str]: projectMsgId 列表
        """
        if not self.db_manager.connect():
            return []

        try:
            sql = """
            SELECT DISTINCT project_msg_id 
            FROM zhenxuan_querySelectProjectList 
            WHERE project_msg_id IS NOT NULL 
            AND project_msg_id != ''
            ORDER BY project_msg_id
            """
            
            with self.db_manager.get_cursor() as cursor:
                cursor.execute(sql)
                results = cursor.fetchall()
                
            project_msg_ids = [row['project_msg_id'] for row in results]
            logger.info(f"📋 从数据库获取到 {len(project_msg_ids)} 个项目消息ID")
            return project_msg_ids
            
        except Exception as e:
            logger.error(f"❌ 获取项目消息ID失败: {e}")
            return []
        finally:
            self.db_manager.disconnect()

    def fetch_audit_track_history(self, project_msg_id: str) -> Optional[Dict[str, Any]]:
        """
        获取指定项目的审核跟踪历史数据
        
        Args:
            project_msg_id: 项目消息ID（作为businessId使用）
            
        Returns:
            Optional[Dict]: API响应数据，失败时返回None
        """
        url = f"{self.base_url}{self.api_endpoint}"
        
        # 构建请求参数：使用projectMsgId作为businessId，workOrderMsgId为null，stepName为空字符串
        payload = {
            "businessId": project_msg_id,
            "workOrderMsgId": None,
            "stepName": ""
        }
        
        try:
            logger.info(f"🔍 正在获取项目 {project_msg_id} 的审核跟踪历史数据...")
            
            response = self.session.post(
                url,
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ 成功获取项目 {project_msg_id} 的数据")
                return data
            else:
                logger.error(f"❌ API请求失败，状态码: {response.status_code}")
                logger.error(f"❌ 响应内容: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 请求异常: {e}")
            return None

    def parse_and_save_data(self, project_msg_id: str, api_data: Dict[str, Any]) -> bool:
        """
        解析API数据并保存到数据库
        
        Args:
            project_msg_id: 项目消息ID
            api_data: API响应数据
            
        Returns:
            bool: 保存是否成功
        """
        if not self.db_manager.connect():
            return False

        try:
            # 解析基础响应信息
            busi_date = api_data.get('busiDate')
            code = api_data.get('code')
            message = api_data.get('message')
            result_body = api_data.get('resultBody', [])
            
            # 转换日期格式
            busi_date_obj = None
            if busi_date:
                try:
                    busi_date_obj = datetime.strptime(busi_date, '%Y-%m-%d %H:%M:%S')
                except:
                    pass

            saved_count = 0
            
            # 处理resultBody中的每条记录
            for record in result_body:
                # 解析记录数据
                audit_process_track_id = record.get('auditProcessTrackId')
                business_id = record.get('businessId')
                step_name = record.get('stepName')
                create_time = record.get('createTime')
                finish_time = record.get('finishTime')
                status = record.get('status')
                audit_handler = record.get('auditHandler')
                audit_remark = record.get('auditRemark')
                
                # 转换时间格式
                create_time_obj = None
                finish_time_obj = None
                
                if create_time:
                    try:
                        create_time_obj = datetime.strptime(create_time, '%Y-%m-%d %H:%M:%S')
                    except:
                        pass
                        
                if finish_time:
                    try:
                        finish_time_obj = datetime.strptime(finish_time, '%Y-%m-%d %H:%M:%S')
                    except:
                        pass

                # 构建请求参数JSON
                request_params = {
                    "businessId": project_msg_id,
                    "workOrderMsgId": None,
                    "stepName": ""
                }

                # 插入数据库
                sql = """
                INSERT INTO zhenxuan_querylocalaudittrackhistory_xqxx (
                    project_msg_id, business_id, work_order_msg_id, step_name_filter,
                    request_params, busi_date, code, message,
                    audit_process_track_id, step_name, create_time, finish_time,
                    status, audit_handler, audit_remark, raw_data
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                ) ON DUPLICATE KEY UPDATE
                    step_name = VALUES(step_name),
                    create_time = VALUES(create_time),
                    finish_time = VALUES(finish_time),
                    status = VALUES(status),
                    audit_handler = VALUES(audit_handler),
                    audit_remark = VALUES(audit_remark),
                    raw_data = VALUES(raw_data),
                    updated_at = CURRENT_TIMESTAMP
                """
                
                with self.db_manager.get_cursor() as cursor:
                    cursor.execute(sql, (
                        project_msg_id,  # project_msg_id
                        business_id,     # business_id
                        None,            # work_order_msg_id (固定为null)
                        "",              # step_name_filter (固定为空字符串)
                        json.dumps(request_params, ensure_ascii=False),  # request_params
                        busi_date_obj,   # busi_date
                        code,            # code
                        message,         # message
                        audit_process_track_id,  # audit_process_track_id
                        step_name,       # step_name
                        create_time_obj, # create_time
                        finish_time_obj, # finish_time
                        status,          # status
                        audit_handler,   # audit_handler
                        audit_remark,    # audit_remark
                        json.dumps(record, ensure_ascii=False)  # raw_data
                    ))
                    
                saved_count += 1

            logger.info(f"✅ 项目 {project_msg_id} 保存了 {saved_count} 条审核跟踪记录")
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存数据失败: {e}")
            return False
        finally:
            self.db_manager.disconnect()

    def fetch_all_data(self):
        """
        获取所有项目的审核跟踪历史数据
        """
        logger.info("🚀 开始获取所有项目的审核跟踪历史数据...")
        
        # 获取所有项目消息ID
        project_msg_ids = self.get_project_msg_ids()
        
        if not project_msg_ids:
            logger.warning("⚠️ 没有找到任何项目消息ID")
            return
        
        total_count = len(project_msg_ids)
        success_count = 0
        
        for i, project_msg_id in enumerate(project_msg_ids, 1):
            logger.info(f"📊 处理进度: {i}/{total_count} - 项目ID: {project_msg_id}")
            
            # 获取数据
            api_data = self.fetch_audit_track_history(project_msg_id)
            
            if api_data:
                # 保存数据
                if self.parse_and_save_data(project_msg_id, api_data):
                    success_count += 1
                else:
                    logger.error(f"❌ 项目 {project_msg_id} 数据保存失败")
            else:
                logger.error(f"❌ 项目 {project_msg_id} 数据获取失败")
            
            # 请求间隔，避免过快请求
            if i < total_count:
                time.sleep(1)
        
        logger.info(f"🎉 数据获取完成！总计: {total_count}, 成功: {success_count}, 失败: {total_count - success_count}")

    def query_data(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        查询数据库中的数据
        
        Args:
            limit: 查询记录数限制
            
        Returns:
            List[Dict]: 查询结果
        """
        if not self.db_manager.connect():
            return []

        try:
            sql = """
            SELECT
                id, project_msg_id, business_id, step_name, status,
                audit_handler, create_time, finish_time, audit_remark, created_at
            FROM zhenxuan_querylocalaudittrackhistory_xqxx
            ORDER BY created_at DESC, create_time DESC
            LIMIT %s
            """
            
            with self.db_manager.get_cursor() as cursor:
                cursor.execute(sql, (limit,))
                return cursor.fetchall()
                
        except Exception as e:
            logger.error(f"❌ 查询数据失败: {e}")
            return []
        finally:
            self.db_manager.disconnect()


def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(description='甄选本地审核跟踪历史数据获取和入库程序（需求信息版本）')
    parser.add_argument('--project-msg-id', type=str, help='指定单个项目消息ID')
    parser.add_argument('--cookie-file', type=str, help='Cookie文件路径，默认使用cookies/cookies_dict_zhenxuan.json')
    parser.add_argument('--cookie', type=str, help='更新Cookie字符串')
    parser.add_argument('--query', action='store_true', help='查询已同步的数据')
    parser.add_argument('--limit', type=int, default=10, help='查询记录数限制，默认10')
    parser.add_argument('-all', action='store_true', help='轮询所有项目模式，从数据库获取所有项目ID并同步')

    args = parser.parse_args()

    # 创建数据获取器（使用指定的Cookie文件）
    fetcher = QueryLocalAuditTrackHistoryXqxxFetcher(cookie_file_path=args.cookie_file)

    # 更新Cookie（如果提供）
    if args.cookie:
        fetcher.update_cookies(args.cookie)

    # 查询模式
    if args.query:
        logger.info("🔍 查询数据库中的数据...")
        results = fetcher.query_data(args.limit)

        if results:
            logger.info(f"📋 查询到 {len(results)} 条记录:")
            for i, record in enumerate(results, 1):
                logger.info(f"  {i}. 项目ID: {record['project_msg_id']}")
                logger.info(f"     步骤: {record['step_name']}")
                logger.info(f"     状态: {record['status']}")
                logger.info(f"     处理人: {record['audit_handler']}")
                logger.info(f"     创建时间: {record['create_time']}")
                logger.info("")
        else:
            logger.info("📋 没有查询到数据")
        return

    # 单个项目模式
    if args.project_msg_id:
        logger.info(f"🎯 获取单个项目数据: {args.project_msg_id}")
        api_data = fetcher.fetch_audit_track_history(args.project_msg_id)
        
        if api_data:
            if fetcher.parse_and_save_data(args.project_msg_id, api_data):
                logger.info("✅ 数据获取和保存成功")
            else:
                logger.error("❌ 数据保存失败")
        else:
            logger.error("❌ 数据获取失败")
        return

    # 全部数据模式（默认）
    if getattr(args, 'all', False):
        fetcher.fetch_all_data()
    else:
        logger.info("ℹ️ 请使用 -all 参数来获取所有数据，或使用 --project-msg-id 指定单个项目")
        logger.info("ℹ️ 使用 --query 参数查询已有数据")


if __name__ == "__main__":
    main()
