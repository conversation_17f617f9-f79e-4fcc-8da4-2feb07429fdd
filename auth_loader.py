"""
通用认证信息加载器
支持新旧两种cookie文件格式
"""

import json
import os
import logging
from typing import Dict, Any, Optional, Tuple

logger = logging.getLogger(__name__)


class AuthLoader:
    """认证信息加载器，支持新旧格式"""
    
    def __init__(self, cookie_file_path: str = None):
        """
        初始化认证加载器
        
        Args:
            cookie_file_path: cookie文件路径，默认为 cookies/cookies_dict_zhenxuan.json
        """
        if cookie_file_path is None:
            # 查找项目根目录下的cookies文件
            project_root = os.path.dirname(os.path.abspath(__file__))
            cookie_file_path = os.path.join(project_root, 'cookies', 'cookies_dict_zhenxuan.json')
        
        self.cookie_file_path = cookie_file_path
        self.auth_data = None
        self.cookies_dict = {}
        self.cookie_string = ""
        self.headers = {}
        
    def load_auth_data(self) -> bool:
        """
        加载认证数据，自动检测文件格式
        
        Returns:
            bool: 加载是否成功
        """
        try:
            if not os.path.exists(self.cookie_file_path):
                logger.warning(f"⚠️ 认证文件不存在: {self.cookie_file_path}")
                return False
            
            with open(self.cookie_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 检测文件格式
            if self._is_new_format(data):
                logger.info("📋 检测到新格式认证文件（包含headers）")
                return self._load_new_format(data)
            else:
                logger.info("📋 检测到旧格式认证文件（仅cookies）")
                return self._load_old_format(data)
                
        except Exception as e:
            logger.error(f"❌ 加载认证文件失败: {e}")
            return False
    
    def _is_new_format(self, data: Any) -> bool:
        """
        检测是否为新格式（包含cookies、headers等字段）
        
        Args:
            data: 加载的JSON数据
            
        Returns:
            bool: 是否为新格式
        """
        if isinstance(data, dict):
            # 新格式应该包含 cookies 和 headers 字段
            return 'cookies' in data and 'headers' in data
        elif isinstance(data, list):
            # 旧格式是直接的cookie数组
            return False
        else:
            return False
    
    def _load_new_format(self, data: Dict[str, Any]) -> bool:
        """
        加载新格式认证数据
        
        Args:
            data: 新格式认证数据
            
        Returns:
            bool: 加载是否成功
        """
        try:
            self.auth_data = data
            
            # 提取cookies
            cookies = data.get('cookies', [])
            self._process_cookies(cookies)
            
            # 提取headers
            headers = data.get('headers', {})
            self.headers = headers.copy()
            
            # 确保Cookie header包含完整的cookie字符串
            if self.cookie_string:
                self.headers['Cookie'] = self.cookie_string
            
            logger.info(f"✅ 成功加载新格式认证数据")
            logger.info(f"📅 保存时间: {data.get('timestamp', 'Unknown')}")
            logger.info(f"🌐 URL: {data.get('url', 'Unknown')}")
            logger.info(f"📋 加载了 {len(cookies)} 个Cookie")
            logger.info(f"📋 加载了 {len(headers)} 个Header")
            
            # 检查Authorization token
            if 'Authorization' in headers:
                token = headers['Authorization']
                logger.info(f"🔑 找到Authorization token: {token[:30]}...")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 处理新格式认证数据失败: {e}")
            return False
    
    def _load_old_format(self, data: list) -> bool:
        """
        加载旧格式认证数据（仅cookies数组）
        
        Args:
            data: 旧格式cookie数组
            
        Returns:
            bool: 加载是否成功
        """
        try:
            # 处理cookies
            self._process_cookies(data)
            
            # 设置基本headers
            self.headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Content-Type': 'application/json;charset=UTF-8',
                'X-Requested-With': 'XMLHttpRequest'
            }
            
            # 添加Cookie header
            if self.cookie_string:
                self.headers['Cookie'] = self.cookie_string
            
            logger.info(f"✅ 成功加载旧格式认证数据")
            logger.info(f"📋 加载了 {len(data)} 个Cookie")
            logger.info(f"📋 生成了 {len(self.headers)} 个Header")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 处理旧格式认证数据失败: {e}")
            return False
    
    def _process_cookies(self, cookies: list):
        """
        处理cookies数组，生成cookie字典和字符串
        
        Args:
            cookies: cookie数组
        """
        cookie_pairs = []
        cookie_dict = {}
        
        for cookie in cookies:
            name = cookie['name']
            value = cookie['value']
            path = cookie.get('path', '/')
            
            # 添加到Cookie字符串（所有Cookie都要包含）
            cookie_pairs.append(f"{name}={value}")
            
            # 用于显示的字典（同名Cookie显示最后一个，但实际都会发送）
            if name not in cookie_dict:
                cookie_dict[name] = []
            cookie_dict[name].append({'value': value, 'path': path})
        
        self.cookie_string = "; ".join(cookie_pairs)
        self.cookies_dict = {name: cookies[-1]['value'] for name, cookies in cookie_dict.items()}
        
        # 显示重复Cookie统计
        for name, cookies in cookie_dict.items():
            if len(cookies) > 1:
                logger.info(f"  - {name}: {len(cookies)}个 (不同path)")
            else:
                logger.info(f"  - {name}: {cookies[0]['value'][:20]}...")
        
        logger.info(f"📋 完整Cookie字符串长度: {len(self.cookie_string)} 字符")
    
    def get_cookies_dict(self) -> Dict[str, str]:
        """获取cookies字典"""
        return self.cookies_dict.copy()
    
    def get_cookie_string(self) -> str:
        """获取cookie字符串"""
        return self.cookie_string
    
    def get_headers(self) -> Dict[str, str]:
        """获取完整的headers字典"""
        return self.headers.copy()
    
    def get_requests_config(self) -> Dict[str, Any]:
        """
        获取requests库使用的配置
        
        Returns:
            dict: 包含headers和cookies的配置
        """
        return {
            'headers': self.get_headers(),
            'cookies': self.get_cookies_dict(),
            'verify': False  # 忽略SSL证书验证
        }
    
    def update_session(self, session):
        """
        更新requests session的认证信息
        
        Args:
            session: requests.Session对象
        """
        session.headers.update(self.get_headers())
        session.cookies.update(self.get_cookies_dict())
        session.verify = False


def main():
    """测试函数"""
    loader = AuthLoader()
    
    if loader.load_auth_data():
        print("\n🔐 认证信息加载成功")
        print(f"🍪 Cookie字符串长度: {len(loader.get_cookie_string())}")
        print(f"📋 Headers数量: {len(loader.get_headers())}")
        
        # 显示部分信息
        headers = loader.get_headers()
        print("\n📋 Headers:")
        for key, value in list(headers.items())[:5]:
            if key == 'Authorization':
                print(f"  {key}: {str(value)[:30]}...")
            elif key == 'Cookie':
                print(f"  {key}: {str(value)[:50]}...")
            else:
                print(f"  {key}: {value}")
        
        if len(headers) > 5:
            print(f"  ... 还有 {len(headers) - 5} 个headers")
    else:
        print("❌ 认证信息加载失败")


if __name__ == "__main__":
    main()
