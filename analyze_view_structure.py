#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析 v_zhenxuan_queryselectapplydetail_done 视图结构
"""

import sys
import os
from database.db_config import DatabaseManager, ZHENXUAN_DB_CONFIG

def analyze_view_structure():
    """分析视图结构"""
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    if not db_manager.connect():
        print("❌ 数据库连接失败")
        return
    
    try:
        print("📋 分析 v_zhenxuan_queryselectapplydetail_done 视图结构")
        print("="*80)
        
        with db_manager.get_cursor() as cursor:
            # 检查视图是否存在
            cursor.execute("SHOW TABLES LIKE 'v_zhenxuan_queryselectapplydetail_done'")
            result = cursor.fetchone()
            
            if not result:
                print("❌ 视图 v_zhenxuan_queryselectapplydetail_done 不存在")
                
                # 查看所有相关视图
                print("\n📋 查找相关视图:")
                cursor.execute("SHOW TABLES LIKE 'v_zhenxuan%'")
                views = cursor.fetchall()
                
                if views:
                    print("找到的相关视图:")
                    for view in views:
                        view_name = list(view.values())[0]
                        print(f"  - {view_name}")
                        
                        # 查看每个视图的结构
                        try:
                            cursor.execute(f"DESCRIBE {view_name}")
                            columns = cursor.fetchall()
                            print(f"    字段数: {len(columns)}")
                            
                            # 查看记录数
                            cursor.execute(f"SELECT COUNT(*) as count FROM {view_name}")
                            count_result = cursor.fetchone()
                            print(f"    记录数: {count_result['count']}")
                            print()
                        except Exception as e:
                            print(f"    ❌ 查看结构失败: {e}")
                else:
                    print("❌ 没有找到相关视图")
                return
            
            print("✅ 视图存在，开始分析结构...")
            
            # 查看视图结构
            print("\n📋 视图字段结构:")
            cursor.execute("DESCRIBE v_zhenxuan_queryselectapplydetail_done")
            columns = cursor.fetchall()
            
            print(f"字段总数: {len(columns)}")
            print("字段详情:")
            for i, col in enumerate(columns, 1):
                print(f"  {i:2d}. {col['Field']:30s} - {col['Type']:20s} - NULL:{col['Null']:3s} - KEY:{col['Key']:3s}")
            
            # 查看视图记录数
            print(f"\n📊 视图数据统计:")
            cursor.execute("SELECT COUNT(*) as total FROM v_zhenxuan_queryselectapplydetail_done")
            count_result = cursor.fetchone()
            print(f"总记录数: {count_result['total']}")
            
            # 查看几条示例数据
            print(f"\n📋 示例数据（前3条）:")
            cursor.execute("SELECT * FROM v_zhenxuan_queryselectapplydetail_done LIMIT 3")
            sample_data = cursor.fetchall()
            
            for i, row in enumerate(sample_data, 1):
                print(f"\n记录 {i}:")
                for key, value in row.items():
                    if value is not None:
                        value_str = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                        print(f"  {key}: {value_str}")
            
            # 分析字段类型分布
            print(f"\n📊 字段类型分布:")
            type_count = {}
            for col in columns:
                col_type = col['Type'].split('(')[0]  # 去掉长度限制
                type_count[col_type] = type_count.get(col_type, 0) + 1
            
            for col_type, count in sorted(type_count.items()):
                print(f"  {col_type}: {count} 个字段")
            
            # 检查是否有主键或唯一字段
            print(f"\n🔑 键信息:")
            key_fields = [col['Field'] for col in columns if col['Key'] in ['PRI', 'UNI', 'MUL']]
            if key_fields:
                print(f"有键的字段: {', '.join(key_fields)}")
            else:
                print("没有找到键字段")
            
            # 检查NULL值分布
            print(f"\n📊 NULL值分布检查:")
            for col in columns[:10]:  # 只检查前10个字段，避免查询过慢
                field_name = col['Field']
                try:
                    cursor.execute(f"""
                    SELECT 
                        COUNT(*) as total,
                        COUNT({field_name}) as not_null,
                        COUNT(*) - COUNT({field_name}) as null_count
                    FROM v_zhenxuan_queryselectapplydetail_done
                    """)
                    null_stats = cursor.fetchone()
                    null_pct = (null_stats['null_count'] / null_stats['total'] * 100) if null_stats['total'] > 0 else 0
                    print(f"  {field_name:30s}: NULL {null_stats['null_count']:4d}/{null_stats['total']} ({null_pct:5.1f}%)")
                except Exception as e:
                    print(f"  {field_name:30s}: ❌ 检查失败")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    analyze_view_structure()
