"""
验证 zhenxuan_queryLocalAuditTrackHistory 表数据
检查新增字段 scoreRuleId 和 scoreOrderMsgId 是否正确入库
"""

import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

from database.db_config import ZHENXUAN_DB_CONFIG, DatabaseManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('verify_audit_track_data.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def verify_data():
    """验证数据"""
    
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    try:
        # 连接数据库
        if not db_manager.connect():
            logger.error("❌ 数据库连接失败")
            return False
        
        logger.info("✅ 数据库连接成功")
        
        # 查询数据统计
        count_sql = """
        SELECT COUNT(*) as total_count
        FROM zhenxuan_queryLocalAuditTrackHistory
        """
        
        with db_manager.get_cursor() as cursor:
            cursor.execute(count_sql)
            result = cursor.fetchone()
            total_count = result['total_count']
            
            logger.info(f"📊 总记录数: {total_count}")
        
        # 查询新增字段数据
        new_fields_sql = """
        SELECT 
            id,
            project_msg_id,
            business_id,
            score_rule_id,
            score_order_msg_id,
            audit_process_track_id,
            step_name,
            status,
            audit_handler,
            create_time,
            finish_time
        FROM zhenxuan_queryLocalAuditTrackHistory
        ORDER BY id DESC
        LIMIT 10
        """
        
        with db_manager.get_cursor() as cursor:
            cursor.execute(new_fields_sql)
            records = cursor.fetchall()
            
            logger.info(f"📋 最新 {len(records)} 条记录:")
            for i, record in enumerate(records, 1):
                logger.info(f"   记录 {i}:")
                logger.info(f"     ID: {record['id']}")
                logger.info(f"     项目消息ID: {record['project_msg_id']}")
                logger.info(f"     业务ID: {record['business_id']}")
                logger.info(f"     评分规则ID: {record['score_rule_id']}")
                logger.info(f"     评分工单消息ID: {record['score_order_msg_id']}")
                logger.info(f"     审核流程跟踪ID: {record['audit_process_track_id']}")
                logger.info(f"     步骤名称: {record['step_name']}")
                logger.info(f"     状态: {record['status']}")
                logger.info(f"     审核处理人: {record['audit_handler']}")
                logger.info(f"     创建时间: {record['create_time']}")
                logger.info(f"     完成时间: {record['finish_time']}")
                logger.info("")
        
        # 验证新增字段是否有值
        new_fields_count_sql = """
        SELECT 
            COUNT(*) as total,
            COUNT(score_rule_id) as score_rule_id_count,
            COUNT(score_order_msg_id) as score_order_msg_id_count
        FROM zhenxuan_queryLocalAuditTrackHistory
        """
        
        with db_manager.get_cursor() as cursor:
            cursor.execute(new_fields_count_sql)
            result = cursor.fetchone()
            
            logger.info("📊 新增字段统计:")
            logger.info(f"   总记录数: {result['total']}")
            logger.info(f"   score_rule_id 有值记录数: {result['score_rule_id_count']}")
            logger.info(f"   score_order_msg_id 有值记录数: {result['score_order_msg_id_count']}")
        
        # 查询不同的 scoreRuleId 和 scoreOrderMsgId 组合
        distinct_params_sql = """
        SELECT DISTINCT 
            score_rule_id,
            score_order_msg_id,
            COUNT(*) as record_count
        FROM zhenxuan_queryLocalAuditTrackHistory
        WHERE score_rule_id IS NOT NULL 
        AND score_order_msg_id IS NOT NULL
        GROUP BY score_rule_id, score_order_msg_id
        ORDER BY record_count DESC
        """
        
        with db_manager.get_cursor() as cursor:
            cursor.execute(distinct_params_sql)
            params_groups = cursor.fetchall()
            
            logger.info(f"📋 不同参数组合 ({len(params_groups)} 组):")
            for i, group in enumerate(params_groups, 1):
                logger.info(f"   组合 {i}:")
                logger.info(f"     评分规则ID: {group['score_rule_id']}")
                logger.info(f"     评分工单消息ID: {group['score_order_msg_id']}")
                logger.info(f"     记录数: {group['record_count']}")
                logger.info("")
        
        logger.info("🎉 数据验证完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 验证数据失败: {e}")
        return False
    finally:
        db_manager.disconnect()

def main():
    """主函数"""
    logger.info("=" * 80)
    logger.info("🚀 验证 zhenxuan_queryLocalAuditTrackHistory 表数据")
    logger.info("=" * 80)
    
    start_time = datetime.now()
    
    try:
        success = verify_data()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if success:
            logger.info("=" * 80)
            logger.info("🎉 数据验证成功！")
            logger.info(f"⏱️ 总耗时: {duration:.2f} 秒")
            logger.info("=" * 80)
        else:
            logger.error("❌ 数据验证失败")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"❌ 程序执行异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
