"""
创建包含Bearer token的认证文件
将现有的cookies和已知的Bearer token合并为新格式
"""

import json
import os
from datetime import datetime


def create_enhanced_auth_file():
    """创建增强的认证文件"""
    
    # 读取现有的cookies文件
    cookie_file = "cookies/cookies_dict_zhenxuan.json"
    
    if not os.path.exists(cookie_file):
        print(f"❌ Cookie文件不存在: {cookie_file}")
        return False
    
    try:
        with open(cookie_file, 'r', encoding='utf-8') as f:
            cookies = json.load(f)
        
        print(f"✅ 成功读取现有cookies文件")
        print(f"📋 包含 {len(cookies)} 个cookies")
        
        # 已知的Bearer tokens（从现有脚本中提取）
        bearer_tokens = [
            "040bf40f-2e2a-4104-9111-52a577c3ec61",  # 来自 fetch_querySelectProjectList.py
            "d25c514c-e026-4ddf-b455-9929dfcd3cfb"   # 来自 zhenxuan_querySelectApplyDetail.py
        ]
        
        print(f"\n🔑 发现的Bearer tokens:")
        for i, token in enumerate(bearer_tokens, 1):
            print(f"  {i}. {token}")
        
        # 选择使用哪个token（默认使用第二个，因为它在更多脚本中使用）
        selected_token = bearer_tokens[1]
        print(f"\n✅ 选择使用token: {selected_token}")
        
        # 创建新格式的认证数据
        auth_data = {
            "cookies": cookies,
            "headers": {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": "zh-CN,zh;q=0.9",
                "Accept-Encoding": "gzip, deflate, br",
                "Connection": "keep-alive",
                "Content-Type": "application/json;charset=UTF-8",
                "X-Requested-With": "XMLHttpRequest",
                "Authorization": f"Bearer {selected_token}",
                "Host": "dict.gmcc.net:30722",
                "Origin": "http://dict.gmcc.net:30722",
                "Referer": "http://dict.gmcc.net:30722/ptn/main/selectDemand"
            },
            "timestamp": datetime.now().isoformat(),
            "url": "http://dict.gmcc.net:30722/ptn/main/selectDemand",
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "source": "manual_creation_from_existing_tokens"
        }
        
        # 生成cookie字符串并添加到headers
        cookie_pairs = []
        for cookie in cookies:
            cookie_pairs.append(f"{cookie['name']}={cookie['value']}")
        cookie_string = "; ".join(cookie_pairs)
        auth_data["headers"]["Cookie"] = cookie_string
        
        # 备份原文件
        backup_file = f"{cookie_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(cookies, f, indent=2, ensure_ascii=False)
        print(f"💾 原文件已备份到: {backup_file}")
        
        # 保存新格式文件
        with open(cookie_file, 'w', encoding='utf-8') as f:
            json.dump(auth_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 新格式认证文件已保存到: {cookie_file}")
        print(f"📋 包含信息:")
        print(f"  - Cookies: {len(auth_data['cookies'])} 个")
        print(f"  - Headers: {len(auth_data['headers'])} 个")
        print(f"  - Bearer Token: {selected_token[:20]}...")
        print(f"  - Cookie字符串长度: {len(cookie_string)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建认证文件失败: {e}")
        return False


def verify_auth_file():
    """验证认证文件格式"""
    cookie_file = "cookies/cookies_dict_zhenxuan.json"
    
    try:
        with open(cookie_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"\n🔍 验证认证文件格式...")
        
        if isinstance(data, list):
            print("📋 格式: 旧格式（仅cookies数组）")
            print(f"📊 Cookies数量: {len(data)}")
        elif isinstance(data, dict):
            print("📋 格式: 新格式（包含cookies和headers）")
            
            cookies = data.get('cookies', [])
            headers = data.get('headers', {})
            
            print(f"📊 Cookies数量: {len(cookies)}")
            print(f"📊 Headers数量: {len(headers)}")
            
            if 'Authorization' in headers:
                auth_header = headers['Authorization']
                print(f"🔑 Authorization: {auth_header[:30]}...")
            else:
                print("⚠️ 未找到Authorization header")
            
            if 'timestamp' in data:
                print(f"📅 创建时间: {data['timestamp']}")
            
            if 'url' in data:
                print(f"🌐 URL: {data['url']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证文件失败: {e}")
        return False


def main():
    """主函数"""
    print("🔧 Bearer Token认证文件创建工具")
    print("="*50)
    
    # 验证当前文件格式
    verify_auth_file()
    
    # 询问是否要创建新格式文件
    print(f"\n❓ 是否要将现有cookies文件升级为包含Bearer token的新格式？")
    print("   这将备份原文件并创建新格式文件。")
    
    choice = input("请输入 y/yes 确认，其他键取消: ").lower().strip()
    
    if choice in ['y', 'yes']:
        if create_enhanced_auth_file():
            print(f"\n✅ 认证文件升级完成！")
            
            # 再次验证新文件
            print(f"\n🔍 验证新文件...")
            verify_auth_file()
            
            print(f"\n🚀 现在可以使用新的认证系统了！")
            print("测试命令:")
            print("  python auth_loader.py")
            print("  python example_api_with_new_auth.py")
        else:
            print(f"\n❌ 认证文件升级失败")
    else:
        print(f"\n⏹️ 操作已取消")


if __name__ == "__main__":
    main()
