# 甄选需求管理页面数据字典 - 优化版

## 📋 分析总结

经过深度分析，该页面使用了现代前端技术栈，具有以下特征：
- **前端框架**: Vue.js + Element UI
- **字段命名**: 直接使用中文字段名，未发现对应的英文字段名
- **技术特点**: 组件化开发，可能使用了编译后的代码

## 🗃️ 核心数据结构

### 主数据表：甄选需求列表

基于实际页面分析和数据抓取，确认的字段结构：

| 序号 | 中文字段名 | 数据类型 | 长度 | 必填 | 英文字段名 | 说明 | 实际样本 |
|------|-----------|---------|------|------|-----------|------|---------|
| 1 | 序号 | Integer | - | ✅ | **无** | 列表排序号 | 1, 2, 3, ... |
| 2 | 项目名称 | String | 50+ | ✅ | **无** | 完整项目名称 | "中山市坤鹏电子科技有限公司信息化建设项目" |
| 3 | 项目编码 | String | 20 | ✅ | **无** | 项目唯一标识 | "CMGDZSICT20250707037" |
| 4 | 需求名称 | String | 30+ | ✅ | **无** | 甄选需求名称 | "中山移动某智慧园区项目" |
| 5 | 甄选方案数量 | Integer | 1 | ✅ | **无** | 方案数量 | 0, 1, 2 |
| 6 | 需求编码 | BigInteger | 19 | ✅ | **无** | 需求唯一ID | "1942422593200898048" |
| 7 | 甄选类别 | String | 10 | ✅ | **无** | 甄选分类 | "项目甄选" |
| 8 | 归属地市 | String | 10 | ✅ | **无** | 所属地市 | "中山" |
| 9 | 创建时间 | DateTime | 19 | ✅ | **无** | 创建时间戳 | "2025-07-08 11:16:49" |
| 10 | 甄选需求状态 | String | 20 | ✅ | **无** | 当前状态 | "审核通过", "审核通过(已制定方案)" |

## 🔍 技术分析结果

### 前端技术栈
- **UI框架**: Element UI (el-table, el-form, el-input等组件)
- **JavaScript框架**: Vue.js
- **CSS类名模式**: 
  - `el-table_1_column_1` (表格列标识)
  - `el-form-item` (表单项)
  - `el-input__inner` (输入框)

### HTML结构特征
```html
<!-- 表格结构 -->
<th class="el-table_1_column_1 is-center is-leaf el-table__cell">
  <div class="cell">序号</div>
</th>

<!-- 表单结构 -->
<input class="el-input__inner" placeholder="项目名称" type="text">
```

### 字段映射分析
经过深度分析，**未发现明确的中英文字段映射**，原因如下：

1. **直接使用中文**: 页面直接使用中文作为字段标识
2. **组件化封装**: Vue组件内部可能有字段映射，但未暴露到DOM
3. **编译后代码**: 可能使用了webpack等工具编译，原始字段名被处理
4. **后端接口**: API可能直接接受中文参数名

## 📊 搜索表单字段

### 查询条件字段

| 中文字段名 | HTML属性 | 元素类型 | 占位符 | CSS类 |
|-----------|---------|---------|--------|-------|
| 项目名称 | placeholder="项目名称" | input[type="text"] | 项目名称 | el-input__inner |
| 项目编码 | placeholder="项目编码" | input[type="text"] | 项目编码 | el-input__inner |
| 需求名称 | placeholder="需求名称" | input[type="text"] | 需求名称 | el-input__inner |
| 甄选类别 | placeholder="请选择" | select | 请选择 | el-select |
| 归属地市 | placeholder="请选择" | select | 请选择 | el-select |
| 需求编码 | placeholder="需求编码" | input[type="text"] | 需求编码 | el-input__inner |
| 甄选需求状态 | placeholder="请选择" | select | 请选择 | el-select |

## 🎛️ 功能按钮分析

### 操作按钮

| 中文按钮名 | CSS类 | 功能说明 | HTML结构 |
|-----------|-------|---------|----------|
| 重置 | el-button | 清空搜索条件 | `<button class="el-button">重置</button>` |
| 查询 | el-button | 执行搜索 | `<button class="el-button">查询</button>` |
| 发起项目甄选 | el-button | 创建项目甄选 | `<button class="el-button">发起项目甄选</button>` |
| 发起产品甄选 | el-button | 创建产品甄选 | `<button class="el-button">发起产品甄选</button>` |
| 发起算力项目甄选 | el-button | 创建算力甄选 | `<button class="el-button">发起算力项目甄选</button>` |
| 发起中移集成项目甄选补录 | el-button | 补录甄选 | `<button class="el-button">发起中移集成项目甄选补录</button>` |
| 甄选附件变更 | el-button | 修改附件 | `<button class="el-button">甄选附件变更</button>` |
| 查看详情 | el-link | 查看详情 | `<a class="el-link">查看详情</a>` |

## 🌐 API接口分析

### 页面访问
- **URL**: `https://dict.gmcc.net:30722/ptn/main/selectDemand`
- **方法**: GET
- **响应**: HTML页面（Vue.js单页应用）

### 推测的API接口
基于页面功能推测可能的API接口：

```javascript
// 查询接口（推测）
GET/POST /api/selectDemand/list
参数: {
  "项目名称": "string",
  "项目编码": "string", 
  "需求名称": "string",
  "甄选类别": "string",
  "归属地市": "string",
  "需求编码": "string",
  "甄选需求状态": "string"
}

// 详情接口（推测）
GET /api/selectDemand/detail/{需求编码}

// 创建接口（推测）
POST /api/selectDemand/create
```

## 📝 数据样本

### 标准数据记录
```json
{
  "序号": 1,
  "项目名称": "中山市坤鹏电子科技有限公司信息化建设项目",
  "项目编码": "CMGDZSICT20250707037",
  "需求名称": "中山移动某智慧园区项目",
  "甄选方案数量": 0,
  "需求编码": "1942422593200898048",
  "甄选类别": "项目甄选",
  "归属地市": "中山",
  "创建时间": "2025-07-08 11:16:49",
  "甄选需求状态": "审核通过"
}
```

## 🔧 技术实现建议

### 1. 数据抓取
由于字段直接使用中文，建议：
- 使用中文字段名进行数据处理
- 在数据库设计时可以使用英文字段名，但需要建立映射关系
- API接口可能需要支持中文参数名

### 2. 字段映射建议
如果需要英文字段名，建议的映射关系：

| 中文字段名 | 建议英文名 | 说明 |
|-----------|-----------|------|
| 序号 | `id` 或 `sequence` | 主键或序号 |
| 项目名称 | `projectName` | 项目名称 |
| 项目编码 | `projectCode` | 项目编码 |
| 需求名称 | `demandName` | 需求名称 |
| 甄选方案数量 | `solutionCount` | 方案数量 |
| 需求编码 | `demandCode` | 需求编码 |
| 甄选类别 | `selectionType` | 甄选类别 |
| 归属地市 | `city` | 归属地市 |
| 创建时间 | `createTime` | 创建时间 |
| 甄选需求状态 | `status` | 状态 |

### 3. 数据库设计建议
```sql
CREATE TABLE selection_demand (
  id BIGINT PRIMARY KEY,                    -- 序号
  project_name VARCHAR(100),                -- 项目名称
  project_code VARCHAR(50),                 -- 项目编码
  demand_name VARCHAR(100),                 -- 需求名称
  solution_count INT,                       -- 甄选方案数量
  demand_code BIGINT,                       -- 需求编码
  selection_type VARCHAR(20),               -- 甄选类别
  city VARCHAR(20),                         -- 归属地市
  create_time DATETIME,                     -- 创建时间
  status VARCHAR(50)                        -- 甄选需求状态
);
```

## 📋 总结

1. **字段命名**: 该系统直接使用中文字段名，符合国内系统的常见做法
2. **技术栈**: 使用Vue.js + Element UI，现代化的前端技术
3. **数据结构**: 10个核心字段，结构清晰，数据完整
4. **英文映射**: 原系统未使用英文字段名，如需要可按建议进行映射

**注意**: 本分析基于页面结构和实际数据抓取，如需要准确的API字段名，建议：
- 查看浏览器开发者工具的网络请求
- 联系系统开发团队获取API文档
- 使用Vue DevTools查看组件数据结构
