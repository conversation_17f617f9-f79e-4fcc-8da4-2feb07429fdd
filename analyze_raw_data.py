#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析 zhenxuan_queryselectapplydetail 表的 raw_data 字段
"""

import json
import sys
import os
from database.db_config import DatabaseManager, ZHENXUAN_DB_CONFIG

def analyze_raw_data():
    """分析raw_data字段的JSON结构"""
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    if not db_manager.connect():
        print("❌ 数据库连接失败")
        return
    
    try:
        # 查看表结构
        print("📋 表结构:")
        sql_desc = "DESCRIBE zhenxuan_queryselectapplydetail"
        with db_manager.get_cursor() as cursor:
            cursor.execute(sql_desc)
            columns = cursor.fetchall()
            for col in columns:
                print(f"  {col['Field']} - {col['Type']} - {col['Null']} - {col['Key']}")
        
        print("\n" + "="*80)
        
        # 获取一条记录的raw_data样例
        print("📋 raw_data 样例分析:")
        sql_sample = """
        SELECT id, select_apply_id, raw_data 
        FROM zhenxuan_queryselectapplydetail 
        WHERE raw_data IS NOT NULL 
        LIMIT 1
        """
        
        with db_manager.get_cursor() as cursor:
            cursor.execute(sql_sample)
            result = cursor.fetchone()
            
            if result:
                print(f"记录ID: {result['id']}")
                print(f"申请ID: {result['select_apply_id']}")
                
                # 解析JSON
                try:
                    raw_data = json.loads(result['raw_data'])
                    print("\n📋 JSON结构分析:")
                    
                    # 分析根级字段
                    print("根级字段:")
                    for key in raw_data.keys():
                        print(f"  - {key}: {type(raw_data[key])}")
                    
                    # 分析resultBody字段
                    if 'resultBody' in raw_data:
                        result_body = raw_data['resultBody']
                        print(f"\nresultBody字段 (类型: {type(result_body)}):")
                        if isinstance(result_body, dict):
                            for key, value in result_body.items():
                                print(f"  - {key}: {type(value)} = {str(value)[:100]}...")
                        
                        # 检查目标字段是否存在
                        target_fields = [
                            'select_apply_id', 'request_params', 'select_rev_id', 'select_rev_name', 
                            'project_name', 'customer_name', 'project_code', 'project_no',
                            'select_type', 'select_name', 'select_type_value', 'project_type_value',
                            'create_time', 'start_time', 'end_time',
                            'apply_status_value', 'apply_review_status_value',
                            'review_file_business_id', 'work_order_msg_id', 'score_order_msg_id', 'score_rule_id',
                            'is_need_verification', 'is_finish_verification', 'non_tax_select_budget',
                            'action_remark', 'push_notice',
                            'is_technical_review', 'bid_flag_desc', 'bid_opening_time', 'rating', 'is_pre_review',
                            'select_result_doc', 'result_input_type', 'result_title', 'result_content', 
                            'doc_number_sub', 'doc_number',
                            'select_result_meet', 'select_result_meet_list',
                            'select_msg_id', 'realEndTime', 'systemEndSelectTime', 'selectMsgId'
                        ]
                        
                        print(f"\n📋 目标字段检查:")
                        found_fields = []
                        missing_fields = []
                        
                        for field in target_fields:
                            if field in result_body:
                                found_fields.append(field)
                                value = result_body[field]
                                print(f"  ✅ {field}: {type(value)} = {str(value)[:50]}...")
                            else:
                                missing_fields.append(field)
                                print(f"  ❌ {field}: 不存在")
                        
                        print(f"\n📊 统计:")
                        print(f"  找到字段: {len(found_fields)}/{len(target_fields)}")
                        print(f"  缺失字段: {len(missing_fields)}")
                        
                        if missing_fields:
                            print(f"\n❌ 缺失的字段:")
                            for field in missing_fields:
                                print(f"    - {field}")
                    
                    # 输出完整的JSON结构（格式化）
                    print(f"\n📋 完整JSON结构（前1000字符）:")
                    json_str = json.dumps(raw_data, ensure_ascii=False, indent=2)
                    print(json_str[:1000] + "..." if len(json_str) > 1000 else json_str)
                    
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
            else:
                print("❌ 没有找到包含raw_data的记录")
                
    except Exception as e:
        print(f"❌ 分析失败: {e}")
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    analyze_raw_data()
