"""
深度字段分析器
通过JavaScript代码、网络请求、Vue组件等方式深度挖掘字段的英文名称
"""

import asyncio
import json
import os
import re
from datetime import datetime
from playwright.async_api import async_playwright


class DeepFieldAnalyzer:
    def __init__(self):
        self.target_url = "https://dict.gmcc.net:30722/ptn/main/selectDemand"
        self.cookie_dir = "cookies"
        self.analysis_dir = "deep_analysis"
        self.cookies = None
        
        # 创建分析结果目录
        if not os.path.exists(self.analysis_dir):
            os.makedirs(self.analysis_dir)
        
    def load_cookies(self):
        """加载cookies"""
        try:
            latest_cookie_file = os.path.join(self.cookie_dir, "cookies_dict_zhenxuan.json")
            
            if os.path.exists(latest_cookie_file):
                with open(latest_cookie_file, 'r', encoding='utf-8') as f:
                    self.cookies = json.load(f)
                print(f"✅ 成功加载 {len(self.cookies)} 个cookie")
                return True
            else:
                print("❌ 未找到cookies文件")
                return False
                
        except Exception as e:
            print(f"❌ 加载cookies失败: {e}")
            return False
    
    async def deep_analyze_fields(self):
        """深度分析字段映射"""
        print("🔬 开始深度字段分析...")
        
        if not self.load_cookies():
            return False
            
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False, slow_mo=500)
            context = await browser.new_context(ignore_https_errors=True)
            
            try:
                await context.add_cookies(self.cookies)
                page = await context.new_page()
                
                # 收集网络请求
                network_requests = []
                page.on("request", lambda request: network_requests.append({
                    "url": request.url,
                    "method": request.method,
                    "headers": dict(request.headers),
                    "post_data": request.post_data
                }))
                
                page.on("response", lambda response: network_requests.append({
                    "url": response.url,
                    "status": response.status,
                    "headers": dict(response.headers)
                }))
                
                print(f"🌐 访问页面: {self.target_url}")
                await page.goto(self.target_url, wait_until='networkidle')
                await asyncio.sleep(3)
                
                # 分析结果
                analysis_result = {
                    "javascript_fields": [],
                    "vue_data_fields": [],
                    "network_api_fields": [],
                    "source_code_fields": [],
                    "computed_fields": []
                }
                
                # 1. 分析JavaScript代码中的字段
                analysis_result["javascript_fields"] = await self.analyze_javascript_fields(page)
                
                # 2. 分析Vue组件数据
                analysis_result["vue_data_fields"] = await self.analyze_vue_data(page)
                
                # 3. 触发网络请求并分析
                analysis_result["network_api_fields"] = await self.analyze_network_requests(page, network_requests)
                
                # 4. 分析页面源代码
                analysis_result["source_code_fields"] = await self.analyze_source_code(page)
                
                # 5. 推断字段映射
                analysis_result["computed_fields"] = self.compute_field_mappings(analysis_result)
                
                # 保存分析结果
                await self.save_deep_analysis(analysis_result)
                
                # 生成最终的字段映射文档
                await self.generate_final_mapping_doc(analysis_result)
                
                print("✅ 深度字段分析完成")
                await asyncio.sleep(5)
                return True
                
            except Exception as e:
                print(f"❌ 深度分析过程中出错: {e}")
                return False
                
            finally:
                await browser.close()
    
    async def analyze_javascript_fields(self, page):
        """分析JavaScript代码中的字段定义"""
        print("📜 分析JavaScript字段...")
        
        js_fields = []
        
        try:
            # 获取页面中的所有script标签内容
            scripts = await page.evaluate('''
                () => {
                    const scripts = [];
                    document.querySelectorAll('script').forEach(script => {
                        if (script.textContent) {
                            scripts.push(script.textContent);
                        }
                    });
                    return scripts;
                }
            ''')
            
            # 分析JavaScript代码中的字段定义
            field_patterns = [
                r'["\'](\w+)["\']:\s*["\']([^"\']*项目[^"\']*)["\']',  # 项目相关
                r'["\'](\w+)["\']:\s*["\']([^"\']*需求[^"\']*)["\']',  # 需求相关
                r'["\'](\w+)["\']:\s*["\']([^"\']*甄选[^"\']*)["\']',  # 甄选相关
                r'["\'](\w+)["\']:\s*["\']([^"\']*状态[^"\']*)["\']',  # 状态相关
                r'["\'](\w+)["\']:\s*["\']([^"\']*编码[^"\']*)["\']',  # 编码相关
                r'["\'](\w+)["\']:\s*["\']([^"\']*时间[^"\']*)["\']',  # 时间相关
                r'["\'](\w+)["\']:\s*["\']([^"\']*地市[^"\']*)["\']',  # 地市相关
                r'["\'](\w+)["\']:\s*["\']([^"\']*数量[^"\']*)["\']',  # 数量相关
            ]
            
            for script in scripts:
                for pattern in field_patterns:
                    matches = re.findall(pattern, script)
                    for match in matches:
                        if len(match) == 2:
                            js_fields.append({
                                "english_key": match[0],
                                "chinese_value": match[1],
                                "pattern": pattern
                            })
            
            # 查找Vue组件的props和data定义
            vue_patterns = [
                r'props:\s*\{([^}]+)\}',
                r'data\s*\(\)\s*\{[^}]*return\s*\{([^}]+)\}',
                r'columns:\s*\[([^\]]+)\]'
            ]
            
            for script in scripts:
                for pattern in vue_patterns:
                    matches = re.findall(pattern, script, re.DOTALL)
                    for match in matches:
                        # 进一步解析匹配的内容
                        prop_matches = re.findall(r'["\']?(\w+)["\']?\s*:', match)
                        for prop in prop_matches:
                            if len(prop) > 2:  # 过滤掉太短的属性名
                                js_fields.append({
                                    "english_key": prop,
                                    "chinese_value": "",
                                    "pattern": "vue_component"
                                })
        
        except Exception as e:
            print(f"    ⚠️ 分析JavaScript时出错: {e}")
        
        return js_fields
    
    async def analyze_vue_data(self, page):
        """分析Vue组件的数据"""
        print("🔧 分析Vue组件数据...")
        
        vue_fields = []
        
        try:
            # 尝试获取Vue实例的数据
            vue_data = await page.evaluate('''
                () => {
                    const vueData = [];
                    
                    // 查找Vue实例
                    if (window.Vue && window.Vue.version) {
                        vueData.push({type: 'vue_version', data: window.Vue.version});
                    }
                    
                    // 查找页面中的Vue组件实例
                    const elements = document.querySelectorAll('[data-v-*], .el-*');
                    elements.forEach((el, index) => {
                        if (el.__vue__) {
                            const vueInstance = el.__vue__;
                            if (vueInstance.$data) {
                                vueData.push({
                                    type: 'vue_data',
                                    index: index,
                                    data: Object.keys(vueInstance.$data)
                                });
                            }
                            if (vueInstance.$props) {
                                vueData.push({
                                    type: 'vue_props',
                                    index: index,
                                    data: Object.keys(vueInstance.$props)
                                });
                            }
                        }
                    });
                    
                    return vueData;
                }
            ''')
            
            vue_fields = vue_data
        
        except Exception as e:
            print(f"    ⚠️ 分析Vue数据时出错: {e}")
        
        return vue_fields
    
    async def analyze_network_requests(self, page, network_requests):
        """分析网络请求中的字段"""
        print("🌐 分析网络请求字段...")
        
        api_fields = []
        
        try:
            # 触发一些操作来产生网络请求
            print("    触发查询操作...")
            
            # 尝试点击查询按钮
            query_button = await page.query_selector('button:has-text("查询")')
            if query_button:
                await query_button.click()
                await asyncio.sleep(3)
            
            # 尝试输入搜索条件
            project_name_input = await page.query_selector('input[placeholder="项目名称"]')
            if project_name_input:
                await project_name_input.fill("测试")
                await asyncio.sleep(1)
                if query_button:
                    await query_button.click()
                    await asyncio.sleep(3)
            
            # 分析收集到的网络请求
            for request in network_requests:
                if isinstance(request, dict) and 'url' in request:
                    url = request['url']
                    
                    # 分析API URL中的参数
                    if 'selectDemand' in url or 'api' in url:
                        api_fields.append({
                            "type": "api_url",
                            "url": url,
                            "method": request.get('method', ''),
                            "post_data": request.get('post_data', '')
                        })
                        
                        # 解析URL参数
                        if '?' in url:
                            params = url.split('?')[1]
                            param_pairs = params.split('&')
                            for pair in param_pairs:
                                if '=' in pair:
                                    key, value = pair.split('=', 1)
                                    api_fields.append({
                                        "type": "url_param",
                                        "english_key": key,
                                        "value": value
                                    })
                        
                        # 解析POST数据
                        post_data = request.get('post_data', '')
                        if post_data:
                            try:
                                # 尝试解析JSON
                                if post_data.startswith('{'):
                                    json_data = json.loads(post_data)
                                    for key, value in json_data.items():
                                        api_fields.append({
                                            "type": "post_json",
                                            "english_key": key,
                                            "value": str(value)
                                        })
                                # 尝试解析表单数据
                                elif '=' in post_data:
                                    pairs = post_data.split('&')
                                    for pair in pairs:
                                        if '=' in pair:
                                            key, value = pair.split('=', 1)
                                            api_fields.append({
                                                "type": "post_form",
                                                "english_key": key,
                                                "value": value
                                            })
                            except:
                                pass
        
        except Exception as e:
            print(f"    ⚠️ 分析网络请求时出错: {e}")
        
        return api_fields
    
    async def analyze_source_code(self, page):
        """分析页面源代码"""
        print("📄 分析页面源代码...")
        
        source_fields = []
        
        try:
            # 获取页面HTML源代码
            html_content = await page.content()
            
            # 查找可能的字段映射模式
            patterns = [
                r'v-model=["\']([^"\']+)["\']',  # Vue v-model
                r':prop=["\']([^"\']+)["\']',    # Vue props
                r'ref=["\']([^"\']+)["\']',      # Vue refs
                r'key=["\']([^"\']+)["\']',      # Vue keys
                r'@(\w+)=',                     # Vue events
                r'data-(\w+)=["\']([^"\']*)["\']'  # data attributes
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, html_content)
                for match in matches:
                    if isinstance(match, tuple):
                        source_fields.append({
                            "type": "html_attribute",
                            "pattern": pattern,
                            "match": match
                        })
                    else:
                        source_fields.append({
                            "type": "html_attribute",
                            "pattern": pattern,
                            "match": match
                        })
        
        except Exception as e:
            print(f"    ⚠️ 分析源代码时出错: {e}")
        
        return source_fields
    
    def compute_field_mappings(self, analysis_result):
        """基于分析结果推断字段映射"""
        print("🧮 推断字段映射...")
        
        computed_mappings = []
        
        # 定义中文字段列表
        chinese_fields = [
            "序号", "项目名称", "项目编码", "需求名称", "甄选方案数量",
            "需求编码", "甄选类别", "归属地市", "创建时间", "甄选需求状态"
        ]
        
        # 从JavaScript字段中查找映射
        for js_field in analysis_result.get("javascript_fields", []):
            english_key = js_field.get("english_key", "")
            chinese_value = js_field.get("chinese_value", "")
            
            # 匹配中文字段
            for chinese_field in chinese_fields:
                if chinese_field in chinese_value:
                    computed_mappings.append({
                        "chinese_name": chinese_field,
                        "english_name": english_key,
                        "confidence": "high",
                        "source": "javascript_analysis"
                    })
        
        # 从API字段中推断映射
        for api_field in analysis_result.get("network_api_fields", []):
            if api_field.get("type") in ["post_json", "post_form", "url_param"]:
                english_key = api_field.get("english_key", "")
                
                # 基于常见的英文字段名推断
                field_mappings = {
                    "projectName": "项目名称",
                    "projectCode": "项目编码",
                    "demandName": "需求名称",
                    "demandCode": "需求编码",
                    "selectType": "甄选类别",
                    "city": "归属地市",
                    "status": "甄选需求状态",
                    "createTime": "创建时间",
                    "count": "甄选方案数量",
                    "id": "序号"
                }
                
                if english_key in field_mappings:
                    computed_mappings.append({
                        "chinese_name": field_mappings[english_key],
                        "english_name": english_key,
                        "confidence": "medium",
                        "source": "api_analysis"
                    })
        
        # 去重
        unique_mappings = []
        seen = set()
        for mapping in computed_mappings:
            key = (mapping["chinese_name"], mapping["english_name"])
            if key not in seen:
                seen.add(key)
                unique_mappings.append(mapping)
        
        return unique_mappings
    
    async def save_deep_analysis(self, analysis_result):
        """保存深度分析结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = f"{self.analysis_dir}/deep_analysis_{timestamp}.json"
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, indent=2, ensure_ascii=False)
        
        print(f"💾 深度分析结果已保存: {result_file}")
    
    async def generate_final_mapping_doc(self, analysis_result):
        """生成最终的字段映射文档"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        doc_file = f"{self.analysis_dir}/最终字段映射_{timestamp}.md"
        
        with open(doc_file, 'w', encoding='utf-8') as f:
            f.write("# 甄选需求管理页面最终字段映射分析\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 推断的字段映射
            f.write("## 🎯 推断的字段映射\n\n")
            f.write("| 中文字段名 | 推断的英文名 | 置信度 | 来源 |\n")
            f.write("|-----------|-------------|--------|------|\n")
            
            computed_fields = analysis_result.get("computed_fields", [])
            if computed_fields:
                for field in computed_fields:
                    f.write(f"| {field['chinese_name']} | {field['english_name']} | {field['confidence']} | {field['source']} |\n")
            else:
                f.write("| - | 未发现明确的英文字段映射 | - | - |\n")
            
            f.write("\n")
            
            # JavaScript分析结果
            f.write("## 📜 JavaScript字段分析\n\n")
            js_fields = analysis_result.get("javascript_fields", [])
            if js_fields:
                f.write("| 英文键名 | 中文值 | 匹配模式 |\n")
                f.write("|---------|--------|----------|\n")
                for field in js_fields[:20]:  # 只显示前20个
                    f.write(f"| {field['english_key']} | {field['chinese_value']} | {field['pattern']} |\n")
            else:
                f.write("未发现JavaScript中的字段定义\n")
            
            f.write("\n")
            
            # Vue组件分析
            f.write("## 🔧 Vue组件分析\n\n")
            vue_fields = analysis_result.get("vue_data_fields", [])
            if vue_fields:
                for field in vue_fields:
                    f.write(f"- **{field['type']}**: {field.get('data', 'N/A')}\n")
            else:
                f.write("未发现Vue组件数据\n")
            
            f.write("\n")
            
            # API分析结果
            f.write("## 🌐 API字段分析\n\n")
            api_fields = analysis_result.get("network_api_fields", [])
            if api_fields:
                f.write("| 类型 | 英文键名 | 值 | URL |\n")
                f.write("|------|---------|----|----- |\n")
                for field in api_fields[:20]:  # 只显示前20个
                    english_key = field.get('english_key', '')
                    value = field.get('value', '')
                    url = field.get('url', '')[:50] + '...' if len(field.get('url', '')) > 50 else field.get('url', '')
                    f.write(f"| {field['type']} | {english_key} | {value} | {url} |\n")
            else:
                f.write("未发现API字段信息\n")
            
            f.write("\n")
            
            # 结论
            f.write("## 📋 分析结论\n\n")
            
            if computed_fields:
                f.write("✅ **发现字段映射**: 通过深度分析发现了部分字段的英文名称\n\n")
                f.write("**高置信度映射**:\n")
                for field in computed_fields:
                    if field['confidence'] == 'high':
                        f.write(f"- {field['chinese_name']} → {field['english_name']}\n")
                
                f.write("\n**中等置信度映射**:\n")
                for field in computed_fields:
                    if field['confidence'] == 'medium':
                        f.write(f"- {field['chinese_name']} → {field['english_name']}\n")
            else:
                f.write("⚠️ **未发现明确映射**: 该页面可能使用了以下技术特征:\n\n")
                f.write("- 使用Element UI框架，字段名直接使用中文\n")
                f.write("- Vue.js组件化开发，字段映射可能在组件内部\n")
                f.write("- 可能使用了编译后的代码，原始字段名被混淆\n")
                f.write("- 后端API可能直接接受中文字段名\n\n")
                f.write("**建议**:\n")
                f.write("- 查看网络请求的实际参数名\n")
                f.write("- 检查Vue DevTools中的组件数据\n")
                f.write("- 联系开发团队获取API文档\n")
        
        print(f"📚 最终字段映射文档已生成: {doc_file}")


async def main():
    """主函数"""
    analyzer = DeepFieldAnalyzer()
    success = await analyzer.deep_analyze_fields()
    
    if success:
        print("🎉 深度字段分析完成！")
    else:
        print("❌ 分析失败")


if __name__ == "__main__":
    print("🔬 深度字段分析器")
    print("📊 通过JavaScript、Vue组件、网络请求等方式深度挖掘字段映射")
    print("⚠️ 只提取真实存在的英文字段名，不进行翻译")
    print("=" * 70)
    asyncio.run(main())
