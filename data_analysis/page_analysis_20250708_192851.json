{"page_info": {"title": "框架-主页面", "url": "https://dict.gmcc.net:30722/ptn/main/selectDemand", "timestamp": "2025-07-08T19:28:47.880585"}, "tables": [{"table_index": 1, "headers": ["序号", "项目名称", "项目编码", "需求名称", "甄选方案数量", "需求编码", "甄选类别", "归属地市", "创建时间", "甄选需求状态", "", ""], "sample_data": [["序号", "项目名称", "项目编码", "需求名称", "甄选方案数量", "需求编码", "甄选类别", "归属地市", "创建时间", "甄选需求状态", "", ""]], "row_count": 1, "column_count": 12}, {"table_index": 2, "headers": ["1", "中山市坤鹏电子科技有限公司信息化建设项目", "CMGDZSICT20250707037", "中山移动某智慧园区项目", "0", "1942422593200898048", "项目甄选", "中山", "2025-07-08 11:16:49", "审核通过", ""], "sample_data": [["1", "中山市坤鹏电子科技有限公司信息化建设项目", "CMGDZSICT20250707037", "中山移动某智慧园区项目", "0", "1942422593200898048", "项目甄选", "中山", "2025-07-08 11:16:49", "审核通过", ""], ["2", "中山市火炬开发区第一幼儿园2025年智慧安防进校园门禁系统维护项目", "CMGDZSICT20250514031", "中山移动校园门禁项目", "1", "1942116921179553792", "项目甄选", "中山", "2025-07-07 15:02:11", "审核通过(已制定方案)", ""], ["3", "横栏消防支队LCD+AP+报警项目", "CMGDZSICT20250603017", "中山移动横栏消防救援支队LCD+报警项目", "1", "1942069083955445760", "项目甄选", "中山", "2025-07-07 11:52:06", "审核通过(已制定方案)", ""]], "row_count": 10, "column_count": 11}, {"table_index": 3, "headers": ["", "", "", "", "", "", "", "", "", "", "操作"], "sample_data": [["", "", "", "", "", "", "", "", "", "", "操作"]], "row_count": 1, "column_count": 11}, {"table_index": 4, "headers": ["", "", "", "", "", "", "", "", "", "", "查看详情"], "sample_data": [["", "", "", "", "", "", "", "", "", "", "查看详情"], ["", "", "", "", "", "", "", "", "", "", "查看详情"], ["", "", "", "", "", "", "", "", "", "", "查看详情"]], "row_count": 10, "column_count": 11}], "forms": [{"form_index": 1, "action": "", "method": "", "inputs": [{"type": "text", "name": "", "placeholder": "项目名称", "value": ""}, {"type": "text", "name": "", "placeholder": "项目编码", "value": ""}, {"type": "text", "name": "", "placeholder": "需求名称", "value": ""}, {"type": "text", "name": "", "placeholder": "请选择", "value": ""}, {"type": "text", "name": "", "placeholder": "请选择", "value": ""}, {"type": "text", "name": "", "placeholder": "需求编码", "value": ""}, {"type": "text", "name": "", "placeholder": "", "value": ""}]}], "buttons": [{"index": 1, "text": "重置", "type": "button", "class": "el-button form-btn el-button--default"}, {"index": 2, "text": "查询", "type": "button", "class": "el-button form-btn el-button--primary"}, {"index": 3, "text": "发起项目甄选", "type": "button", "class": "el-button mgl-15 el-button--primary"}, {"index": 4, "text": "发起产品甄选", "type": "button", "class": "el-button mgl-15 el-button--primary"}, {"index": 5, "text": "发起算力项目甄选", "type": "button", "class": "el-button mgl-15 el-button--primary"}, {"index": 6, "text": "发起中移集成项目甄选补录", "type": "button", "class": "el-button mgl-15 el-button--primary"}, {"index": 7, "text": "甄选附件变更", "type": "button", "class": "el-button mgl-15 el-button--primary"}, {"index": 8, "text": "", "type": "button", "class": "btn-prev"}, {"index": 9, "text": "", "type": "button", "class": "btn-next"}, {"index": 10, "text": "", "type": "button", "class": "el-dialog__headerbtn"}, {"index": 11, "text": "", "type": "button", "class": "el-dialog__headerbtn"}, {"index": 12, "text": "", "type": "button", "class": "el-dialog__headerbtn"}], "inputs": [{"index": 1, "type": "text", "name": "", "placeholder": "项目名称", "id": "", "class": "el-input__inner"}, {"index": 2, "type": "text", "name": "", "placeholder": "项目编码", "id": "", "class": "el-input__inner"}, {"index": 3, "type": "text", "name": "", "placeholder": "需求名称", "id": "", "class": "el-input__inner"}, {"index": 4, "type": "text", "name": "", "placeholder": "请选择", "id": "", "class": "el-input__inner"}, {"index": 5, "type": "text", "name": "", "placeholder": "请选择", "id": "", "class": "el-input__inner"}, {"index": 6, "type": "text", "name": "", "placeholder": "需求编码", "id": "", "class": "el-input__inner"}, {"index": 7, "type": "text", "name": "", "placeholder": "", "id": "", "class": "el-input__inner"}, {"index": 8, "type": "text", "name": "", "placeholder": "请选择", "id": "", "class": "el-input__inner"}, {"index": 9, "type": "number", "name": "", "placeholder": "", "id": "", "class": "el-input__inner"}], "data_dictionary": {"table_1": {"序号": {"field_name": "序号", "data_type": "string", "sample_values": [], "unique_values": [], "max_length": 0, "is_nullable": false}, "项目名称": {"field_name": "项目名称", "data_type": "string", "sample_values": [], "unique_values": [], "max_length": 0, "is_nullable": false}, "项目编码": {"field_name": "项目编码", "data_type": "string", "sample_values": [], "unique_values": [], "max_length": 0, "is_nullable": false}, "需求名称": {"field_name": "需求名称", "data_type": "string", "sample_values": [], "unique_values": [], "max_length": 0, "is_nullable": false}, "甄选方案数量": {"field_name": "甄选方案数量", "data_type": "string", "sample_values": [], "unique_values": [], "max_length": 0, "is_nullable": false}, "需求编码": {"field_name": "需求编码", "data_type": "string", "sample_values": [], "unique_values": [], "max_length": 0, "is_nullable": false}, "甄选类别": {"field_name": "甄选类别", "data_type": "string", "sample_values": [], "unique_values": [], "max_length": 0, "is_nullable": false}, "归属地市": {"field_name": "归属地市", "data_type": "string", "sample_values": [], "unique_values": [], "max_length": 0, "is_nullable": false}, "创建时间": {"field_name": "创建时间", "data_type": "string", "sample_values": [], "unique_values": [], "max_length": 0, "is_nullable": false}, "甄选需求状态": {"field_name": "甄选需求状态", "data_type": "string", "sample_values": [], "unique_values": [], "max_length": 0, "is_nullable": false}}, "table_2": {"1": {"field_name": "1", "data_type": "numeric", "sample_values": ["2", "3", "4", "5", "6", "7", "8", "9", "10"], "unique_values": ["7", "8", "5", "6", "3", "10", "4", "2", "9"], "max_length": 2, "is_nullable": false}, "中山市坤鹏电子科技有限公司信息化建设项目": {"field_name": "中山市坤鹏电子科技有限公司信息化建设项目", "data_type": "string", "sample_values": ["中山市火炬开发区第一幼儿园2025年智慧安防进校园门禁系统维护项目", "横栏消防支队LCD+AP+报警项目", "社会保险业务智能稽核复审项目", "嘉钦工业园区智能化项目", "绩东一德原北路三线下地整治工程", "中山市南朗医院信息应急系统建设服务项目", "中山市第一职业技术学校智慧课程建设项目", "中山市第一中学教育装备、医务室设备等物资采购项目", "中山市第二人民医院智能发光药筐系统采购项目"], "unique_values": ["嘉钦工业园区智能化项目", "中山市火炬开发区第一幼儿园2025年智慧安防进校园门禁系统维护项目", "横栏消防支队LCD+AP+报警项目", "社会保险业务智能稽核复审项目", "中山市第一中学教育装备、医务室设备等物资采购项目", "绩东一德原北路三线下地整治工程", "中山市第一职业技术学校智慧课程建设项目", "中山市第二人民医院智能发光药筐系统采购项目", "中山市南朗医院信息应急系统建设服务项目"], "max_length": 33, "is_nullable": false}, "CMGDZSICT20250707037": {"field_name": "CMGDZSICT20250707037", "data_type": "string", "sample_values": ["CMGDZSICT20250514031", "CMGDZSICT20250603017", "CMGDZSICT20250707001", "CMGDZSICT20250616063", "CMGDZSICT20250604017", "CMGDZSICT20250626070", "CMGDZSICT20250703027", "CMGDZSICT20250625079", "CMGDZSICT20250619024"], "unique_values": ["CMGDZSICT20250626070", "CMGDZSICT20250703027", "CMGDZSICT20250514031", "CMGDZSICT20250616063", "CMGDZSICT20250625079", "CMGDZSICT20250604017", "CMGDZSICT20250619024", "CMGDZSICT20250707001", "CMGDZSICT20250603017"], "max_length": 20, "is_nullable": false}, "中山移动某智慧园区项目": {"field_name": "中山移动某智慧园区项目", "data_type": "string", "sample_values": ["中山移动校园门禁项目", "中山移动横栏消防救援支队LCD+报警项目", "中山移动业务智能稽核复审项目", "中山移动嘉钦工业园区智能化项目", "中山移动小榄镇某村信息化项目（第二次）", "中山移动C镇区信息应急系统建设服务项目", "中山移动中山市第一职业技术学校智慧课程建设项目", "中山市第一中学教育装备、医务室设备等物资采购项目", "2025智能发光药筐系统项目（第二次）"], "unique_values": ["中山移动中山市第一职业技术学校智慧课程建设项目", "中山移动业务智能稽核复审项目", "中山移动C镇区信息应急系统建设服务项目", "中山移动横栏消防救援支队LCD+报警项目", "中山移动校园门禁项目", "中山移动小榄镇某村信息化项目（第二次）", "中山市第一中学教育装备、医务室设备等物资采购项目", "2025智能发光药筐系统项目（第二次）", "中山移动嘉钦工业园区智能化项目"], "max_length": 24, "is_nullable": false}, "0": {"field_name": "0", "data_type": "numeric", "sample_values": ["1", "1", "1", "1", "1", "1", "1", "1", "1"], "unique_values": ["1"], "max_length": 1, "is_nullable": false}, "1942422593200898048": {"field_name": "1942422593200898048", "data_type": "numeric", "sample_values": ["1942116921179553792", "1942069083955445760", "1942023416348327936", "1940961734821855232", "1940953867796529152", "1940705441884323840", "1940668636770254848", "1940620473380290560", "1940221567911772160"], "unique_values": ["1940705441884323840", "1940668636770254848", "1940961734821855232", "1940221567911772160", "1942116921179553792", "1940953867796529152", "1940620473380290560", "1942023416348327936", "1942069083955445760"], "max_length": 19, "is_nullable": false}, "项目甄选": {"field_name": "项目甄选", "data_type": "string", "sample_values": ["项目甄选", "项目甄选", "项目甄选", "项目甄选", "项目甄选", "项目甄选", "项目甄选", "项目甄选", "项目甄选"], "unique_values": ["项目甄选"], "max_length": 4, "is_nullable": false}, "中山": {"field_name": "中山", "data_type": "string", "sample_values": ["中山", "中山", "中山", "中山", "中山", "中山", "中山", "中山", "中山"], "unique_values": ["中山"], "max_length": 2, "is_nullable": false}, "2025-07-08 11:16:49": {"field_name": "2025-07-08 11:16:49", "data_type": "datetime", "sample_values": ["2025-07-07 15:02:11", "2025-07-07 11:52:06", "2025-07-07 08:50:38", "2025-07-04 10:31:53", "2025-07-04 10:00:37", "2025-07-03 17:33:28", "2025-07-03 15:07:13", "2025-07-03 11:55:50", "2025-07-02 09:30:44"], "unique_values": ["2025-07-04 10:00:37", "2025-07-03 17:33:28", "2025-07-03 15:07:13", "2025-07-02 09:30:44", "2025-07-03 11:55:50", "2025-07-07 08:50:38", "2025-07-07 11:52:06", "2025-07-07 15:02:11", "2025-07-04 10:31:53"], "max_length": 19, "is_nullable": false}, "审核通过": {"field_name": "审核通过", "data_type": "string", "sample_values": ["审核通过(已制定方案)", "审核通过(已制定方案)", "审核通过(已制定方案)", "审核通过(已制定方案)", "审核通过(已制定方案)", "审核通过(已制定方案)", "审核通过(已制定方案)", "审核通过(已制定方案)", "审核通过(已制定方案)"], "unique_values": ["审核通过(已制定方案)"], "max_length": 11, "is_nullable": false}}, "table_3": {"操作": {"field_name": "操作", "data_type": "string", "sample_values": [], "unique_values": [], "max_length": 0, "is_nullable": false}}, "table_4": {"查看详情": {"field_name": "查看详情", "data_type": "string", "sample_values": ["查看详情", "查看详情", "查看详情", "查看详情", "查看详情", "查看详情", "查看详情", "查看详情", "查看详情"], "unique_values": ["查看详情"], "max_length": 4, "is_nullable": false}}}}