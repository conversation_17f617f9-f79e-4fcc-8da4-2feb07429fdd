#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
执行创建合作伙伴视图的SQL脚本
"""

import sys
import os
from database.db_config import DatabaseManager, ZHENXUAN_DB_CONFIG

def execute_create_partner_view():
    """执行创建合作伙伴视图的SQL"""
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    if not db_manager.connect():
        print("❌ 数据库连接失败")
        return False
    
    try:
        # 读取SQL文件
        with open('create_view_partner.sql', 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        print("📋 开始执行创建合作伙伴视图SQL...")
        
        # 分割SQL语句（按分号分割）
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        with db_manager.get_cursor() as cursor:
            for i, sql in enumerate(sql_statements, 1):
                if sql:
                    print(f"🔄 执行第 {i} 条SQL语句...")
                    print(f"   {sql[:100]}...")
                    cursor.execute(sql)
                    print(f"✅ 第 {i} 条SQL执行成功")
            
            # 提交事务
            db_manager.connection.commit()
            print("✅ 所有SQL语句执行完成，事务已提交")
        
        # 验证视图是否创建成功
        print("\n📋 验证视图创建结果...")
        with db_manager.get_cursor() as cursor:
            # 检查视图是否存在
            cursor.execute("SHOW TABLES LIKE 'v_zhenxuan_queryselectapplydetai_partner'")
            result = cursor.fetchone()
            
            if result:
                print("✅ 视图 v_zhenxuan_queryselectapplydetai_partner 创建成功")
                
                # 查看视图结构
                print("\n📋 视图字段结构:")
                cursor.execute("DESCRIBE v_zhenxuan_queryselectapplydetai_partner")
                columns = cursor.fetchall()
                for col in columns:
                    print(f"  {col['Field']} - {col['Type']} - {col['Null']}")
                
                # 查询几条示例数据
                print(f"\n📋 视图数据示例（前5条）:")
                cursor.execute("""
                SELECT 
                    select_apply_id, project_no, selectApplyResultId, selectApplyId,
                    partnerMsgId, partnerName, decideResultValue, bidMoneyValue,
                    contactsName, contactsPhone
                FROM v_zhenxuan_queryselectapplydetai_partner 
                LIMIT 5
                """)
                
                rows = cursor.fetchall()
                for i, row in enumerate(rows, 1):
                    print(f"  {i}. 申请ID: {row['select_apply_id']}")
                    print(f"     项目编号: {row['project_no']}")
                    print(f"     结果ID: {row['selectApplyResultId']}")
                    print(f"     合作伙伴ID: {row['partnerMsgId']}")
                    print(f"     合作伙伴名称: {row['partnerName']}")
                    print(f"     决策结果: {row['decideResultValue']}")
                    print(f"     投标金额: {row['bidMoneyValue']}")
                    print(f"     联系人: {row['contactsName']}")
                    print(f"     联系电话: {row['contactsPhone']}")
                    print()
                
                # 统计视图记录数
                cursor.execute("SELECT COUNT(*) as total FROM v_zhenxuan_queryselectapplydetai_partner")
                count_result = cursor.fetchone()
                print(f"📊 视图总记录数: {count_result['total']}")
                
                # 统计中选合作伙伴分布
                print(f"\n📊 中选合作伙伴统计:")
                cursor.execute("""
                SELECT 
                    COUNT(DISTINCT select_apply_id) as unique_projects,
                    COUNT(*) as total_selected_partners,
                    COUNT(DISTINCT partnerMsgId) as unique_partners
                FROM v_zhenxuan_queryselectapplydetai_partner
                """)
                
                stats = cursor.fetchone()
                print(f"  涉及项目数: {stats['unique_projects']}")
                print(f"  中选记录数: {stats['total_selected_partners']}")
                print(f"  唯一合作伙伴数: {stats['unique_partners']}")
                
                # 查看投标金额分布
                print(f"\n📊 投标金额分布:")
                cursor.execute("""
                SELECT 
                    COUNT(CASE WHEN bidMoneyValue IS NOT NULL AND bidMoneyValue != '' THEN 1 END) as has_bid_amount,
                    COUNT(CASE WHEN bidMoneyValue IS NULL OR bidMoneyValue = '' THEN 1 END) as no_bid_amount
                FROM v_zhenxuan_queryselectapplydetai_partner
                """)
                
                bid_stats = cursor.fetchone()
                print(f"  有投标金额: {bid_stats['has_bid_amount']}")
                print(f"  无投标金额: {bid_stats['no_bid_amount']}")
                
            else:
                print("❌ 视图创建失败")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    success = execute_create_partner_view()
    if success:
        print("\n🎉 合作伙伴视图创建完成！")
    else:
        print("\n❌ 合作伙伴视图创建失败！")
