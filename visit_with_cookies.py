"""
使用保存的cookies访问甄选需求管理页面
基于Playwright框架实现
"""

import asyncio
import json
import os
from datetime import datetime
from playwright.async_api import async_playwright


class CookieVisitor:
    def __init__(self):
        self.target_url = "https://dict.gmcc.net:30722/ptn/main/selectDemand"
        self.cookie_dir = "cookies"
        self.cookies = None
        
    def load_latest_cookies(self):
        """加载最新的cookies"""
        try:
            # 优先使用最新的cookies文件
            latest_cookie_file = os.path.join(self.cookie_dir, "cookies_dict_zhenxuan.json")
            
            if os.path.exists(latest_cookie_file):
                print(f"📂 加载最新cookies文件: {latest_cookie_file}")
                with open(latest_cookie_file, 'r', encoding='utf-8') as f:
                    self.cookies = json.load(f)
                print(f"✅ 成功加载 {len(self.cookies)} 个cookie")
                return True
            else:
                # 如果没有最新文件，查找最新的时间戳文件
                cookie_files = []
                if os.path.exists(self.cookie_dir):
                    for filename in os.listdir(self.cookie_dir):
                        if filename.startswith("cookies_zhenxuan_") and filename.endswith(".json"):
                            cookie_files.append(filename)
                
                if cookie_files:
                    # 按文件名排序，获取最新的
                    cookie_files.sort(reverse=True)
                    latest_file = os.path.join(self.cookie_dir, cookie_files[0])
                    print(f"📂 加载cookies文件: {latest_file}")
                    
                    with open(latest_file, 'r', encoding='utf-8') as f:
                        self.cookies = json.load(f)
                    print(f"✅ 成功加载 {len(self.cookies)} 个cookie")
                    return True
                else:
                    print("❌ 未找到任何cookies文件")
                    return False
                    
        except Exception as e:
            print(f"❌ 加载cookies失败: {e}")
            return False
    
    async def visit_with_cookies(self):
        """使用cookies访问目标页面"""
        if not self.load_latest_cookies():
            print("❌ 无法加载cookies，请先运行登录脚本")
            return False
            
        async with async_playwright() as p:
            # 启动浏览器
            browser = await p.chromium.launch(
                headless=False,  # 设置为True可以无头模式运行
                slow_mo=500,
                args=['--disable-blink-features=AutomationControlled']
            )
            
            # 创建浏览器上下文
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                ignore_https_errors=True
            )
            
            try:
                # 添加cookies到上下文
                print("🍪 正在添加cookies到浏览器上下文...")
                await context.add_cookies(self.cookies)
                print("✅ Cookies添加成功")
                
                # 创建新页面
                page = await context.new_page()
                
                # 访问目标页面
                print(f"🌐 正在访问目标页面: {self.target_url}")
                await page.goto(self.target_url, wait_until='networkidle')
                
                # 等待页面加载
                await asyncio.sleep(3)
                
                # 检查是否成功访问
                current_url = page.url
                print(f"📍 当前URL: {current_url}")
                
                if "selectDemand" in current_url:
                    print("✅ 成功访问甄选需求管理页面！")
                    
                    # 检查页面内容
                    await self.check_page_content(page)
                    
                    # 保持页面打开以便查看
                    print("⏰ 页面将保持打开30秒，您可以查看内容...")
                    await asyncio.sleep(30)
                    
                    return True
                else:
                    print("⚠️ 可能需要重新登录，当前页面不是目标页面")
                    
                    # 检查是否被重定向到登录页面
                    if "login" in current_url.lower():
                        print("❌ 被重定向到登录页面，cookies可能已过期")
                    
                    await asyncio.sleep(10)
                    return False
                    
            except Exception as e:
                print(f"❌ 访问页面时出现错误: {e}")
                await asyncio.sleep(10)
                return False
                
            finally:
                await browser.close()
    
    async def check_page_content(self, page):
        """检查页面内容"""
        try:
            print("🔍 检查页面内容...")
            
            # 获取页面标题
            title = await page.title()
            print(f"📄 页面标题: {title}")
            
            # 检查是否有表格或列表内容
            tables = await page.query_selector_all('table')
            if tables:
                print(f"📊 发现 {len(tables)} 个表格")
            
            # 检查是否有按钮
            buttons = await page.query_selector_all('button')
            if buttons:
                print(f"🔘 发现 {len(buttons)} 个按钮")
            
            # 检查是否有输入框
            inputs = await page.query_selector_all('input')
            if inputs:
                print(f"📝 发现 {len(inputs)} 个输入框")
            
            # 尝试获取页面主要内容区域的文本
            main_content = await page.query_selector('main, .main, .content, .container')
            if main_content:
                content_text = await main_content.inner_text()
                if content_text:
                    # 只显示前200个字符
                    preview = content_text[:200] + "..." if len(content_text) > 200 else content_text
                    print(f"📋 页面内容预览: {preview}")
            
        except Exception as e:
            print(f"⚠️ 检查页面内容时出错: {e}")


async def main():
    """主函数"""
    visitor = CookieVisitor()
    success = await visitor.visit_with_cookies()
    
    if success:
        print("🎉 访问成功完成！")
    else:
        print("❌ 访问失败，可能需要重新登录获取新的cookies")


if __name__ == "__main__":
    print("🚀 启动Cookie访问程序...")
    print("🍪 将使用保存的cookies访问甄选需求管理页面")
    print(f"🎯 目标URL: https://dict.gmcc.net:30722/ptn/main/selectDemand")
    print("=" * 60)
    asyncio.run(main())
