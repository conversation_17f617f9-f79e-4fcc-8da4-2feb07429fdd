#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的认证系统
验证所有脚本是否能正常加载认证信息
"""

import sys
import os
import importlib.util
from pathlib import Path

def test_script_auth(script_path: str) -> bool:
    """
    测试单个脚本的认证加载
    
    Args:
        script_path: 脚本文件路径
        
    Returns:
        bool: 测试是否成功
    """
    try:
        # 动态导入脚本模块
        spec = importlib.util.spec_from_file_location("test_module", script_path)
        module = importlib.util.module_from_spec(spec)
        
        # 临时重定向stdout来捕获日志输出
        import io
        from contextlib import redirect_stdout, redirect_stderr
        
        stdout_capture = io.StringIO()
        stderr_capture = io.StringIO()
        
        with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
            spec.loader.exec_module(module)
            
            # 查找主要的类（通常以Fetcher结尾）
            fetcher_classes = [
                attr for attr in dir(module) 
                if attr.endswith('Fetcher') and not attr.startswith('_')
            ]
            
            if fetcher_classes:
                # 尝试实例化第一个找到的Fetcher类
                fetcher_class = getattr(module, fetcher_classes[0])
                fetcher = fetcher_class()
                
                # 检查是否有session属性且已配置认证
                if hasattr(fetcher, 'session'):
                    session = fetcher.session
                    headers = session.headers
                    
                    # 检查关键的认证headers
                    has_auth = (
                        'Authorization' in headers or 
                        'Cookie' in headers or
                        len(session.cookies) > 0
                    )
                    
                    if has_auth:
                        print(f"✅ {script_path}: 认证信息加载成功")
                        return True
                    else:
                        print(f"⚠️ {script_path}: 未检测到认证信息")
                        return False
                else:
                    print(f"⚠️ {script_path}: 未找到session属性")
                    return False
            else:
                print(f"⚠️ {script_path}: 未找到Fetcher类")
                return False
                
    except Exception as e:
        print(f"❌ {script_path}: 测试失败 - {e}")
        return False

def main():
    """主函数"""
    print("🧪 测试更新后的认证系统")
    print("="*50)
    
    # 需要测试的脚本列表
    scripts_to_test = [
        "scripts/fetch_querySelectProjectList.py",
        "scripts/fetch_zhenxuan_data.py",
        "scripts/fetch_queryLocalAuditTrackHistory.py",
        "scripts/fetch_queryLocalAuditTrackHistory_ksm.py", 
        "scripts/fetch_queryPartnerSelectDetail.py",
        "scripts/fetch_querySelectAuditTrackHistory.py",
        "scripts/fetch_querySelectProjectDetail.py",
        "scripts/fetch_querySelectStage.py",
        "scripts/zhenxuan_queryNoticeHistoryBySelectId.py",
    ]
    
    success_count = 0
    total_count = len(scripts_to_test)
    
    for script_path in scripts_to_test:
        if os.path.exists(script_path):
            if test_script_auth(script_path):
                success_count += 1
        else:
            print(f"⚠️ 文件不存在: {script_path}")
    
    print(f"\n📊 测试完成: {success_count}/{total_count} 个脚本认证正常")
    
    if success_count == total_count:
        print("🎉 所有脚本认证系统更新成功！")
        print("\n🚀 现在可以使用以下功能：")
        print("  - 自动获取最新的Bearer token")
        print("  - 统一的认证信息管理")
        print("  - 支持新旧格式认证文件")
        print("  - 更好的错误处理和日志记录")
    else:
        print("⚠️ 部分脚本认证测试失败，请检查相关脚本")

if __name__ == "__main__":
    main()
