"""
简单创建 zhenxuan_querylocalaudittrackhistory_xqxx 数据表
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from database.db_config import DatabaseManager, ZHENXUAN_DB_CONFIG

def create_table():
    """创建数据表"""
    # 连接数据库
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    if not db_manager.connect():
        print("❌ 数据库连接失败")
        return False
    
    try:
        print("🚀 开始创建数据表...")
        
        # 删除表（如果存在）
        drop_sql = "DROP TABLE IF EXISTS `zhenxuan_querylocalaudittrackhistory_xqxx`"
        with db_manager.get_cursor() as cursor:
            cursor.execute(drop_sql)
        print("✅ 删除旧表（如果存在）")
        
        # 创建表
        create_sql = """
        CREATE TABLE `zhenxuan_querylocalaudittrackhistory_xqxx` (
          `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
          `project_msg_id` VARCHAR(50) NOT NULL COMMENT '项目消息ID - 来源于zhenxuan_querySelectProjectList.projectMsgId，同时作为businessId入参',
          `business_id` VARCHAR(50) NOT NULL COMMENT '业务ID（入参）- 使用projectMsgId的值',
          `work_order_msg_id` VARCHAR(100) DEFAULT NULL COMMENT '工单消息ID（入参）- 固定为null',
          `step_name_filter` VARCHAR(100) DEFAULT NULL COMMENT '步骤名称过滤（入参）- 固定为空字符串',
          `request_params` JSON DEFAULT NULL COMMENT '请求参数JSON',
          `busi_date` DATETIME DEFAULT NULL COMMENT '业务日期 - busiDate',
          `code` VARCHAR(20) DEFAULT NULL COMMENT '响应代码 - code',
          `message` TEXT DEFAULT NULL COMMENT '响应消息 - message',
          `audit_process_track_id` VARCHAR(50) DEFAULT NULL COMMENT '审核流程跟踪ID - auditProcessTrackId',
          `step_name` VARCHAR(100) DEFAULT NULL COMMENT '步骤名称 - stepName',
          `create_time` DATETIME DEFAULT NULL COMMENT '创建时间 - createTime',
          `finish_time` DATETIME DEFAULT NULL COMMENT '完成时间 - finishTime',
          `status` VARCHAR(50) DEFAULT NULL COMMENT '状态 - status',
          `audit_handler` VARCHAR(100) DEFAULT NULL COMMENT '审核处理人 - auditHandler',
          `audit_remark` TEXT DEFAULT NULL COMMENT '审核备注 - auditRemark',
          `raw_data` JSON DEFAULT NULL COMMENT '原始JSON数据',
          `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
          `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
          INDEX `idx_project_msg_id` (`project_msg_id`) COMMENT '项目消息ID索引',
          INDEX `idx_business_id` (`business_id`) COMMENT '业务ID索引',
          INDEX `idx_audit_process_track_id` (`audit_process_track_id`) COMMENT '审核流程跟踪ID索引',
          INDEX `idx_step_name` (`step_name`) COMMENT '步骤名称索引',
          INDEX `idx_create_time` (`create_time`) COMMENT '创建时间索引',
          INDEX `idx_status` (`status`) COMMENT '状态索引',
          INDEX `idx_created_at` (`created_at`) COMMENT '记录创建时间索引',
          INDEX `idx_project_business` (`project_msg_id`, `business_id`) COMMENT '项目ID和业务ID复合索引',
          UNIQUE KEY `uk_audit_track_unique_xqxx` (`project_msg_id`, `business_id`, `audit_process_track_id`) COMMENT '审核跟踪唯一约束（需求信息版本）'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='本地审核跟踪历史数据表（需求信息版本）'
        """
        
        with db_manager.get_cursor() as cursor:
            cursor.execute(create_sql)
        print("✅ 创建表成功")
        
        # 验证表创建
        verify_sql = """
        SELECT TABLE_NAME, TABLE_COMMENT
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = 'zhenxuandb' 
        AND TABLE_NAME = 'zhenxuan_querylocalaudittrackhistory_xqxx'
        """
        
        with db_manager.get_cursor() as cursor:
            cursor.execute(verify_sql)
            result = cursor.fetchone()
        
        if result:
            print(f"✅ 表创建验证成功: {result['TABLE_NAME']} - {result['TABLE_COMMENT']}")
        else:
            print("❌ 表创建验证失败")
            return False
        
        print("🎉 数据表创建完成！")
        return True
        
    except Exception as e:
        print(f"❌ 创建数据表失败: {e}")
        return False
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    create_table()
