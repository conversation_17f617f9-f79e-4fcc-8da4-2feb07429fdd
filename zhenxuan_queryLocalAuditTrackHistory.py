"""
甄选信息-评审小组 本地审核跟踪历史数据获取程序
根据 queryLocalAuditTrackHistory 接口获取审核跟踪历史数据并入库
特别说明：使用 zhenxuan_querySelectApplyDetail 表的 scoreRuleId 和 scoreOrderMsgId 作为API参数
"""

import os
import sys
import json
import requests
import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

from database.db_config import ZHENXUAN_DB_CONFIG, DatabaseManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('zhenxuan_queryLocalAuditTrackHistory_fetch.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ZhenxuanQueryLocalAuditTrackHistoryFetcher:
    """甄选信息-评审小组 本地审核跟踪历史数据获取器"""
    
    def __init__(self, cookie_file_path=None):
        """初始化"""
        self.base_url = "https://dict.gmcc.net:30722"
        self.api_endpoint = "/partner/materialManage/pnrSelectProject/queryLocalAuditTrackHistory"
        self.db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
        self.session = requests.Session()
        
        # 默认请求头
        self.headers = {
            'Host': "dict.gmcc.net:30722",
            'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            'Accept': "application/json, text/plain, */*",
            'Accept-Encoding': "gzip, deflate, br, zstd",
            'Content-Type': "application/json;charset=UTF-8",
            'sec-ch-ua-platform': '"Windows"',
            'Authorization': "Bearer d25c514c-e026-4ddf-b455-9929dfcd3cfb",
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': "?0",
            'Origin': "https://dict.gmcc.net:30722",
            'Sec-Fetch-Site': "same-origin",
            'Sec-Fetch-Mode': "cors",
            'Sec-Fetch-Dest': "empty",
            'Referer': "https://dict.gmcc.net:30722/ptn/main/selectDemand/detail",
            'Accept-Language': "zh-CN,zh;q=0.9,ee;q=0.8",
        }
        
        # 初始化Cookie
        self.cookies = {}
        self.cookie_string = ""

        # 设置默认Cookie文件路径
        if cookie_file_path is None:
            # 查找项目根目录下的cookies文件
            cookie_file_path = os.path.join(project_root, 'cookies', 'cookies_dict_zhenxuan.json')

        # 加载Cookie
        self.load_cookies_from_file(cookie_file_path)

    def load_cookies_from_file(self, cookie_file_path: str):
        """
        从JSON文件加载Cookie，转换为Cookie字符串格式
        保留所有Cookie，包括同名但不同path的Cookie

        Args:
            cookie_file_path: Cookie文件路径
        """
        try:
            if os.path.exists(cookie_file_path):
                with open(cookie_file_path, 'r', encoding='utf-8') as f:
                    cookie_data = json.load(f)

                # 生成Cookie字符串（保留所有Cookie，包括同名的）
                cookie_pairs = []
                cookie_dict = {}  # 用于显示和统计

                for cookie in cookie_data:
                    name = cookie['name']
                    value = cookie['value']
                    path = cookie.get('path', '/')

                    # 添加到Cookie字符串（所有Cookie都要包含）
                    cookie_pairs.append(f"{name}={value}")

                    # 用于显示的字典（同名Cookie显示最后一个，但实际都会发送）
                    if name not in cookie_dict:
                        cookie_dict[name] = []
                    cookie_dict[name].append({'value': value, 'path': path})

                self.cookie_string = "; ".join(cookie_pairs)
                self.headers['Cookie'] = self.cookie_string
                self.cookies = cookie_dict

                logger.info(f"✅ 成功加载Cookie文件: {cookie_file_path}")
                logger.info(f"📊 Cookie统计: 共{len(cookie_data)}个Cookie")
                
                # 显示Cookie详情（用于调试）
                for name, values in cookie_dict.items():
                    if len(values) > 1:
                        logger.info(f"   🔄 {name}: {len(values)}个值 (多路径)")
                    else:
                        logger.info(f"   ✓ {name}: {values[0]['value'][:20]}...")

            else:
                logger.warning(f"⚠️ Cookie文件不存在: {cookie_file_path}")
                logger.info("🔄 使用默认Cookie配置")

        except Exception as e:
            logger.error(f"❌ 加载Cookie文件失败: {e}")
            logger.info("🔄 使用默认Cookie配置")

    def get_apply_detail_params_from_db(self, get_all: bool = False) -> List[Tuple[str, str]]:
        """
        从zhenxuan_querySelectApplyDetail表获取scoreRuleId和scoreOrderMsgId参数

        Args:
            get_all: 是否获取全部数据，默认False（限制数量）

        Returns:
            List[Tuple[str, str]]: (scoreRuleId, scoreOrderMsgId)元组列表
        """
        try:
            # 从zhenxuan_querySelectApplyDetail表获取参数
            # 修正：使用 select_apply_id 作为 businessId，work_order_msg_id 作为 workOrderMsgId
            if get_all:
                sql = """
                SELECT DISTINCT select_apply_id, work_order_msg_id
                FROM zhenxuan_querySelectApplyDetail
                WHERE select_apply_id IS NOT NULL
                AND work_order_msg_id IS NOT NULL
                AND select_apply_id != ''
                AND work_order_msg_id != ''
                """
            else:
                sql = """
                SELECT DISTINCT select_apply_id, work_order_msg_id
                FROM zhenxuan_querySelectApplyDetail
                WHERE select_apply_id IS NOT NULL
                AND work_order_msg_id IS NOT NULL
                AND select_apply_id != ''
                AND work_order_msg_id != ''
                LIMIT 10
                """

            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql)
                    results = cursor.fetchall()

                    if results:
                        params_list = [(row[0], row[1]) for row in results]

                        if get_all:
                            logger.info(f"✅ 从zhenxuan_querySelectApplyDetail表获取到{len(params_list)}组参数（全部数据，修正后逻辑）")
                        else:
                            logger.info(f"✅ 从zhenxuan_querySelectApplyDetail表获取到{len(params_list)}组参数（修正后逻辑）")

                        return params_list
                    else:
                        logger.warning("⚠️ zhenxuan_querySelectApplyDetail表中没有找到有效的参数")
                        return []

        except Exception as e:
            logger.error(f"❌ 从zhenxuan_querySelectApplyDetail表获取数据失败: {e}")

        # 如果数据库查询失败，返回示例数据
        logger.info("🔄 使用示例数据进行测试")
        example_params = [
            ("1904002654635212800", "GD76020250326100556464322")
        ]
        return example_params

    def fetch_audit_track_history(self, business_id: str, work_order_msg_id: str) -> Optional[Dict[str, Any]]:
        """
        获取本地审核跟踪历史数据

        Args:
            business_id: 业务ID（来源于select_apply_id）
            work_order_msg_id: 工单消息ID（来源于work_order_msg_id）

        Returns:
            Dict[str, Any]: API响应数据，失败返回None
        """
        url = f"{self.base_url}{self.api_endpoint}"

        # 构造请求数据（修正后的参数）
        request_data = {
            "businessId": business_id,          # 使用select_apply_id作为businessId
            "workOrderMsgId": work_order_msg_id,  # 使用work_order_msg_id作为workOrderMsgId
            "stepName": ""                      # 固定为空字符串
        }

        try:
            logger.info(f"🔄 请求审核跟踪历史数据: businessId={business_id}, workOrderMsgId={work_order_msg_id}")

            response = self.session.post(
                url,
                headers=self.headers,
                json=request_data,
                verify=False,  # 忽略SSL证书验证
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ 成功获取数据: code={data.get('code', 'N/A')}")
                return data
            else:
                logger.error(f"❌ HTTP请求失败: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"❌ 请求失败: {e}")
            return None

    def transform_audit_track_data(self, response_data: Dict[str, Any], business_id: str, work_order_msg_id: str) -> List[Dict[str, Any]]:
        """
        转换审核跟踪历史数据为数据库格式

        Args:
            response_data: API响应数据
            business_id: 业务ID（来源于select_apply_id）
            work_order_msg_id: 工单消息ID（来源于work_order_msg_id）

        Returns:
            List[Dict[str, Any]]: 转换后的数据列表
        """
        try:
            # 构造请求参数
            request_params = {
                "businessId": business_id,          # 使用select_apply_id作为businessId
                "workOrderMsgId": work_order_msg_id,  # 使用work_order_msg_id作为workOrderMsgId
                "stepName": ""                      # 固定为空字符串
            }

            # 获取基础响应信息
            busi_date = response_data.get('busiDate')
            code = response_data.get('code')
            message = response_data.get('message')

            # 转换busiDate格式
            busi_date_obj = None
            if busi_date:
                try:
                    busi_date_obj = datetime.strptime(busi_date, '%Y-%m-%d %H:%M:%S')
                except:
                    logger.warning(f"⚠️ 无法解析busiDate: {busi_date}")

            # 获取resultBody数据
            result_body = response_data.get('resultBody', [])
            if not isinstance(result_body, list):
                logger.warning("⚠️ resultBody不是列表格式")
                result_body = []

            transformed_data = []

            for record in result_body:
                # 转换时间字段
                create_time_obj = None
                finish_time_obj = None

                if record.get('createTime'):
                    try:
                        create_time_obj = datetime.strptime(record['createTime'], '%Y-%m-%d %H:%M:%S')
                    except:
                        logger.warning(f"⚠️ 无法解析createTime: {record.get('createTime')}")

                if record.get('finishTime'):
                    try:
                        finish_time_obj = datetime.strptime(record['finishTime'], '%Y-%m-%d %H:%M:%S')
                    except:
                        logger.warning(f"⚠️ 无法解析finishTime: {record.get('finishTime')}")

                # 构造数据库记录
                db_record = {
                    # 请求参数
                    'project_msg_id': business_id,      # 使用select_apply_id作为project_msg_id
                    'business_id': business_id,         # 使用select_apply_id作为businessId
                    'work_order_msg_id': work_order_msg_id,  # 使用work_order_msg_id
                    'step_name_filter': "",             # 固定为空字符串
                    'request_params': json.dumps(request_params, ensure_ascii=False),

                    # 响应基础信息
                    'busi_date': busi_date_obj,
                    'code': code,
                    'message': message,

                    # 审核跟踪历史信息
                    'audit_process_track_id': record.get('auditProcessTrackId'),
                    'step_name': record.get('stepName'),
                    'create_time': create_time_obj,
                    'finish_time': finish_time_obj,
                    'status': record.get('status'),
                    'audit_handler': record.get('auditHandler'),
                    'audit_remark': record.get('auditRemark'),

                    # 新增字段（保持原有字段名，但使用新的参数值）
                    'score_rule_id': business_id,       # 实际存储select_apply_id
                    'score_order_msg_id': work_order_msg_id,  # 实际存储work_order_msg_id

                    # 原始数据
                    'raw_data': json.dumps(record, ensure_ascii=False)
                }

                transformed_data.append(db_record)

            logger.info(f"✅ 数据转换完成: {len(transformed_data)}条记录")
            return transformed_data

        except Exception as e:
            logger.error(f"❌ 数据转换失败: {e}")
            return []

    def insert_audit_track_data(self, data_list: List[Dict[str, Any]]) -> bool:
        """
        插入审核跟踪历史数据到数据库

        Args:
            data_list: 要插入的数据列表

        Returns:
            bool: 插入是否成功
        """
        if not data_list:
            logger.warning("⚠️ 没有数据需要插入")
            return True

        sql = """
        INSERT INTO zhenxuan_queryLocalAuditTrackHistory (
            project_msg_id, business_id, work_order_msg_id, step_name_filter, request_params,
            busi_date, code, message, audit_process_track_id, step_name,
            create_time, finish_time, status, audit_handler, audit_remark,
            score_rule_id, score_order_msg_id, raw_data
        ) VALUES (
            %(project_msg_id)s, %(business_id)s, %(work_order_msg_id)s, %(step_name_filter)s, %(request_params)s,
            %(busi_date)s, %(code)s, %(message)s, %(audit_process_track_id)s, %(step_name)s,
            %(create_time)s, %(finish_time)s, %(status)s, %(audit_handler)s, %(audit_remark)s,
            %(score_rule_id)s, %(score_order_msg_id)s, %(raw_data)s
        ) ON DUPLICATE KEY UPDATE
            step_name_filter = VALUES(step_name_filter),
            request_params = VALUES(request_params),
            busi_date = VALUES(busi_date),
            code = VALUES(code),
            message = VALUES(message),
            step_name = VALUES(step_name),
            create_time = VALUES(create_time),
            finish_time = VALUES(finish_time),
            status = VALUES(status),
            audit_handler = VALUES(audit_handler),
            audit_remark = VALUES(audit_remark),
            score_rule_id = VALUES(score_rule_id),
            score_order_msg_id = VALUES(score_order_msg_id),
            raw_data = VALUES(raw_data),
            updated_at = CURRENT_TIMESTAMP
        """

        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.executemany(sql, data_list)
                    affected_rows = cursor.rowcount
                    conn.commit()

                    logger.info(f"✅ 成功插入/更新 {affected_rows} 条记录")
                    return True

        except Exception as e:
            logger.error(f"❌ 插入数据失败: {e}")
            return False

    def fetch_and_save_audit_track_history(self, business_id: str, work_order_msg_id: str) -> bool:
        """
        获取并保存单组参数的审核跟踪历史数据

        Args:
            business_id: 业务ID（来源于select_apply_id）
            work_order_msg_id: 工单消息ID（来源于work_order_msg_id）

        Returns:
            bool: 处理是否成功
        """
        try:
            # 获取数据
            response_data = self.fetch_audit_track_history(business_id, work_order_msg_id)
            if not response_data:
                return False

            # 检查响应状态
            if response_data.get('code') != '000000':
                logger.warning(f"⚠️ API返回错误: {response_data.get('code')} - {response_data.get('message')}")
                return False

            # 转换数据
            transformed_data = self.transform_audit_track_data(response_data, business_id, work_order_msg_id)
            if not transformed_data:
                logger.info("ℹ️ 没有审核跟踪历史数据")
                return True

            # 保存数据
            return self.insert_audit_track_data(transformed_data)

        except Exception as e:
            logger.error(f"❌ 处理参数数据失败: {e}")
            return False

    def batch_fetch_audit_track_history(self, get_all: bool = False, delay_seconds: float = 1.0) -> int:
        """
        批量获取审核跟踪历史数据

        Args:
            get_all: 是否获取全部数据
            delay_seconds: 请求间隔时间（秒）

        Returns:
            int: 成功处理的记录数
        """
        logger.info("🚀 开始批量获取审核跟踪历史数据...")

        # 连接数据库
        if not self.db_manager.connect():
            logger.error("❌ 数据库连接失败")
            return 0

        try:
            # 获取参数列表
            params_list = self.get_apply_detail_params_from_db(get_all)
            if not params_list:
                logger.warning("⚠️ 没有找到有效的参数")
                return 0

            total_count = len(params_list)
            success_count = 0

            logger.info(f"📊 准备处理 {total_count} 组参数")

            for i, (business_id, work_order_msg_id) in enumerate(params_list, 1):
                logger.info(f"🔄 处理第 {i}/{total_count} 组参数: businessId={business_id}, workOrderMsgId={work_order_msg_id}")

                try:
                    # 获取并保存数据
                    if self.fetch_and_save_audit_track_history(business_id, work_order_msg_id):
                        success_count += 1
                        logger.info(f"✅ 第 {i} 组参数处理成功")
                    else:
                        logger.warning(f"⚠️ 第 {i} 组参数处理失败")

                    # 添加延迟避免请求过快
                    if i < total_count:  # 最后一个不需要延迟
                        time.sleep(delay_seconds)

                except Exception as e:
                    logger.error(f"❌ 处理第 {i} 组参数异常: {e}")

            logger.info(f"🎉 批量处理完成: 成功 {success_count}/{total_count}")
            return success_count

        except Exception as e:
            logger.error(f"❌ 批量处理失败: {e}")
            return 0
        finally:
            self.db_manager.disconnect()

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='甄选信息-评审小组 本地审核跟踪历史数据获取程序')
    parser.add_argument('-all', action='store_true', help='获取全部数据（默认限制10条）')
    parser.add_argument('--score-rule-id', type=str, help='指定评分规则ID')
    parser.add_argument('--score-order-msg-id', type=str, help='指定评分工单消息ID')
    parser.add_argument('--delay', type=float, default=1.0, help='请求间隔时间（秒，默认1.0）')
    parser.add_argument('--cookie-file', type=str, help='Cookie文件路径')

    args = parser.parse_args()

    logger.info("=" * 80)
    logger.info("🚀 甄选信息-评审小组 本地审核跟踪历史数据获取程序")
    logger.info("=" * 80)

    start_time = datetime.now()

    try:
        # 创建获取器
        fetcher = ZhenxuanQueryLocalAuditTrackHistoryFetcher(args.cookie_file)

        if args.score_rule_id and args.score_order_msg_id:
            # 单组参数处理（注意：参数名保持兼容，但实际含义已变更）
            logger.info(f"🎯 单组参数处理模式: businessId={args.score_rule_id}, workOrderMsgId={args.score_order_msg_id}")

            if not fetcher.db_manager.connect():
                logger.error("❌ 数据库连接失败")
                sys.exit(1)

            try:
                success = fetcher.fetch_and_save_audit_track_history(args.score_rule_id, args.score_order_msg_id)

                if success:
                    logger.info("✅ 单组参数处理成功")
                else:
                    logger.error("❌ 单组参数处理失败")
                    sys.exit(1)
            finally:
                fetcher.db_manager.disconnect()
        else:
            # 批量处理
            if args.all:
                logger.info("🌍 批量处理模式: 全部数据")
            else:
                logger.info("📊 批量处理模式: 限制数据（10条）")

            success_count = fetcher.batch_fetch_audit_track_history(args.all, args.delay)

            if success_count > 0:
                logger.info(f"✅ 批量处理成功: {success_count} 组参数")
            else:
                logger.error("❌ 批量处理失败")
                sys.exit(1)

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        logger.info("=" * 80)
        logger.info("🎉 程序执行成功！")
        logger.info(f"⏱️ 总耗时: {duration:.2f} 秒")
        logger.info("=" * 80)

    except KeyboardInterrupt:
        logger.info("\n⚠️ 程序被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 程序执行异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
