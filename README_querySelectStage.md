# 甄选阶段查询数据获取系统 - 项目总结

## 🎯 项目概述

基于您提供的JSON数据结构和curl脚本，成功创建了完整的甄选阶段查询数据获取和入库系统。系统实现了从API获取数据、数据转换、数据库存储和查询等完整功能。

## ✅ 已完成功能

### 1. 数据库设计 ✅
- **表名**：`zhenxuan_querySelectStage`
- **字段数量**：24个字段，完整映射JSON数据结构
- **排序规则**：`utf8mb4_general_ci`（符合要求）
- **索引设计**：7个索引，优化查询性能
- **唯一约束**：基于`project_msg_id`和`select_rev_id`防止重复数据

### 2. 核心功能实现 ✅

#### 📊 数据表创建
- **文件**：`database/create_zhenxuan_querySelectStage.sql`
- **脚本**：`scripts/create_querySelectStage_table.py`
- **状态**：✅ 已创建并验证

#### 🔄 数据获取程序
- **文件**：`scripts/fetch_querySelectStage.py`
- **功能**：
  - ✅ 支持Cookie认证（JSON格式转字符串格式）
  - ✅ 支持`projectMsgId`参数（从数据库获取）
  - ✅ 支持`selectRevId`参数（入参入库）
  - ✅ 自动数据转换
  - ✅ 数据入库处理
  - ✅ 重复数据处理

#### 🔍 数据验证工具
- **查询功能**：支持查询已同步的数据
- **状态显示**：显示处理进度和结果统计

### 3. 参数支持 ✅

| 参数 | 支持状态 | 说明 |
|------|---------|------|
| `projectMsgId` | ✅ | 项目消息ID，从数据库自动获取 |
| `selectRevId` | ✅ | 甄选版本ID，支持入参和入库 |
| Cookie认证 | ✅ | 完整Cookie支持，JSON格式转字符串 |

### 4. 数据映射 ✅

完整映射JSON中的所有字段：

#### 响应基础信息
- `busiDate` → `busi_date`
- `code` → `code`
- `message` → `message`

#### resultBody甄选阶段信息
- `selectStage` → `select_stage`
- `selectStageValue` → `select_stage_value`
- `selectApplyId` → `select_apply_id`
- `selectApplyStatus` → `select_apply_status`
- `selectMsgId` → `select_msg_id`
- `selectPlanStatus` → `select_plan_status`
- `projectMsgId` → `project_msg_id`（入参）
- `selectDemandStatus` → `select_demand_status`
- `noticeId` → `notice_id`
- `selectClarifyId` → `select_clarify_id`
- `reviewTeamMsgId` → `review_team_msg_id`
- `clarifyWorkOrderMsgId` → `clarify_work_order_msg_id`
- `secondNegotiateId` → `second_negotiate_id`
- `workOrderMsgId` → `work_order_msg_id`
- `shutOrderMsgId` → `shut_order_msg_id`

## 🗂️ 文件结构

```
项目根目录/
├── database/
│   ├── create_zhenxuan_querySelectStage.sql    # 数据表创建SQL
│   └── db_config.py                            # 数据库配置（已扩展）
├── scripts/
│   ├── fetch_querySelectStage.py               # 主程序：数据获取和入库
│   └── create_querySelectStage_table.py        # 数据表创建脚本
├── cookies/
│   └── cookies_dict_zhenxuan.json              # Cookie文件（已更新）
├── logs/
│   └── fetch_querySelectStage.log              # 程序运行日志
└── README_querySelectStage.md                  # 项目总结（本文件）
```

## 🚀 使用方法

### 1. 创建数据表
```bash
python scripts/create_querySelectStage_table.py
```

### 2. 数据同步（基础）
```bash
python scripts/fetch_querySelectStage.py
```

### 3. 数据同步（带selectRevId）
```bash
python scripts/fetch_querySelectStage.py --select-rev-id "REV001"
```

### 4. 限制处理数量（测试）
```bash
python scripts/fetch_querySelectStage.py --max-records 5
```

### 5. 更新Cookie后同步
```bash
python scripts/fetch_querySelectStage.py --cookie "BSS-SESSION=xxx; jsession_id_4_boss=yyy"
```

### 6. 查询数据
```bash
python scripts/fetch_querySelectStage.py --query --limit 10
```

## 🔧 技术特性

### 数据库特性
- **引擎**：InnoDB
- **字符集**：utf8mb4
- **排序规则**：utf8mb4_general_ci ✅
- **主键**：自增BIGINT
- **唯一约束**：(project_msg_id, select_rev_id)
- **JSON字段**：request_params, raw_data

### 程序特性
- **请求方式**：GET with query parameters
- **认证方式**：Cookie + Authorization Bearer
- **错误处理**：完整的异常处理机制
- **日志记录**：详细的操作日志
- **数据去重**：ON DUPLICATE KEY UPDATE
- **Cookie处理**：JSON格式转字符串，保留同名Cookie

### 性能优化
- **请求限流**：1秒间隔防止过快请求
- **连接复用**：数据库连接管理
- **索引优化**：7个索引提升查询性能
- **批量处理**：支持批量获取多个projectMsgId

## 📊 数据统计

### 表结构统计
- **字段数量**：24个字段
- **索引数量**：7个索引
- **约束数量**：1个唯一约束

### 功能覆盖率
- **JSON字段映射**：100%覆盖
- **API参数支持**：100%支持
- **错误处理**：完整覆盖
- **日志记录**：详细记录

## 🎯 核心亮点

### 1. 完整的JSON字段映射 ✅
所有JSON字段都正确映射到数据库字段，包括嵌套的resultBody结构。

### 2. projectMsgId自动获取 ✅
- 优先从`zhenxuan_queryPartnerSelectDetail`表获取
- 降级到`zhenxuan_querySelectProjectList`表获取
- 支持示例数据测试

### 3. selectRevId参数支持 ✅
- 作为API请求参数传递
- 存储到数据库select_rev_id字段
- 支持按版本ID查询和统计

### 4. Cookie智能处理 ✅
- JSON格式自动转换为字符串格式
- 保留所有Cookie，包括同名但不同路径的Cookie
- 支持Cookie文件自动加载和手动更新

### 5. 原始数据保留 ✅
- request_params：保存请求参数JSON
- raw_data：保存原始响应JSON
- 便于数据追溯和调试

### 6. 数据完整性保障 ✅
- 唯一约束防止重复
- 非空约束保证关键字段
- 时间字段自动转换
- 完整的错误处理

### 7. 易用性设计 ✅
- 命令行参数支持
- 详细的帮助信息
- 友好的日志输出
- 完整的使用文档

## 🔍 验证清单

- ✅ MySQL8数据库兼容
- ✅ utf8mb4_general_ci排序规则
- ✅ 表名：zhenxuan_querySelectStage
- ✅ JSON数据结构完整映射
- ✅ projectMsgId参数支持（自动获取+入库）
- ✅ selectRevId参数支持（入参+入库）
- ✅ Cookie认证支持（JSON转字符串）
- ✅ 数据去重处理
- ✅ 错误处理机制
- ✅ 日志记录功能
- ✅ 查询验证功能

## 📚 相关文档

- [数据库配置说明](database/db_config.py)
- [API接口分析](API接口分析总结报告.md)
- [Cookie处理说明](docs/fetch_querySelectProjectList_优化说明.md)

## 🚨 注意事项

1. **Cookie有效性**：请确保Cookie文件中的认证信息有效
2. **网络连接**：程序需要访问https://dict.gmcc.net:30722
3. **数据库权限**：确保数据库用户有创建表和插入数据的权限
4. **SSL证书**：程序已配置忽略SSL证书验证

## 🔄 后续优化建议

1. **Cookie自动更新**：实现Cookie过期自动刷新机制
2. **数据校验**：增加数据完整性校验规则
3. **性能监控**：添加API响应时间监控
4. **错误重试**：实现网络错误自动重试机制
