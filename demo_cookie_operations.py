"""
Cookie操作演示脚本
展示如何使用保存的cookies进行各种操作
"""

import asyncio
import json
import os
from datetime import datetime
from playwright.async_api import async_playwright


class CookieOperationsDemo:
    def __init__(self):
        self.target_url = "https://dict.gmcc.net:30722/ptn/main/selectDemand"
        self.cookie_dir = "cookies"
        self.cookies = None
        
    def load_cookies(self):
        """加载cookies"""
        try:
            latest_cookie_file = os.path.join(self.cookie_dir, "cookies_dict_zhenxuan.json")
            
            if os.path.exists(latest_cookie_file):
                with open(latest_cookie_file, 'r', encoding='utf-8') as f:
                    self.cookies = json.load(f)
                print(f"✅ 成功加载 {len(self.cookies)} 个cookie")
                return True
            else:
                print("❌ 未找到cookies文件")
                return False
                
        except Exception as e:
            print(f"❌ 加载cookies失败: {e}")
            return False
    
    async def demo_basic_visit(self):
        """演示基础访问"""
        print("\n🎯 演示1: 基础页面访问")
        print("-" * 40)
        
        if not self.load_cookies():
            return False
            
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False, slow_mo=500)
            context = await browser.new_context(ignore_https_errors=True)
            
            try:
                # 添加cookies
                await context.add_cookies(self.cookies)
                page = await context.new_page()
                
                # 访问页面
                print(f"🌐 访问: {self.target_url}")
                await page.goto(self.target_url, wait_until='networkidle')
                
                # 检查访问结果
                current_url = page.url
                title = await page.title()
                
                print(f"📍 当前URL: {current_url}")
                print(f"📄 页面标题: {title}")
                
                if "selectDemand" in current_url:
                    print("✅ 成功访问目标页面")
                    await asyncio.sleep(3)
                    return True
                else:
                    print("❌ 访问失败")
                    return False
                    
            finally:
                await browser.close()
    
    async def demo_data_extraction(self):
        """演示数据提取"""
        print("\n🎯 演示2: 数据提取")
        print("-" * 40)
        
        if not self.load_cookies():
            return False
            
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False, slow_mo=300)
            context = await browser.new_context(ignore_https_errors=True)
            
            try:
                await context.add_cookies(self.cookies)
                page = await context.new_page()
                
                await page.goto(self.target_url, wait_until='networkidle')
                await asyncio.sleep(2)
                
                # 提取表格数据
                print("📊 正在提取表格数据...")
                tables = await page.query_selector_all('table')
                
                total_rows = 0
                for i, table in enumerate(tables):
                    rows = await table.query_selector_all('tr')
                    if len(rows) > 1:  # 有数据行
                        print(f"  表格 {i+1}: {len(rows)-1} 行数据")
                        total_rows += len(rows) - 1
                
                print(f"✅ 总共提取到 {total_rows} 行数据")
                
                # 提取页面统计信息
                buttons = await page.query_selector_all('button')
                inputs = await page.query_selector_all('input')
                
                print(f"🔘 页面按钮数量: {len(buttons)}")
                print(f"📝 输入框数量: {len(inputs)}")
                
                await asyncio.sleep(3)
                return True
                
            finally:
                await browser.close()
    
    async def demo_page_interaction(self):
        """演示页面交互"""
        print("\n🎯 演示3: 页面交互")
        print("-" * 40)
        
        if not self.load_cookies():
            return False
            
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False, slow_mo=800)
            context = await browser.new_context(ignore_https_errors=True)
            
            try:
                await context.add_cookies(self.cookies)
                page = await context.new_page()
                
                await page.goto(self.target_url, wait_until='networkidle')
                await asyncio.sleep(2)
                
                # 查找并分析搜索框
                print("🔍 分析搜索功能...")
                search_inputs = await page.query_selector_all('input[type="text"]')
                
                for i, input_elem in enumerate(search_inputs[:3]):  # 只检查前3个
                    placeholder = await input_elem.get_attribute('placeholder')
                    if placeholder:
                        print(f"  搜索框 {i+1}: {placeholder}")
                
                # 查找并分析按钮
                print("🔘 分析按钮功能...")
                buttons = await page.query_selector_all('button')
                
                button_texts = []
                for button in buttons[:5]:  # 只检查前5个
                    text = await button.inner_text()
                    if text.strip():
                        button_texts.append(text.strip())
                
                print(f"  主要按钮: {', '.join(button_texts)}")
                
                # 演示滚动操作
                print("📜 演示页面滚动...")
                await page.evaluate("window.scrollTo(0, document.body.scrollHeight/2)")
                await asyncio.sleep(1)
                await page.evaluate("window.scrollTo(0, 0)")
                
                print("✅ 交互演示完成")
                await asyncio.sleep(3)
                return True
                
            finally:
                await browser.close()
    
    async def demo_screenshot_capture(self):
        """演示截图功能"""
        print("\n🎯 演示4: 截图功能")
        print("-" * 40)
        
        if not self.load_cookies():
            return False
            
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)  # 无头模式截图
            context = await browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                ignore_https_errors=True
            )
            
            try:
                await context.add_cookies(self.cookies)
                page = await context.new_page()
                
                await page.goto(self.target_url, wait_until='networkidle')
                await asyncio.sleep(3)
                
                # 创建截图目录
                screenshot_dir = "screenshots"
                if not os.path.exists(screenshot_dir):
                    os.makedirs(screenshot_dir)
                
                # 全页面截图
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                full_screenshot = f"{screenshot_dir}/full_page_{timestamp}.png"
                await page.screenshot(path=full_screenshot, full_page=True)
                print(f"📸 全页面截图: {full_screenshot}")
                
                # 可视区域截图
                viewport_screenshot = f"{screenshot_dir}/viewport_{timestamp}.png"
                await page.screenshot(path=viewport_screenshot)
                print(f"📸 可视区域截图: {viewport_screenshot}")
                
                # 特定元素截图（如果存在表格）
                table = await page.query_selector('table')
                if table:
                    table_screenshot = f"{screenshot_dir}/table_{timestamp}.png"
                    await table.screenshot(path=table_screenshot)
                    print(f"📸 表格截图: {table_screenshot}")
                
                print("✅ 截图演示完成")
                return True
                
            finally:
                await browser.close()
    
    async def run_all_demos(self):
        """运行所有演示"""
        print("🚀 开始Cookie操作演示")
        print("=" * 50)
        
        demos = [
            self.demo_basic_visit,
            self.demo_data_extraction,
            self.demo_page_interaction,
            self.demo_screenshot_capture
        ]
        
        results = []
        for demo in demos:
            try:
                result = await demo()
                results.append(result)
                await asyncio.sleep(2)  # 演示间隔
            except Exception as e:
                print(f"❌ 演示过程中出错: {e}")
                results.append(False)
        
        # 总结
        print("\n📊 演示结果总结")
        print("=" * 50)
        demo_names = ["基础访问", "数据提取", "页面交互", "截图功能"]
        
        for i, (name, result) in enumerate(zip(demo_names, results)):
            status = "✅ 成功" if result else "❌ 失败"
            print(f"{i+1}. {name}: {status}")
        
        success_count = sum(results)
        print(f"\n🎯 总体成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")


async def main():
    """主函数"""
    demo = CookieOperationsDemo()
    await demo.run_all_demos()


if __name__ == "__main__":
    print("🎭 Cookie操作演示程序")
    print("📋 将演示基础访问、数据提取、页面交互、截图功能")
    print("🍪 请确保已有有效的cookies文件")
    print("=" * 60)
    asyncio.run(main())
