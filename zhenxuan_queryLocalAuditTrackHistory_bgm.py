"""
甄选信息-合作伙伴甄选结果反馈审核(BGM) 本地审核跟踪历史数据获取程序
根据 queryLocalAuditTrackHistory 接口获取BGM审核跟踪历史数据并入库
特别说明：使用 zhenxuan_querySelectApplyDetail 表的 scoreRuleId 和 scoreOrderMsgId 作为API参数
"""

import os
import sys
import json
import requests
import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

from database.db_config import ZHENXUAN_DB_CONFIG, DatabaseManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('zhenxuan_queryLocalAuditTrackHistory_bgm_fetch.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ZhenxuanQueryLocalAuditTrackHistoryBgmFetcher:
    """甄选信息-合作伙伴甄选结果反馈审核(BGM) 本地审核跟踪历史数据获取器"""
    
    def __init__(self, cookie_file_path=None):
        """初始化"""
        self.base_url = "https://dict.gmcc.net:30722"
        self.api_endpoint = "/partner/materialManage/pnrSelectProject/queryLocalAuditTrackHistory"
        self.db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
        self.session = requests.Session()
        
        # 默认请求头
        self.headers = {
            'Host': "dict.gmcc.net:30722",
            'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            'Accept': "application/json, text/plain, */*",
            'Accept-Encoding': "gzip, deflate, br, zstd",
            'Content-Type': "application/json;charset=UTF-8",
            'sec-ch-ua-platform': '"Windows"',
            'Authorization': "Bearer d25c514c-e026-4ddf-b455-9929dfcd3cfb",
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': "?0",
            'Origin': "https://dict.gmcc.net:30722",
            'Sec-Fetch-Site': "same-origin",
            'Sec-Fetch-Mode': "cors",
            'Sec-Fetch-Dest': "empty",
            'Referer': "https://dict.gmcc.net:30722/ptn/main/selectDemand/detail",
            'Accept-Language': "zh-CN,zh;q=0.9,ee;q=0.8",
        }
        
        # 初始化Cookie
        self.cookies = {}
        self.cookie_string = ""

        # 设置默认Cookie文件路径
        if cookie_file_path is None:
            # 查找项目根目录下的cookies文件
            cookie_file_path = os.path.join(project_root, 'cookies', 'cookies_dict_zhenxuan.json')

        # 加载Cookie
        self.load_cookies_from_file(cookie_file_path)

    def load_cookies_from_file(self, cookie_file_path: str):
        """
        从JSON文件加载Cookie，转换为Cookie字符串格式
        保留所有Cookie，包括同名但不同path的Cookie

        Args:
            cookie_file_path: Cookie文件路径
        """
        try:
            if os.path.exists(cookie_file_path):
                with open(cookie_file_path, 'r', encoding='utf-8') as f:
                    cookie_data = json.load(f)

                # 生成Cookie字符串（保留所有Cookie，包括同名的）
                cookie_pairs = []
                cookie_dict = {}  # 用于显示和统计

                for cookie in cookie_data:
                    name = cookie['name']
                    value = cookie['value']
                    path = cookie.get('path', '/')

                    # 添加到Cookie字符串（所有Cookie都要包含）
                    cookie_pairs.append(f"{name}={value}")

                    # 用于显示的字典（同名Cookie显示最后一个，但实际都会发送）
                    if name not in cookie_dict:
                        cookie_dict[name] = []
                    cookie_dict[name].append({'value': value[:20] + '...', 'path': path})

                self.cookie_string = '; '.join(cookie_pairs)
                self.headers['Cookie'] = self.cookie_string

                logger.info(f"✅ 成功加载Cookie文件: {cookie_file_path}")
                logger.info(f"📋 加载了 {len(cookie_dict)} 个Cookie项")

                # 显示Cookie统计信息
                for name, cookies in cookie_dict.items():
                    if len(cookies) > 1:
                        logger.info(f"  - {name}: {len(cookies)}个 (不同path)")
                        for i, cookie_info in enumerate(cookies, 1):
                            logger.info(f"    [{i}] {cookie_info['value']} (path: {cookie_info['path']})")
                    else:
                        logger.info(f"  - {name}: {cookies[0]['value']}")

            else:
                logger.warning(f"⚠️ Cookie文件不存在: {cookie_file_path}")

        except Exception as e:
            logger.error(f"❌ 加载Cookie失败: {e}")

    def update_cookies(self, cookie_string: str):
        """
        更新Cookie字符串

        Args:
            cookie_string: Cookie字符串，格式如 "key1=value1; key2=value2"
        """
        self.cookie_string = cookie_string
        self.headers['Cookie'] = cookie_string
        logger.info("✅ Cookie已更新")

    def fetch_audit_track_history(self, business_id: str, work_order_msg_id: str, step_name: str = "") -> Optional[Dict[str, Any]]:
        """
        获取本地审核跟踪历史数据

        Args:
            business_id: 业务ID (来源于zhenxuan_querySelectApplyDetail.select_apply_id)
            work_order_msg_id: 工单消息ID (来源于zhenxuan_querySelectApplyDetail.score_order_msg_id)
            step_name: 步骤名称过滤，默认为空字符串

        Returns:
            Dict: API响应数据，失败返回None
        """
        url = f"{self.base_url}{self.api_endpoint}"
        
        # 构建请求数据
        request_data = {
            "businessId": business_id,
            "workOrderMsgId": work_order_msg_id,
            "stepName": step_name
        }

        logger.info(f"🔄 开始获取BGM审核跟踪历史，业务ID: {business_id}, 工单ID: {work_order_msg_id}")

        try:
            # 禁用SSL验证
            response = self.session.post(
                url,
                headers=self.headers,
                json=request_data,
                verify=False,
                timeout=30
            )

            if response.status_code == 200:
                response_data = response.json()
                
                # 检查响应代码
                if response_data.get('code') == '000000':
                    logger.info(f"✅ 数据获取成功，响应码: {response_data.get('code')}")
                    return response_data
                else:
                    logger.error(f"❌ API返回错误，响应码: {response_data.get('code')}, 消息: {response_data.get('message')}")
                    return None
            else:
                logger.error(f"❌ HTTP请求失败，状态码: {response.status_code}")
                return None

        except requests.exceptions.SSLError as e:
            logger.error(f"❌ SSL证书验证失败: {e}")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 网络请求失败: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ 获取数据时发生未知错误: {e}")
            return None

    def transform_data(self, response_data: Dict[str, Any], business_id: str, work_order_msg_id: str, select_apply_id: str) -> List[Dict[str, Any]]:
        """
        转换API响应数据为数据库记录格式

        Args:
            response_data: API响应数据
            business_id: 业务ID
            work_order_msg_id: 工单消息ID
            select_apply_id: 甄选申请ID

        Returns:
            List[Dict]: 转换后的记录列表
        """
        records = []
        
        try:
            # 解析响应数据
            busi_date = response_data.get('busiDate')
            code = response_data.get('code')
            message = response_data.get('message')
            result_body = response_data.get('resultBody', [])

            # 转换busiDate格式
            busi_date_obj = None
            if busi_date:
                try:
                    busi_date_obj = datetime.strptime(busi_date, '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    logger.warning(f"⚠️ 无法解析业务日期: {busi_date}")

            # 处理resultBody中的每条记录
            for item in result_body:
                # 转换时间字段
                create_time_obj = None
                finish_time_obj = None
                
                if item.get('createTime'):
                    try:
                        create_time_obj = datetime.strptime(item['createTime'], '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        logger.warning(f"⚠️ 无法解析创建时间: {item.get('createTime')}")
                
                if item.get('finishTime'):
                    try:
                        finish_time_obj = datetime.strptime(item['finishTime'], '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        logger.warning(f"⚠️ 无法解析完成时间: {item.get('finishTime')}")

                # 构建记录
                record = {
                    'business_id': business_id,
                    'work_order_msg_id': work_order_msg_id,
                    'step_name_filter': "",  # 固定为空字符串
                    'select_apply_id': select_apply_id,
                    'score_order_msg_id': work_order_msg_id,  # 新增字段
                    'request_params': json.dumps({
                        'businessId': business_id,
                        'workOrderMsgId': work_order_msg_id,
                        'stepName': ""
                    }, ensure_ascii=False),
                    'busi_date': busi_date_obj,
                    'code': code,
                    'message': message,
                    'audit_process_track_id': item.get('auditProcessTrackId'),
                    'step_name': item.get('stepName'),
                    'create_time': create_time_obj,
                    'finish_time': finish_time_obj,
                    'status': item.get('status'),
                    'audit_handler': item.get('auditHandler'),
                    'audit_remark': item.get('auditRemark'),
                    'raw_data': json.dumps(item, ensure_ascii=False)
                }
                
                records.append(record)

            logger.info(f"📊 转换完成，共 {len(records)} 条记录")
            return records

        except Exception as e:
            logger.error(f"❌ 数据转换失败: {e}")
            return []

    def insert_records(self, records: List[Dict[str, Any]]) -> bool:
        """
        批量插入记录到数据库

        Args:
            records: 记录列表

        Returns:
            bool: 插入是否成功
        """
        if not records:
            logger.warning("⚠️ 没有记录需要插入")
            return True

        if not self.db_manager.connect():
            return False

        try:
            insert_sql = """
            INSERT INTO zhenxuan_querylocalaudittrackhistory_bgm (
                business_id, work_order_msg_id, step_name_filter, select_apply_id, score_order_msg_id, request_params,
                busi_date, code, message, audit_process_track_id, step_name,
                create_time, finish_time, status, audit_handler, audit_remark, raw_data
            ) VALUES (
                %(business_id)s, %(work_order_msg_id)s, %(step_name_filter)s, %(select_apply_id)s, %(score_order_msg_id)s, %(request_params)s,
                %(busi_date)s, %(code)s, %(message)s, %(audit_process_track_id)s, %(step_name)s,
                %(create_time)s, %(finish_time)s, %(status)s, %(audit_handler)s, %(audit_remark)s, %(raw_data)s
            ) ON DUPLICATE KEY UPDATE
                step_name_filter = VALUES(step_name_filter),
                select_apply_id = VALUES(select_apply_id),
                request_params = VALUES(request_params),
                busi_date = VALUES(busi_date),
                code = VALUES(code),
                message = VALUES(message),
                step_name = VALUES(step_name),
                create_time = VALUES(create_time),
                finish_time = VALUES(finish_time),
                status = VALUES(status),
                audit_handler = VALUES(audit_handler),
                audit_remark = VALUES(audit_remark),
                raw_data = VALUES(raw_data),
                updated_at = CURRENT_TIMESTAMP
            """

            with self.db_manager.get_cursor() as cursor:
                cursor.executemany(insert_sql, records)
                affected_rows = cursor.rowcount
                logger.info(f"✅ 成功插入/更新 {affected_rows} 条记录")
                return True

        except Exception as e:
            logger.error(f"❌ 插入记录失败: {e}")
            return False
        finally:
            self.db_manager.disconnect()

    def get_apply_detail_params(self) -> List[Tuple[str, str, str]]:
        """
        从zhenxuan_querySelectApplyDetail表获取API调用参数

        Returns:
            List[Tuple]: (business_id, work_order_msg_id, select_apply_id) 列表
        """
        if not self.db_manager.connect():
            return []

        try:
            sql = """
            SELECT DISTINCT
                select_apply_id as business_id,
                work_order_msg_id,
                select_apply_id
            FROM zhenxuan_querySelectApplyDetail
            WHERE select_apply_id IS NOT NULL
            AND work_order_msg_id IS NOT NULL
            AND select_apply_id != ''
            AND work_order_msg_id != ''
            ORDER BY select_apply_id
            """

            with self.db_manager.get_cursor() as cursor:
                cursor.execute(sql)
                results = cursor.fetchall()

                params_list = []
                for row in results:
                    business_id = row['business_id']
                    work_order_msg_id = row['work_order_msg_id']
                    select_apply_id = row['select_apply_id']

                    if business_id and work_order_msg_id and select_apply_id:
                        params_list.append((business_id, work_order_msg_id, select_apply_id))

                logger.info(f"📋 从申请详情表获取到 {len(params_list)} 个有效参数组合")
                return params_list

        except Exception as e:
            logger.error(f"❌ 获取申请详情参数失败: {e}")
            return []
        finally:
            self.db_manager.disconnect()

    def sync_all_data(self) -> int:
        """
        同步所有BGM审核跟踪历史数据

        Returns:
            int: 成功同步的记录数
        """
        logger.info("🌍 开始同步所有BGM审核跟踪历史数据...")

        # 获取所有申请详情参数
        params_list = self.get_apply_detail_params()

        if not params_list:
            logger.warning("⚠️ 没有找到有效的申请详情参数")
            return 0

        total_synced = 0
        total_params = len(params_list)

        for i, (business_id, work_order_msg_id, select_apply_id) in enumerate(params_list, 1):
            logger.info(f"🔄 处理第 {i}/{total_params} 个: 申请ID {select_apply_id}")

            try:
                # 获取审核跟踪历史数据
                response_data = self.fetch_audit_track_history(business_id, work_order_msg_id)

                if response_data:
                    # 转换数据格式
                    records = self.transform_data(response_data, business_id, work_order_msg_id, select_apply_id)

                    if records:
                        # 插入数据库
                        if self.insert_records(records):
                            total_synced += len(records)
                            logger.info(f"✅ 成功同步: {select_apply_id} ({len(records)} 条记录)")
                        else:
                            logger.error(f"❌ 同步失败: {select_apply_id}")
                    else:
                        logger.info(f"📋 无审核记录: {select_apply_id}")
                else:
                    logger.error(f"❌ 获取数据失败: {select_apply_id}")

                # 添加延迟，避免请求过于频繁
                if i < total_params:
                    time.sleep(1)

            except Exception as e:
                logger.error(f"❌ 处理申请ID {select_apply_id} 时发生错误: {e}")
                continue

        logger.info(f"🎉 BGM审核跟踪历史数据同步完成！共同步 {total_synced} 条记录")
        return total_synced

    def query_data(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        查询数据库中的数据

        Args:
            limit: 查询记录数限制

        Returns:
            List[Dict]: 查询结果
        """
        if not self.db_manager.connect():
            return []

        try:
            sql = """
            SELECT
                id, business_id, work_order_msg_id, select_apply_id, step_name,
                status, audit_handler, create_time, finish_time, audit_remark, created_at
            FROM zhenxuan_querylocalaudittrackhistory_bgm
            ORDER BY created_at DESC, create_time DESC
            LIMIT %s
            """

            with self.db_manager.get_cursor() as cursor:
                cursor.execute(sql, (limit,))
                return cursor.fetchall()

        except Exception as e:
            logger.error(f"❌ 查询数据失败: {e}")
            return []
        finally:
            self.db_manager.disconnect()


def main():
    """主程序入口"""
    import argparse

    parser = argparse.ArgumentParser(description='甄选BGM审核跟踪历史数据获取和入库程序')
    parser.add_argument('--business-id', type=str, help='指定业务ID')
    parser.add_argument('--work-order-msg-id', type=str, help='指定工单消息ID')
    parser.add_argument('--select-apply-id', type=str, help='指定甄选申请ID')
    parser.add_argument('--cookie-file', type=str, help='Cookie文件路径，默认使用cookies/cookies_dict_zhenxuan.json')
    parser.add_argument('--cookie', type=str, help='更新Cookie字符串')
    parser.add_argument('--query', action='store_true', help='查询已同步的数据')
    parser.add_argument('--limit', type=int, default=10, help='查询记录数限制，默认10')
    parser.add_argument('--all', action='store_true', help='查询全部数据模式，轮询入库所有数据')

    args = parser.parse_args()

    # 创建数据获取器（使用指定的Cookie文件）
    fetcher = ZhenxuanQueryLocalAuditTrackHistoryBgmFetcher(cookie_file_path=args.cookie_file)

    # 更新Cookie（如果提供）
    if args.cookie:
        fetcher.update_cookies(args.cookie)

    # 查询模式
    if args.query:
        logger.info("🔍 查询数据库中的BGM审核跟踪历史数据...")
        results = fetcher.query_data(args.limit)

        if results:
            logger.info(f"📋 查询到 {len(results)} 条记录:")
            for i, record in enumerate(results, 1):
                logger.info(f"  {i}. 申请ID: {record['select_apply_id']}")
                logger.info(f"     业务ID: {record['business_id']}")
                logger.info(f"     工单ID: {record['work_order_msg_id']}")
                logger.info(f"     步骤: {record['step_name']}")
                logger.info(f"     状态: {record['status']}")
                logger.info(f"     处理人: {record['audit_handler']}")
                logger.info(f"     创建: {record['create_time']}")
                logger.info(f"     完成: {record['finish_time']}")
                logger.info(f"     入库: {record['created_at']}")
                logger.info("")
        else:
            logger.info("📋 没有查询到数据")
        return

    # 单个参数处理模式
    if args.business_id and args.work_order_msg_id:
        logger.info(f"🔄 处理单个BGM审核跟踪: 业务ID {args.business_id}, 工单ID {args.work_order_msg_id}")

        # 获取审核跟踪历史数据
        response_data = fetcher.fetch_audit_track_history(args.business_id, args.work_order_msg_id)

        if response_data:
            # 转换数据格式
            select_apply_id = args.select_apply_id or "unknown"
            records = fetcher.transform_data(response_data, args.business_id, args.work_order_msg_id, select_apply_id)

            # 插入数据库
            if fetcher.insert_records(records):
                logger.info(f"✅ 成功同步BGM审核跟踪: {args.business_id}")
            else:
                logger.error(f"❌ 同步失败: {args.business_id}")
        else:
            logger.error(f"❌ 获取BGM审核跟踪失败: {args.business_id}")
        return

    # 全部数据同步模式
    if args.all:
        logger.info("🌍 全部BGM审核跟踪历史数据同步模式启动...")
        logger.info("📋 将查询并入库所有可用的BGM审核跟踪历史数据")

        # 执行全部数据同步
        synced_count = fetcher.sync_all_data()

        if synced_count > 0:
            logger.info(f"🎉 全部BGM审核跟踪历史数据同步成功！共同步 {synced_count} 条记录")
        else:
            logger.error("❌ 全部BGM审核跟踪历史数据同步失败或没有新数据")
        return

    # 如果没有指定任何操作，显示帮助信息
    parser.print_help()


if __name__ == "__main__":
    main()
