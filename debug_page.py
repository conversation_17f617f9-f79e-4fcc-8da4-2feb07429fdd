"""
调试脚本：查看登录页面的实际结构
"""

import asyncio
from playwright.async_api import async_playwright


async def debug_page():
    """调试页面结构"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context(ignore_https_errors=True)  # 忽略HTTPS证书错误
        page = await context.new_page()
        
        try:
            print("正在访问登录页面...")
            await page.goto("https://dict.gmcc.net:30722/dictWeb/login")
            await page.wait_for_load_state('networkidle')
            await asyncio.sleep(3)
            
            print("\n=== 页面标题 ===")
            title = await page.title()
            print(f"页面标题: {title}")
            
            print("\n=== 查找所有输入框 ===")
            inputs = await page.query_selector_all('input')
            for i, input_elem in enumerate(inputs):
                input_type = await input_elem.get_attribute('type') or 'text'
                input_name = await input_elem.get_attribute('name') or ''
                input_id = await input_elem.get_attribute('id') or ''
                input_class = await input_elem.get_attribute('class') or ''
                input_placeholder = await input_elem.get_attribute('placeholder') or ''
                
                print(f"输入框 {i+1}:")
                print(f"  type: {input_type}")
                print(f"  name: {input_name}")
                print(f"  id: {input_id}")
                print(f"  class: {input_class}")
                print(f"  placeholder: {input_placeholder}")
                print()
            
            print("\n=== 查找所有图片 ===")
            images = await page.query_selector_all('img')
            for i, img in enumerate(images):
                src = await img.get_attribute('src') or ''
                alt = await img.get_attribute('alt') or ''
                img_class = await img.get_attribute('class') or ''
                img_id = await img.get_attribute('id') or ''
                
                print(f"图片 {i+1}:")
                print(f"  src: {src}")
                print(f"  alt: {alt}")
                print(f"  class: {img_class}")
                print(f"  id: {img_id}")
                print()
            
            print("\n=== 查找所有按钮 ===")
            buttons = await page.query_selector_all('button, input[type="submit"], input[type="button"]')
            for i, btn in enumerate(buttons):
                btn_type = await btn.get_attribute('type') or ''
                btn_class = await btn.get_attribute('class') or ''
                btn_id = await btn.get_attribute('id') or ''
                btn_text = await btn.inner_text() or ''
                
                print(f"按钮 {i+1}:")
                print(f"  type: {btn_type}")
                print(f"  class: {btn_class}")
                print(f"  id: {btn_id}")
                print(f"  text: {btn_text}")
                print()
            
            print("\n=== 页面HTML结构 ===")
            html_content = await page.content()
            # 只显示body部分，避免输出过长
            if '<body' in html_content:
                body_start = html_content.find('<body')
                body_end = html_content.find('</body>') + 7
                body_content = html_content[body_start:body_end]
                print(body_content[:2000] + "..." if len(body_content) > 2000 else body_content)
            
            print("\n按任意键继续...")
            input()
            
        except Exception as e:
            print(f"调试过程中出现错误: {e}")
            
        finally:
            await browser.close()


if __name__ == "__main__":
    asyncio.run(debug_page())
