"""
增强版字典网站自动登录脚本
添加验证码图片保存和更详细的调试信息
"""

import asyncio
import time
import os
from datetime import datetime
from playwright.async_api import async_playwright
import ddddocr


class EnhancedDictLoginBot:
    def __init__(self):
        self.url = "https://dict.gmcc.net:30722/dictWeb/login"
        self.username = "liaochulin"
        self.password = "Liaochulin147!"
        self.ocr = ddddocr.DdddOcr()
        
        # 创建验证码图片保存目录
        self.captcha_dir = "captcha_images"
        if not os.path.exists(self.captcha_dir):
            os.makedirs(self.captcha_dir)
        
    async def login(self):
        """执行登录流程"""
        async with async_playwright() as p:
            # 启动浏览器
            browser = await p.chromium.launch(headless=False, slow_mo=1000)  # 添加慢动作以便观察
            context = await browser.new_context(ignore_https_errors=True)  # 忽略HTTPS证书错误
            page = await context.new_page()
            
            try:
                print("正在访问登录页面...")
                await page.goto(self.url)
                await page.wait_for_load_state('networkidle')
                
                # 等待页面加载完成
                await asyncio.sleep(3)
                
                # 输入用户名
                print("输入用户名...")
                username_input = await page.wait_for_selector('input[name="username"]', timeout=10000)
                await username_input.fill(self.username)
                print(f"✅ 用户名已输入: {self.username}")
                
                # 输入密码
                print("输入密码...")
                password_input = await page.wait_for_selector('input[name="password"]', timeout=10000)
                await password_input.fill(self.password)
                print("✅ 密码已输入")
                
                # 处理验证码
                success = await self.handle_captcha_enhanced(page)
                if not success:
                    print("❌ 验证码处理失败，无法继续登录")
                    return
                
                # 点击登录按钮
                print("点击登录按钮...")
                login_button = await page.wait_for_selector('button:has-text("登录")', timeout=10000)
                await login_button.click()
                print("✅ 登录按钮已点击")
                
                # 等待登录结果
                print("等待登录结果...")
                await asyncio.sleep(5)
                
                # 检查登录结果
                await self.check_login_result(page)
                    
                # 保持浏览器打开一段时间以便查看结果
                print("保持浏览器打开30秒以便查看结果...")
                await asyncio.sleep(30)
                
            except Exception as e:
                print(f"登录过程中出现错误: {e}")
                
            finally:
                await browser.close()
    
    async def handle_captcha_enhanced(self, page):
        """增强版验证码处理"""
        max_attempts = 5
        
        for attempt in range(max_attempts):
            try:
                print(f"\n🔍 第 {attempt + 1} 次尝试识别验证码...")
                
                # 查找验证码图片
                captcha_img = await page.wait_for_selector('img[id="getCodeOfPicture"]', timeout=10000)
                
                if not captcha_img:
                    print("❌ 未找到验证码图片")
                    return False
                
                # 截取验证码图片
                captcha_bytes = await captcha_img.screenshot()
                
                # 保存验证码图片以便调试
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                captcha_filename = f"{self.captcha_dir}/captcha_{timestamp}_{attempt+1}.png"
                with open(captcha_filename, 'wb') as f:
                    f.write(captcha_bytes)
                print(f"📸 验证码图片已保存: {captcha_filename}")
                
                # 使用ddddocr识别验证码
                captcha_text = self.ocr.classification(captcha_bytes)
                print(f"🔤 识别到的验证码: '{captcha_text}' (长度: {len(captcha_text)})")
                
                # 验证码长度检查
                if len(captcha_text) < 3 or len(captcha_text) > 6:
                    print(f"⚠️ 验证码长度异常: {len(captcha_text)}")
                    if attempt < max_attempts - 1:
                        await self.refresh_captcha(page)
                        continue
                
                # 清空验证码输入框并输入新的验证码
                captcha_input = await page.wait_for_selector('input[name="code"]', timeout=10000)
                await captcha_input.fill('')  # 先清空
                await asyncio.sleep(0.5)
                await captcha_input.fill(captcha_text)
                
                # 验证输入是否成功
                input_value = await captcha_input.input_value()
                print(f"✅ 验证码输入完成: '{input_value}'")
                
                if input_value == captcha_text:
                    return True
                else:
                    print(f"⚠️ 输入值与识别值不匹配: '{input_value}' != '{captcha_text}'")
                
            except Exception as e:
                print(f"❌ 验证码处理失败 (尝试 {attempt + 1}/{max_attempts}): {e}")
                if attempt < max_attempts - 1:
                    await self.refresh_captcha(page)
                    await asyncio.sleep(2)
        
        print("❌ 验证码识别失败，已达到最大尝试次数")
        return False
    
    async def refresh_captcha(self, page):
        """刷新验证码"""
        try:
            print("🔄 刷新验证码...")
            captcha_img = await page.query_selector('img[id="getCodeOfPicture"]')
            if captcha_img:
                await captcha_img.click()
                await asyncio.sleep(2)  # 等待新验证码加载
                print("✅ 验证码已刷新")
        except Exception as e:
            print(f"⚠️ 刷新验证码失败: {e}")
    
    async def check_login_result(self, page):
        """检查登录结果"""
        current_url = page.url
        print(f"📍 当前URL: {current_url}")
        
        # 检查是否有错误提示
        try:
            # 等待可能的错误消息
            await asyncio.sleep(2)
            
            # 查找各种可能的错误提示元素
            error_selectors = [
                '.el-message--error',
                '.error-message', 
                '.login-error',
                '.el-message',
                '[class*="error"]',
                '[class*="message"]'
            ]
            
            for selector in error_selectors:
                error_elements = await page.query_selector_all(selector)
                for error_elem in error_elements:
                    error_text = await error_elem.inner_text()
                    if error_text.strip():
                        print(f"❌ 发现错误信息: {error_text}")
                        
        except Exception as e:
            print(f"⚠️ 检查错误信息时出错: {e}")
        
        # 检查URL变化判断登录状态
        if "login" not in current_url.lower():
            print("✅ 登录成功！URL已跳转")
            print(f"🎉 成功跳转到: {current_url}")
        else:
            print("❌ 登录失败，仍在登录页面")
            
            # 尝试查找页面上的具体错误信息
            try:
                page_content = await page.content()
                if "验证码" in page_content and "错误" in page_content:
                    print("💡 可能是验证码错误")
                elif "用户名" in page_content and "密码" in page_content:
                    print("💡 可能是用户名或密码错误")
            except:
                pass


async def main():
    """主函数"""
    bot = EnhancedDictLoginBot()
    await bot.login()


if __name__ == "__main__":
    print("🚀 启动增强版字典网站自动登录程序...")
    print("📝 本程序将保存验证码图片到 captcha_images 目录以便调试")
    asyncio.run(main())
