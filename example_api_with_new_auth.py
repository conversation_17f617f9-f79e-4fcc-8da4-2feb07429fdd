"""
使用新认证加载器的API调用示例
展示如何在现有脚本中集成新的认证系统
"""

import requests
import json
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from auth_loader import AuthLoader

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ModernAPIClient:
    """使用新认证系统的现代API客户端"""
    
    def __init__(self, cookie_file_path: str = None):
        """
        初始化API客户端
        
        Args:
            cookie_file_path: 认证文件路径
        """
        self.base_url = "http://dict.gmcc.net:30722"
        self.session = requests.Session()
        
        # 初始化认证加载器
        self.auth_loader = AuthLoader(cookie_file_path)
        
        # 加载认证信息
        if not self.auth_loader.load_auth_data():
            logger.error("❌ 无法加载认证信息")
            raise Exception("认证信息加载失败")
        
        # 更新session的认证信息
        self.auth_loader.update_session(self.session)
        
        logger.info("✅ API客户端初始化完成")
    
    def test_connection(self) -> bool:
        """
        测试连接和认证
        
        Returns:
            bool: 连接是否成功
        """
        try:
            # 测试一个简单的API端点
            url = f"{self.base_url}/partner/materialManage/pnrSelectProject/querySelectProjectList"

            data = {
                "selecCategory": "",
                "currentPage": 1,
                "pageSize": 1  # 只获取一条记录进行测试
            }
            
            logger.info("🔍 测试API连接...")
            response = self.session.post(url, json=data, timeout=30)
            
            logger.info(f"📊 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    json_data = response.json()
                    logger.info("✅ API连接测试成功")
                    
                    # 检查响应结构
                    if 'resultBody' in json_data:
                        result_body = json_data['resultBody']
                        if 'records' in result_body:
                            records = result_body['records']
                            logger.info(f"📝 测试获取到 {len(records)} 条记录")
                    
                    return True
                    
                except json.JSONDecodeError:
                    logger.error("❌ 响应不是有效的JSON格式")
                    return False
            else:
                logger.error(f"❌ API请求失败，状态码: {response.status_code}")
                logger.error(f"📄 响应内容: {response.text[:200]}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 连接测试失败: {e}")
            return False
    
    def fetch_project_list(self, page_num: int = 1, page_size: int = 10) -> Optional[Dict[str, Any]]:
        """
        获取项目列表
        
        Args:
            page_num: 页码
            page_size: 每页大小
            
        Returns:
            dict: API响应数据
        """
        try:
            url = f"{self.base_url}/partner/materialManage/pnrSelectProject/querySelectProjectList"

            data = {
                "selecCategory": "",
                "currentPage": page_num,
                "pageSize": page_size
            }
            
            logger.info(f"📡 获取项目列表 (页码: {page_num}, 每页: {page_size})")
            response = self.session.post(url, json=data, timeout=30)
            
            if response.status_code == 200:
                json_data = response.json()
                logger.info("✅ 项目列表获取成功")
                
                # 显示统计信息
                if 'resultBody' in json_data:
                    result_body = json_data['resultBody']
                    if 'records' in result_body:
                        records = result_body['records']
                        logger.info(f"📝 获取到 {len(records)} 条记录")
                        
                        if 'total' in result_body:
                            total = result_body['total']
                            logger.info(f"📊 总记录数: {total}")
                
                return json_data
            else:
                logger.error(f"❌ 获取项目列表失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 获取项目列表异常: {e}")
            return None
    
    def fetch_apply_detail(self, select_apply_id: str) -> Optional[Dict[str, Any]]:
        """
        获取申请详情
        
        Args:
            select_apply_id: 申请ID
            
        Returns:
            dict: API响应数据
        """
        try:
            url = f"{self.base_url}/ptn/selectDemand/querySelectApplyDetail"
            
            data = {
                "selectApplyId": select_apply_id
            }
            
            logger.info(f"📡 获取申请详情 (ID: {select_apply_id})")
            response = self.session.post(url, json=data, timeout=30)
            
            if response.status_code == 200:
                json_data = response.json()
                logger.info("✅ 申请详情获取成功")
                return json_data
            else:
                logger.error(f"❌ 获取申请详情失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 获取申请详情异常: {e}")
            return None
    
    def show_auth_info(self):
        """显示当前认证信息"""
        logger.info("\n" + "="*50)
        logger.info("🔐 当前认证信息")
        logger.info("="*50)
        
        headers = self.auth_loader.get_headers()
        cookies = self.auth_loader.get_cookies_dict()
        
        logger.info(f"📋 Headers数量: {len(headers)}")
        logger.info(f"🍪 Cookies数量: {len(cookies)}")
        
        # 显示重要的headers
        important_headers = ['Authorization', 'User-Agent', 'Content-Type']
        for header in important_headers:
            if header in headers:
                value = headers[header]
                if header == 'Authorization':
                    logger.info(f"  {header}: {str(value)[:30]}...")
                else:
                    logger.info(f"  {header}: {value}")
        
        # 显示cookie字符串长度
        cookie_string = self.auth_loader.get_cookie_string()
        logger.info(f"🍪 Cookie字符串长度: {len(cookie_string)} 字符")
        
        logger.info("="*50)


def main():
    """主函数 - 演示新认证系统的使用"""
    try:
        logger.info("🚀 启动现代API客户端演示")
        logger.info("="*60)
        
        # 初始化客户端
        client = ModernAPIClient()
        
        # 显示认证信息
        client.show_auth_info()
        
        # 测试连接
        if not client.test_connection():
            logger.error("❌ 连接测试失败，退出程序")
            return
        
        # 获取项目列表
        project_data = client.fetch_project_list(page_num=1, page_size=5)
        if project_data:
            # 保存响应到文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"project_list_response_{timestamp}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, indent=2, ensure_ascii=False)
            logger.info(f"💾 响应已保存到: {filename}")
            
            # 如果有记录，尝试获取第一个申请的详情
            if 'resultBody' in project_data and 'records' in project_data['resultBody']:
                records = project_data['resultBody']['records']
                if records and 'selectApplyId' in records[0]:
                    select_apply_id = records[0]['selectApplyId']
                    if select_apply_id:
                        detail_data = client.fetch_apply_detail(select_apply_id)
                        if detail_data:
                            detail_filename = f"apply_detail_response_{timestamp}.json"
                            with open(detail_filename, 'w', encoding='utf-8') as f:
                                json.dump(detail_data, f, indent=2, ensure_ascii=False)
                            logger.info(f"💾 申请详情已保存到: {detail_filename}")
        
        logger.info("✅ 演示完成")
        
    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")


if __name__ == "__main__":
    main()
