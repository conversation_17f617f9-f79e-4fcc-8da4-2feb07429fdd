# 甄选系统API参数传递关系表

## 📋 核心参数传递链路

### 1. 主要参数来源表

| 参数名 | 来源表 | 来源字段 | 用途API | 目标表 | 说明 |
|--------|--------|----------|---------|--------|------|
| `projectMsgId` | zhenxuan_querySelectProjectList | project_msg_id | querySelectStage | zhenxuan_querySelectStage | 项目阶段查询 |
| `projectMsgId` | zhenxuan_querySelectProjectList | project_msg_id | querySelectProjectDetail | zhenxuan_querySelectProjectDetail | 项目详情查询 |
| `selectRevId` | zhenxuan_querySelectProjectList | select_msg_id | queryPartnerSelectDetail | zhenxuan_queryPartnerSelectDetail | 合作伙伴详情查询 |
| `selectApplyId` | zhenxuan_querySelectProjectList | select_apply_id | querySelectApplyDetail | zhenxuan_querySelectApplyDetail | 申请详情查询 |
| `workOrderMsgId` | zhenxuan_queryPartnerSelectDetail | work_order_msg_id | querySelectAuditTrackHistory | zhenxuan_querySelectAuditTrackHistory | 甄选审计跟踪 |
| `businessId` | zhenxuan_querySelectApplyDetail | score_rule_id | queryLocalAuditTrackHistory | zhenxuan_queryLocalAuditTrackHistory | 本地审计跟踪 |
| `workOrderMsgId` | zhenxuan_querySelectApplyDetail | score_order_msg_id | queryLocalAuditTrackHistory | zhenxuan_queryLocalAuditTrackHistory | 本地审计跟踪 |
| `businessId` | zhenxuan_querySelectApplyDetail | score_rule_id | queryLocalAuditTrackHistory_ps | zhenxuan_queryLocalAuditTrackHistory_ps | PS审计跟踪 |
| `workOrderMsgId` | zhenxuan_querySelectApplyDetail | score_order_msg_id | queryLocalAuditTrackHistory_ps | zhenxuan_queryLocalAuditTrackHistory_ps | PS审计跟踪 |
| `businessId` | zhenxuan_querySelectApplyDetail | select_apply_id | queryLocalAuditTrackHistory_bgm | zhenxuan_queryLocalAuditTrackHistory_bgm | BGM审计跟踪 |
| `businessId` | zhenxuan_querySelectProjectList | project_msg_id | queryLocalAuditTrackHistory_xqxx | zhenxuan_queryLocalAuditTrackHistory_xqxx | 需求信息审计跟踪 |
| `selectMsgId` | zhenxuan_querySelectProjectList | select_msg_id | queryNoticeHistoryBySelectId | zhenxuan_queryNoticeHistoryBySelectId | 通知历史查询 |
| `projectCode` | zhenxuan_querySelectProjectList | project_no | queryNoticeHistoryBySelectId | zhenxuan_queryNoticeHistoryBySelectId | 通知历史查询 |

### 2. API接口详细参数映射

#### A. querySelectProjectList (主入口API)
```json
{
  "入参": {
    "current": "页码",
    "size": "页大小", 
    "selectCategory": "甄选分类"
  },
  "出参关键字段": {
    "projectMsgId": "→ 用于项目相关API",
    "selectMsgId": "→ 用于合作伙伴详情API (作为selectRevId)",
    "selectApplyId": "→ 用于申请详情API",
    "workOrderMsgId": "→ 备用审计跟踪参数",
    "projectNo": "→ 用于通知历史API (作为projectCode)"
  }
}
```

#### B. querySelectStage (阶段查询API)
```json
{
  "入参": {
    "projectMsgId": "来源: zhenxuan_querySelectProjectList.project_msg_id"
  },
  "调用方式": "GET",
  "参数获取": "SELECT DISTINCT project_msg_id FROM zhenxuan_querySelectProjectList"
}
```

#### C. querySelectProjectDetail (项目详情API)
```json
{
  "入参": {
    "projectMsgId": "来源: zhenxuan_querySelectProjectList.project_msg_id"
  },
  "调用方式": "GET",
  "参数获取": "SELECT DISTINCT project_msg_id FROM zhenxuan_querySelectProjectList"
}
```

#### D. queryPartnerSelectDetail (合作伙伴详情API)
```json
{
  "入参": {
    "selectRevId": "来源: zhenxuan_querySelectProjectList.select_msg_id"
  },
  "调用方式": "GET",
  "参数获取": "SELECT DISTINCT select_msg_id FROM zhenxuan_querySelectProjectList WHERE select_msg_id IS NOT NULL",
  "出参关键字段": {
    "workOrderMsgId": "→ 用于甄选审计跟踪API",
    "selectMsgId": "→ 确认关联关系"
  }
}
```

#### E. querySelectApplyDetail (申请详情API)
```json
{
  "入参": {
    "selectApplyId": "来源: zhenxuan_querySelectProjectList.select_apply_id"
  },
  "调用方式": "GET",
  "参数获取": "SELECT DISTINCT select_apply_id FROM zhenxuan_querySelectProjectList WHERE select_apply_id IS NOT NULL",
  "出参关键字段": {
    "scoreRuleId": "→ 用于本地审计跟踪API (作为businessId)",
    "scoreOrderMsgId": "→ 用于本地审计跟踪API (作为workOrderMsgId)",
    "selectApplyId": "→ 用于BGM审计跟踪API (作为businessId)"
  }
}
```

#### F. querySelectAuditTrackHistory (甄选审计跟踪API)
```json
{
  "入参": {
    "workOrderMsgId": "来源: zhenxuan_queryPartnerSelectDetail.work_order_msg_id"
  },
  "调用方式": "POST",
  "参数获取": "SELECT DISTINCT work_order_msg_id FROM zhenxuan_queryPartnerSelectDetail WHERE work_order_msg_id IS NOT NULL"
}
```

#### G. queryLocalAuditTrackHistory (本地审计跟踪API)
```json
{
  "入参": {
    "businessId": "来源: zhenxuan_querySelectApplyDetail.score_rule_id",
    "workOrderMsgId": "来源: zhenxuan_querySelectApplyDetail.score_order_msg_id"
  },
  "调用方式": "POST",
  "参数获取": "SELECT DISTINCT score_rule_id, score_order_msg_id FROM zhenxuan_querySelectApplyDetail WHERE score_rule_id IS NOT NULL AND score_order_msg_id IS NOT NULL"
}
```

#### H. queryLocalAuditTrackHistory_ps (PS审计跟踪API)
```json
{
  "入参": {
    "businessId": "来源: zhenxuan_querySelectApplyDetail.score_rule_id",
    "workOrderMsgId": "来源: zhenxuan_querySelectApplyDetail.score_order_msg_id"
  },
  "调用方式": "POST",
  "参数获取": "同 queryLocalAuditTrackHistory"
}
```

#### I. queryLocalAuditTrackHistory_bgm (BGM审计跟踪API)
```json
{
  "入参": {
    "businessId": "来源: zhenxuan_querySelectApplyDetail.select_apply_id",
    "workOrderMsgId": "来源: zhenxuan_querySelectApplyDetail.work_order_msg_id"
  },
  "调用方式": "POST",
  "参数获取": "SELECT DISTINCT select_apply_id, work_order_msg_id FROM zhenxuan_querySelectApplyDetail WHERE select_apply_id IS NOT NULL"
}
```

#### J. queryLocalAuditTrackHistory_xqxx (需求信息审计跟踪API)
```json
{
  "入参": {
    "businessId": "来源: zhenxuan_querySelectProjectList.project_msg_id",
    "workOrderMsgId": null,
    "stepName": ""
  },
  "调用方式": "POST",
  "参数获取": "SELECT DISTINCT project_msg_id FROM zhenxuan_querySelectProjectList WHERE project_msg_id IS NOT NULL"
}
```

#### K. queryNoticeHistoryBySelectId (通知历史API)
```json
{
  "入参": {
    "selectMsgId": "来源: zhenxuan_querySelectProjectList.select_msg_id",
    "projectCode": "来源: zhenxuan_querySelectProjectList.project_no"
  },
  "调用方式": "GET",
  "参数获取": "SELECT DISTINCT select_msg_id, project_no FROM zhenxuan_querySelectProjectList WHERE select_msg_id IS NOT NULL AND project_no IS NOT NULL"
}
```

### 3. 数据同步顺序和依赖关系

#### 同步层级
```
第1层 (无依赖):
└── querySelectProjectList

第2层 (依赖第1层):
├── querySelectStage (依赖: projectMsgId)
├── querySelectProjectDetail (依赖: projectMsgId)
├── queryPartnerSelectDetail (依赖: selectMsgId)
├── querySelectApplyDetail (依赖: selectApplyId)
├── queryNoticeHistoryBySelectId (依赖: selectMsgId + projectNo)
└── queryLocalAuditTrackHistory_xqxx (依赖: projectMsgId)

第3层 (依赖第2层):
├── querySelectAuditTrackHistory (依赖: workOrderMsgId from 合作伙伴详情)
├── queryLocalAuditTrackHistory (依赖: scoreRuleId + scoreOrderMsgId from 申请详情)
├── queryLocalAuditTrackHistory_ps (依赖: scoreRuleId + scoreOrderMsgId from 申请详情)
└── queryLocalAuditTrackHistory_bgm (依赖: selectApplyId + workOrderMsgId from 申请详情)
```

### 4. 关键约束和注意事项

#### 参数有效性检查
- 所有参数必须非空且有效
- 建议在API调用前进行参数验证
- 对于可能为空的字段，需要跳过处理

#### 错误处理策略
- API调用失败时记录错误日志
- 参数无效时跳过当前记录
- 网络超时时实现重试机制
- 批量处理时单个失败不影响整体流程

#### 性能优化建议
- 使用批量查询减少数据库访问次数
- 实现并行API调用提高效率
- 添加请求间隔避免API限流
- 使用连接池优化数据库连接

---

**文档版本**: v1.0  
**最后更新**: 2025-07-09  
**维护者**: 甄选数据团队
