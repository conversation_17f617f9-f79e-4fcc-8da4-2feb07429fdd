# 甄选系统数据表关联关系分析

## 📊 项目概览

**项目名称**: 甄选需求管理系统数据爬虫  
**数据库**: zhenxuandb (MySQL 8.0)  
**总表数**: 27个表  
**总数据量**: 23,485行  
**分析时间**: 2025-07-09  

## 🔗 核心数据流向和表关联关系

### 1. 主数据流向图

```
querySelectProjectList (项目列表)
    ↓ projectMsgId
querySelectStage (甄选阶段)
    ↓ projectMsgId  
querySelectProjectDetail (项目详情)

querySelectProjectList (项目列表)
    ↓ selectMsgId → selectRevId
queryPartnerSelectDetail (合作伙伴详情)

querySelectProjectList (项目列表)  
    ↓ selectApplyId
querySelectApplyDetail (申请详情)

各表 → 审计跟踪表群 (6个审计表)
```

### 2. 核心业务表关联关系

#### 🎯 主表: zhenxuan_querySelectProjectList
**作用**: 甄选项目列表，系统的核心入口表  
**数据量**: 1,533行  
**关键字段**:
- `project_msg_id` (主键) → 关联其他表的核心ID
- `select_msg_id` → 用作selectRevId关联合作伙伴详情
- `select_apply_id` → 关联申请详情
- `work_order_msg_id` → 关联审计跟踪

#### 📋 关联表群组

##### A. 项目详情组
```
zhenxuan_querySelectProjectList.project_msg_id
    ↓
zhenxuan_querySelectStage.project_msg_id (入参)
    ↓  
zhenxuan_querySelectProjectDetail.project_msg_id (入参)
```

**API调用链**:
1. `querySelectProjectList` → 获取项目列表
2. `querySelectStage` → 使用projectMsgId获取阶段信息  
3. `querySelectProjectDetail` → 使用projectMsgId获取项目详情

##### B. 合作伙伴详情组
```
zhenxuan_querySelectProjectList.select_msg_id
    ↓ (作为selectRevId)
zhenxuan_queryPartnerSelectDetail.select_rev_id (入参)
```

**API调用链**:
1. `querySelectProjectList` → 获取selectMsgId
2. `queryPartnerSelectDetail` → 使用selectMsgId作为selectRevId参数

##### C. 申请详情组  
```
zhenxuan_querySelectProjectList.select_apply_id
    ↓
zhenxuan_querySelectApplyDetail.select_apply_id (入参)
```

**API调用链**:
1. `querySelectProjectList` → 获取selectApplyId
2. `querySelectApplyDetail` → 使用selectApplyId获取申请详情

### 3. 审计跟踪表关联关系

#### 🔍 审计跟踪表群 (6个表)

##### A. 主审计跟踪表
```
zhenxuan_querySelectAuditTrackHistory
├── 入参: workOrderMsgId (来源: zhenxuan_queryPartnerSelectDetail.work_order_msg_id)
├── 数据量: 3,704行
└── 作用: 甄选审计跟踪历史
```

##### B. 本地审计跟踪表群 (5个表)
```
1. zhenxuan_queryLocalAuditTrackHistory
   ├── 入参: businessId (scoreRuleId), workOrderMsgId (scoreOrderMsgId)  
   ├── 来源: zhenxuan_querySelectApplyDetail表
   ├── 数据量: 15行

2. zhenxuan_queryLocalAuditTrackHistory_ps  
   ├── 入参: businessId (scoreRuleId), workOrderMsgId (scoreOrderMsgId)
   ├── 来源: zhenxuan_querySelectApplyDetail表
   ├── 数据量: 24行

3. zhenxuan_queryLocalAuditTrackHistory_bgm
   ├── 入参: businessId (selectApplyId), workOrderMsgId
   ├── 来源: zhenxuan_querySelectApplyDetail表  
   ├── 数据量: 12行

4. zhenxuan_queryLocalAuditTrackHistory_xqxx
   ├── 入参: businessId (projectMsgId), workOrderMsgId=null
   ├── 来源: zhenxuan_querySelectProjectList表
   ├── 数据量: 874行

5. zhenxuan_queryLocalAuditTrackHistory_ksm
   ├── 入参: 动态参数
   ├── 数据量: 4,359行 (最大)
```

### 4. 通知历史关联
```
zhenxuan_querySelectProjectList
├── select_msg_id + project_no (projectCode)
    ↓
zhenxuan_queryNoticeHistoryBySelectId
├── 入参: selectMsgId, projectCode  
├── 数据量: 65行
└── 作用: 甄选通知历史
```

## 🔧 API接口参数传递关系

### 核心参数传递链

#### 1. 项目维度传递链
```
querySelectProjectList API
    ↓ 返回: projectMsgId
querySelectStage API (入参: projectMsgId)
    ↓ 返回: 阶段信息
querySelectProjectDetail API (入参: projectMsgId)
    ↓ 返回: 项目详情
```

#### 2. 甄选维度传递链  
```
querySelectProjectList API
    ↓ 返回: selectMsgId
queryPartnerSelectDetail API (入参: selectRevId = selectMsgId)
    ↓ 返回: 合作伙伴详情 + workOrderMsgId
querySelectAuditTrackHistory API (入参: workOrderMsgId)
    ↓ 返回: 审计跟踪历史
```

#### 3. 申请维度传递链
```
querySelectProjectList API  
    ↓ 返回: selectApplyId
querySelectApplyDetail API (入参: selectApplyId)
    ↓ 返回: scoreRuleId, scoreOrderMsgId, selectApplyId
queryLocalAuditTrackHistory* APIs (入参: businessId, workOrderMsgId)
    ↓ 返回: 各类审计跟踪数据
```

### API端点汇总

| API端点 | 入参来源表 | 入参字段 | 目标表 |
|---------|------------|----------|--------|
| querySelectProjectList | - | 分页参数 | zhenxuan_querySelectProjectList |
| querySelectStage | querySelectProjectList | projectMsgId | zhenxuan_querySelectStage |
| querySelectProjectDetail | querySelectProjectList | projectMsgId | zhenxuan_querySelectProjectDetail |
| queryPartnerSelectDetail | querySelectProjectList | selectMsgId→selectRevId | zhenxuan_queryPartnerSelectDetail |
| querySelectApplyDetail | querySelectProjectList | selectApplyId | zhenxuan_querySelectApplyDetail |
| querySelectAuditTrackHistory | queryPartnerSelectDetail | workOrderMsgId | zhenxuan_querySelectAuditTrackHistory |
| queryLocalAuditTrackHistory | querySelectApplyDetail | scoreRuleId, scoreOrderMsgId | zhenxuan_queryLocalAuditTrackHistory |
| queryLocalAuditTrackHistory_ps | querySelectApplyDetail | scoreRuleId, scoreOrderMsgId | zhenxuan_queryLocalAuditTrackHistory_ps |
| queryLocalAuditTrackHistory_bgm | querySelectApplyDetail | selectApplyId, workOrderMsgId | zhenxuan_queryLocalAuditTrackHistory_bgm |
| queryLocalAuditTrackHistory_xqxx | querySelectProjectList | projectMsgId | zhenxuan_queryLocalAuditTrackHistory_xqxx |
| queryNoticeHistoryBySelectId | querySelectProjectList | selectMsgId, projectNo | zhenxuan_queryNoticeHistoryBySelectId |

## 📈 数据同步策略

### 同步顺序 (依赖关系)
1. **第一层**: `querySelectProjectList` (主表，无依赖)
2. **第二层**: 
   - `querySelectStage` (依赖: projectMsgId)
   - `querySelectProjectDetail` (依赖: projectMsgId)  
   - `queryPartnerSelectDetail` (依赖: selectMsgId)
   - `querySelectApplyDetail` (依赖: selectApplyId)
   - `queryNoticeHistoryBySelectId` (依赖: selectMsgId, projectNo)
3. **第三层**:
   - `querySelectAuditTrackHistory` (依赖: workOrderMsgId)
   - 各类 `queryLocalAuditTrackHistory*` (依赖: 申请详情表的参数)

### 关键约束
- 所有API调用都需要有效的Cookie认证
- 参数必须从上游表动态获取，不能硬编码
- 需要处理空值和无效参数的情况
- 建议添加请求间隔避免频率限制

## 🎯 业务视图关系

项目包含9个业务视图表，用于数据分析和报表：

| 视图名 | 基础表 | 用途 |
|--------|--------|------|
| v_zhenxuan_audit_track_summary | 审计跟踪表群 | 审计跟踪汇总 |
| v_zhenxuan_project_audit_relation | 项目+审计表 | 项目审计关系 |
| v_zhenxuan_stage_audit_relation | 阶段+审计表 | 阶段审计关系 |
| v_zhenxuan_stage_summary | querySelectStage | 阶段汇总 |
| v_zhenxuan_apply_audit_bgm_relation | 申请+BGM审计 | 申请BGM审计关系 |
| v_zhenxuan_apply_detail_summary | querySelectApplyDetail | 申请详情汇总 |
| v_zhenxuan_notice_history_summary | queryNoticeHistoryBySelectId | 通知历史汇总 |
| v_zhenxuan_audit_track_bgm_summary | LocalAuditTrackHistory_bgm | BGM审计汇总 |
| v_zhenxuan_project_detail_summary | querySelectProjectDetail | 项目详情汇总 |

## 💻 技术实现细节

### Cookie认证机制
- **Cookie文件**: `cookies/cookies_dict_zhenxuan.json`
- **格式转换**: JSON格式 → Cookie字符串 (`key1=value1; key2=value2`)
- **特殊处理**: 保留同名但不同路径的Cookie
- **更新策略**: 覆盖现有文件，不创建时间戳版本

### 数据库配置
```python
ZHENXUAN_DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'cmcc12345',
    'database': 'zhenxuandb',
    'charset': 'utf8mb4'
}
```

### 关键代码文件结构
```
scripts/
├── fetch_querySelectProjectList.py     # 主表数据获取
├── fetch_querySelectStage.py           # 阶段数据获取
├── fetch_queryPartnerSelectDetail.py   # 合作伙伴详情获取
├── zhenxuan_querySelectApplyDetail.py  # 申请详情获取
├── fetch_querySelectAuditTrackHistory.py # 审计跟踪获取
├── fetch_queryLocalAuditTrackHistory*.py # 本地审计跟踪获取
└── zhenxuan_queryNoticeHistoryBySelectId.py # 通知历史获取

database/
├── create_zhenxuan_*.sql               # 各表结构定义
├── db_config.py                        # 数据库配置和工具类
└── final_table_summary.md              # 表统计汇总
```

## 🚨 重要发现和建议

### 数据质量问题
1. **大表问题**: `zhenxuan_querySelectApplyDetail` 表206行占用53.70MB
   - 平均每行267.5KB，可能包含大量JSON或BLOB数据
   - 建议检查`raw_data`和`select_project_demand_detail_vo`字段

2. **空表**: 4个表为空需要激活数据同步
   - `api_request_logs`, `city_areas`, `dict_data`, `selection_projects`

### 性能优化建议
1. **索引优化**: 确保关联字段都有适当索引
2. **分页查询**: 大表查询使用分页避免内存溢出
3. **批量处理**: API调用实现批量处理和错误重试
4. **数据归档**: 审计跟踪表定期归档历史数据

### 监控和维护
1. **数据一致性检查**: 定期验证关联关系完整性
2. **同步状态监控**: 使用`sync_status`表跟踪同步进度
3. **API日志记录**: 激活`api_request_logs`表记录调用历史
4. **定期统计**: 每周运行`table_stats.py`监控数据增长

## 📋 操作手册

### 完整数据同步流程
```bash
# 1. 同步项目列表 (主表)
python scripts/fetch_querySelectProjectList.py --all

# 2. 同步项目相关详情
python scripts/fetch_querySelectStage.py --all
python scripts/fetch_querySelectProjectDetail.py --all

# 3. 同步合作伙伴详情
python scripts/fetch_queryPartnerSelectDetail.py --all

# 4. 同步申请详情
python scripts/zhenxuan_querySelectApplyDetail.py --all

# 5. 同步审计跟踪
python scripts/fetch_querySelectAuditTrackHistory.py --all
python scripts/fetch_queryLocalAuditTrackHistory.py --all
python scripts/fetch_queryLocalAuditTrackHistory_ps.py --all
python scripts/fetch_queryLocalAuditTrackHistory_bgm.py --all
python scripts/fetch_queryLocalAuditTrackHistory_xqxx.py --all

# 6. 同步通知历史
python scripts/zhenxuan_queryNoticeHistoryBySelectId.py --all
```

### 数据验证命令
```bash
# 检查表结构和数据量
python database/table_stats.py

# 验证关联关系
python scripts/verify_data.py

# 检查申请详情参数
python check_apply_detail_params.py
```

---

**文档版本**: v1.0
**最后更新**: 2025-07-09
**维护者**: 甄选数据团队
