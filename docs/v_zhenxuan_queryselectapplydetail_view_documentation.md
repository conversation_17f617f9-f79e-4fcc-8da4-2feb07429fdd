# v_zhenxuan_queryselectapplydetail 视图文档

## 📋 概述

`v_zhenxuan_queryselectapplydetail` 视图是基于 `zhenxuan_queryselectapplydetail` 表的 `raw_data` JSON 字段创建的，用于解析和提取甄选申请详情的关键信息。

## 🎯 创建目的

- 解析 `raw_data` 字段中的 JSON 数据
- 提供结构化的字段访问
- 简化复杂的 JSON 查询操作
- 统一数据访问接口

## 📊 视图结构

### 基础字段（来自表字段）
- `select_apply_id` - 甄选申请ID（主键）
- `request_params` - 请求参数（JSON）

### 从 JSON 提取的核心字段
- `select_rev_id` - 甄选版本ID
- `select_rev_name` - 甄选版本名称
- `project_name` - 项目名称
- `customer_name` - 客户名称
- `project_code` - 项目编号
- `project_no` - 项目号

### 甄选类型相关
- `select_type` - 甄选类型
- `select_name` - 甄选名称
- `select_type_value` - 甄选类型值
- `project_type_value` - 项目类型值

### 时间字段
- `create_time` - 创建时间
- `start_time` - 开始时间
- `end_time` - 结束时间
- `realEndTime` - 实际结束时间
- `systemEndSelectTime` - 系统结束甄选时间

### 状态字段
- `apply_status_value` - 申请状态值
- `apply_review_status_value` - 申请审核状态值
- `rating` - 评级

### 业务ID字段
- `review_file_business_id` - 审核文件业务ID
- `work_order_msg_id` - 工单消息ID
- `score_order_msg_id` - 评分工单消息ID
- `score_rule_id` - 评分规则ID
- `select_msg_id` - 甄选消息ID
- `selectMsgId` - 甄选消息ID（重复字段）

### 验证和预算字段
- `is_need_verification` - 是否需要验证
- `is_finish_verification` - 是否完成验证
- `non_tax_select_budget` - 非税甄选预算

### 备注和通知字段
- `action_remark` - 操作备注
- `push_notice` - 推送通知

### 技术审核和投标字段
- `is_technical_review` - 是否技术审核
- `bid_flag_desc` - 投标标志描述
- `bid_opening_time` - 开标时间
- `is_pre_review` - 是否预审

### 结果文档字段
- `select_result_doc` - 甄选结果文档
- `result_input_type` - 结果输入类型
- `result_title` - 结果标题
- `result_content` - 结果内容
- `doc_number_sub` - 文档编号子号
- `doc_number` - 文档编号

### 会议结果字段
- `select_result_meet` - 甄选结果会议
- `select_result_meet_list` - 甄选结果会议列表（JSON）

### 额外有用字段
- `business_area` - 业务区域
- `business_area_value` - 业务区域值
- `project_type` - 项目类型
- `select_budget` - 甄选预算
- `decide_opinion` - 决策意见
- `end_select_time` - 结束甄选时间

### 元数据字段
- `raw_data` - 原始JSON数据
- `created_at` - 创建时间戳
- `updated_at` - 更新时间戳
- `id` - 记录ID

## 📈 数据统计

- **总记录数**: 895条
- **字段完整性**:
  - 申请ID: 100% (895/895)
  - 项目名称: 100% (895/895)
  - 评级: 100% (895/895)
  - 实际结束时间: 100% (895/895)
  - 系统结束时间: 79% (707/895)

## 🏆 评级分布

- **D级**: 826个项目 (92.3%)
- **C级**: 49个项目 (5.5%)
- **B级**: 14个项目 (1.6%)
- **A级**: 6个项目 (0.7%)

## 💡 使用示例

### 1. 基本查询
```sql
SELECT 
    select_apply_id, project_name, customer_name, 
    apply_status_value, rating, create_time
FROM v_zhenxuan_queryselectapplydetail 
LIMIT 10;
```

### 2. 条件筛选
```sql
SELECT 
    project_name, rating, apply_status_value
FROM v_zhenxuan_queryselectapplydetail 
WHERE apply_status_value = '已结束' 
AND rating IN ('A', 'B', 'C');
```

### 3. 聚合统计
```sql
SELECT 
    rating,
    COUNT(*) as project_count
FROM v_zhenxuan_queryselectapplydetail 
GROUP BY rating
ORDER BY project_count DESC;
```

### 4. 时间范围查询
```sql
SELECT 
    project_name, create_time, realEndTime
FROM v_zhenxuan_queryselectapplydetail 
WHERE create_time >= '2025-01-01'
ORDER BY create_time DESC;
```

### 5. JSON字段查询
```sql
SELECT 
    project_name, select_result_meet_list
FROM v_zhenxuan_queryselectapplydetail 
WHERE select_result_meet_list IS NOT NULL
AND JSON_LENGTH(select_result_meet_list) > 0;
```

## ⚠️ 注意事项

1. **性能考虑**: 视图基于JSON解析，大量数据查询时可能较慢
2. **排序限制**: 避免对大结果集进行复杂排序，可能遇到内存限制
3. **NULL值处理**: JSON中的null值会被转换为SQL的NULL
4. **数据类型**: 时间字段已转换为适当的MySQL数据类型
5. **索引建议**: 考虑在常用查询字段上创建索引

## 🔧 维护建议

1. **定期检查**: 监控视图查询性能
2. **数据验证**: 定期验证JSON数据的完整性
3. **字段映射**: 如果源JSON结构变化，需要更新视图定义
4. **备份策略**: 确保视图定义包含在数据库备份中

## 📝 更新历史

- **2025-07-30**: 初始创建，包含43个目标字段的解析
- 基于 `zhenxuan_queryselectapplydetail` 表的 `raw_data` 字段
- 支持895条记录的完整解析

## 🚀 扩展可能

1. **物化视图**: 考虑创建物化视图提高查询性能
2. **索引优化**: 在常用查询字段上添加索引
3. **分区策略**: 按时间或业务区域进行分区
4. **数据清洗**: 添加数据质量检查和清洗逻辑
