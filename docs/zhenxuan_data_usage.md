# 甄选项目数据获取和入库系统使用说明

## 📋 概述

本系统基于 `querySelectProjectList` 接口，实现甄选项目数据的自动获取和入库功能。支持带 Cookie 访问、参数化查询、数据转换和批量入库。

## 🗄️ 数据库表结构

### 表名：`zhenxuan_querySelectProjectList`

| 字段分类 | 字段名 | 类型 | 说明 |
|---------|--------|------|------|
| **主键** | `id` | BIGINT | 自增主键 |
| **请求参数** | `select_rev_id` | VARCHAR(100) | 甄选版本ID（入参） |
| | `request_params` | JSON | 请求参数JSON |
| **响应信息** | `busi_date` | DATETIME | 业务日期 |
| | `response_code` | VARCHAR(20) | 响应代码 |
| | `response_message` | TEXT | 响应消息 |
| **分页信息** | `total` | INT | 总记录数 |
| | `page_size` | INT | 每页大小 |
| | `current_page` | INT | 当前页码 |
| | `total_pages` | INT | 总页数 |
| **项目核心** | `project_msg_id` | VARCHAR(50) | 项目消息ID（唯一） |
| | `work_order_msg_id` | VARCHAR(50) | 工单消息ID |
| | `select_msg_id` | VARCHAR(50) | 甄选消息ID |
| | `select_apply_id` | VARCHAR(50) | 甄选申请ID |
| **项目基本** | `project_name` | VARCHAR(200) | 项目名称 |
| | `select_name` | VARCHAR(200) | 甄选需求名称 |
| | `project_no` | VARCHAR(50) | 项目编码 |
| | `count` | VARCHAR(10) | 甄选方案数量 |
| **分类信息** | `project_type` | VARCHAR(20) | 项目类型 |
| | `project_label` | VARCHAR(20) | 项目标签 |
| | `select_category` | VARCHAR(20) | 甄选分类代码 |
| | `select_category_value` | VARCHAR(50) | 甄选分类值 |
| **区域时间** | `business_area` | VARCHAR(20) | 业务区域代码 |
| | `business_area_value` | VARCHAR(50) | 业务区域名称 |
| | `start_time` | DATETIME | 开始时间 |
| | `create_time` | DATETIME | 创建时间 |
| **状态信息** | `select_status` | VARCHAR(20) | 甄选状态代码 |
| | `select_status_value` | VARCHAR(50) | 甄选状态值 |
| **人员信息** | `create_staff` | VARCHAR(50) | 创建人员代码 |
| | `create_staff_value` | VARCHAR(50) | 创建人员姓名 |
| | `next_todo_handler` | VARCHAR(50) | 下一步处理人代码 |
| | `next_todo_handler_value` | VARCHAR(50) | 下一步处理人姓名 |
| **操作标识** | `is_fixed_softness` | VARCHAR(10) | 是否固定软件 |
| | `is_operable` | VARCHAR(10) | 是否可操作 |
| | `is_terminable` | VARCHAR(10) | 是否可终止 |
| **原始数据** | `raw_data` | JSON | 原始JSON数据 |
| **系统字段** | `created_at` | TIMESTAMP | 记录创建时间 |
| | `updated_at` | TIMESTAMP | 记录更新时间 |

### 索引设计

- **唯一索引**：`uk_project_msg_id` (project_msg_id)
- **普通索引**：project_no, select_status, business_area, create_time, start_time, create_staff, select_category
- **复合索引**：(select_status, business_area), (create_time, select_status)

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install requests pymysql

# 确保MySQL8数据库运行
# 数据库配置：host=127.0.0.1, port=3306, user=root, password=cmcc12345, database=zhenxuandb
```

### 2. 创建数据表

```bash
python scripts/create_table.py
```

### 3. 数据同步

```bash
# 基本同步（默认甄选分类1，最多50页）
python scripts/fetch_zhenxuan_data.py

# 指定甄选版本ID
python scripts/fetch_zhenxuan_data.py --select-rev-id "REV001"

# 指定甄选分类和页数限制
python scripts/fetch_zhenxuan_data.py --select-category "1" --max-pages 10

# 更新Cookie后同步
python scripts/fetch_zhenxuan_data.py --cookie "BSS-SESSION=xxx; jsession_id_4_boss=yyy"
```

### 4. 查询数据

```bash
# 查询最新10条记录
python scripts/fetch_zhenxuan_data.py --query

# 查询最新20条记录
python scripts/fetch_zhenxuan_data.py --query --limit 20
```

## 📖 详细使用说明

### 命令行参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--select-rev-id` | string | None | 甄选版本ID，会作为请求参数传递给API |
| `--select-category` | string | "1" | 甄选分类（1=项目甄选，3=算力项目甄选） |
| `--max-pages` | int | 50 | 最大页数限制，防止无限循环 |
| `--cookie` | string | None | 更新Cookie字符串，格式："key1=value1; key2=value2" |
| `--query` | flag | False | 查询模式，显示已同步的数据 |
| `--limit` | int | 10 | 查询记录数限制 |

### API接口说明

**接口地址**：`http://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList`

**请求方法**：POST

**请求参数**：
```json
{
  "selecCategory": "1",        // 甄选分类
  "currentPage": 1,            // 当前页码
  "pageSize": 100,             // 每页大小
  "selectRevId": "REV001"      // 甄选版本ID（可选）
}
```

**响应格式**：
```json
{
  "busiDate": "2025-07-08 20:03:04",
  "code": "000000",
  "message": null,
  "resultBody": {
    "total": 1445,
    "size": 10,
    "current": 1,
    "records": [
      {
        "projectMsgId": "1942422593200898048",
        "projectName": "项目名称",
        "selectName": "甄选需求名称",
        "projectNo": "CMGDZSICT20250707037",
        // ... 更多字段
      }
    ],
    "pages": 145
  }
}
```

## 🔧 程序架构

### 核心类：`ZhenxuanDataFetcher`

```python
class ZhenxuanDataFetcher:
    def __init__(self):
        """初始化数据获取器"""
        
    def update_cookies(self, cookie_string: str):
        """更新Cookie"""
        
    def fetch_project_list(self, select_rev_id=None, ...):
        """获取甄选项目列表数据"""
        
    def transform_record_data(self, record, response_data, ...):
        """转换单条记录数据为数据库格式"""
        
    def insert_record(self, record_data):
        """插入单条记录到数据库"""
        
    def sync_all_data(self, select_rev_id=None, ...):
        """同步所有数据"""
        
    def query_data(self, limit=10):
        """查询数据库中的数据"""
```

### 数据转换流程

1. **API响应** → JSON解析
2. **字段映射** → 数据库字段格式
3. **时间转换** → datetime对象
4. **JSON存储** → 原始数据和请求参数
5. **数据库插入** → ON DUPLICATE KEY UPDATE

### 错误处理

- **网络异常**：自动重试机制
- **数据库异常**：事务回滚
- **数据格式异常**：跳过错误记录，记录日志
- **Cookie过期**：提示更新Cookie

## 📊 数据查询示例

### SQL查询示例

```sql
-- 查询最新项目
SELECT project_name, select_name, project_no, select_status_value, create_time
FROM zhenxuan_querySelectProjectList 
ORDER BY create_time DESC 
LIMIT 10;

-- 按状态统计
SELECT select_status_value, COUNT(*) as count
FROM zhenxuan_querySelectProjectList 
GROUP BY select_status_value;

-- 按区域统计
SELECT business_area_value, COUNT(*) as count
FROM zhenxuan_querySelectProjectList 
GROUP BY business_area_value
ORDER BY count DESC;

-- 查询特定甄选版本的数据
SELECT * FROM zhenxuan_querySelectProjectList 
WHERE select_rev_id = 'REV001';
```

### Python查询示例

```python
from database.db_config import get_db_manager

# 获取数据库管理器
db = get_db_manager()
if db.connect():
    # 查询最新项目
    projects = db.execute_query("""
        SELECT project_name, select_status_value, create_time
        FROM zhenxuan_querySelectProjectList 
        ORDER BY create_time DESC 
        LIMIT 5
    """)
    
    for project in projects:
        print(f"{project['project_name']} - {project['select_status_value']}")
    
    db.disconnect()
```

## 🔍 日志和监控

### 日志文件

- **文件位置**：`zhenxuan_data_fetch.log`
- **日志级别**：INFO, ERROR
- **编码格式**：UTF-8

### 日志内容

- 数据获取进度
- 数据库操作结果
- 错误详情和堆栈
- 性能统计信息

### 监控指标

- 同步成功率
- 数据获取耗时
- 数据库插入性能
- 错误记录数量

## ⚠️ 注意事项

1. **Cookie管理**：Cookie有时效性，需要定期更新
2. **请求频率**：程序内置1秒延迟，避免请求过快
3. **数据去重**：基于 `project_msg_id` 自动去重
4. **内存使用**：大批量数据时注意内存占用
5. **网络稳定**：确保网络连接稳定
6. **数据库连接**：确保MySQL服务正常运行

## 🛠️ 故障排除

### 常见问题

1. **Cookie过期**
   ```bash
   # 解决方案：更新Cookie
   python scripts/fetch_zhenxuan_data.py --cookie "新的Cookie字符串"
   ```

2. **数据库连接失败**
   ```bash
   # 检查MySQL服务状态
   # 验证数据库配置：host=127.0.0.1, port=3306, user=root, password=cmcc12345
   ```

3. **网络请求超时**
   ```bash
   # 检查网络连接
   # 减少页大小：修改代码中的page_size参数
   ```

4. **数据重复**
   ```bash
   # 程序自动处理重复数据，基于project_msg_id去重
   ```

## 📈 性能优化

1. **批量插入**：使用 ON DUPLICATE KEY UPDATE
2. **索引优化**：基于查询模式设计索引
3. **分页处理**：避免单次请求过大数据量
4. **连接池**：复用数据库连接
5. **异步处理**：可考虑异步IO优化

## 🔄 扩展功能

1. **定时同步**：结合cron实现定时数据同步
2. **增量同步**：基于时间戳实现增量更新
3. **数据导出**：支持Excel、CSV格式导出
4. **API接口**：提供RESTful API查询接口
5. **监控告警**：集成监控系统，异常告警
