# v_zhenxuan_queryselectapplydetai_partner 视图文档

## 📋 概述

`v_zhenxuan_queryselectapplydetai_partner` 视图专门用于解析 `zhenxuan_queryselectapplydetail` 表中 `raw_data` JSON 字段里 `decideResultValue` 为 "中选" 的合作伙伴信息。

## 🎯 创建目的

- 提取中选合作伙伴的详细信息
- 展开 JSON 数组中的合作伙伴数据
- 提供结构化的合作伙伴查询接口
- 支持合作伙伴中选情况的统计分析

## 📊 数据来源

### JSON 路径结构
```json
{
  "resultBody": {
    "selectApplyResultDetailVos": [
      {
        "selectApplyResultId": "1595643469256114176",
        "selectApplyId": "1595643468912181248", 
        "partnerMsgId": "1575320258933080064",
        "partnerName": "中山市纳加在线科技有限公司",
        "bidMoneyValue": "568,970",
        "decideResultValue": "中选"
      }
    ]
  }
}
```

### 筛选条件
- 只包含 `decideResultValue` = "中选" 的记录
- 自动展开 `selectApplyResultDetailVos` 数组
- 每个中选合作伙伴生成一条记录

## 🏗️ 视图结构

### 核心字段（要求的字段）
- `select_apply_id` - 甄选申请ID（来自表字段）
- `project_no` - 项目编号（从JSON提取）
- `selectApplyResultId` - 甄选申请结果ID
- `selectApplyId` - 甄选申请ID（JSON中的）
- `partnerMsgId` - 合作伙伴消息ID
- `partnerName` - 合作伙伴名称
- `decideResultValue` - 决策结果值（固定为"中选"）

### 扩展字段
- `bidMoneyValue` - 投标金额
- `contactsName` - 联系人姓名
- `contactsPhone` - 联系电话
- `contactsEmail` - 联系邮箱
- `partnerType` - 合作伙伴类型
- `reviewScore` - 评审分数
- `businessScore` - 商务分数
- `technologyScore` - 技术分数
- `selectResultNumber` - 甄选结果编号

### 元数据字段
- `id` - 原始记录ID
- `created_at` - 创建时间
- `updated_at` - 更新时间

## 📈 数据统计

### 总体统计
- **总记录数**: 815条中选合作伙伴记录
- **涉及项目数**: 743个项目
- **唯一合作伙伴数**: 238家合作伙伴

### 数据完整性
- **申请ID一致性**: 100% (811/811)
- **全部为中选**: 100% (811/811)
- **有投标金额**: 100% (805/805)
- **联系信息完整性**: 100%
  - 联系人姓名: 815/815 (100%)
  - 联系电话: 815/815 (100%)
  - 联系邮箱: 815/815 (100%)

## 🏆 合作伙伴中选排行

### 中选次数最多的合作伙伴
1. **中移建设有限公司广东分公司** - 52次中选
2. **广东民新通信科技有限公司** - 31次中选
3. **广东新逸达科技有限公司** - 25次中选
4. **中山市云锦成科技有限公司** - 23次中选
5. **广东合盛智能技术有限公司** - 16次中选
6. **广东凌臣科技有限公司** - 16次中选
7. **宜通世纪科技股份有限公司** - 15次中选
8. **中山市宇信科技有限公司** - 12次中选
9. **中山市双立信息科技有限公司** - 11次中选
10. **中山市网建通信工程有限公司** - 11次中选

## 💡 使用示例

### 1. 基本查询 - 获取中选合作伙伴信息
```sql
SELECT 
    select_apply_id, project_no, selectApplyResultId, selectApplyId,
    partnerMsgId, partnerName, decideResultValue, bidMoneyValue
FROM v_zhenxuan_queryselectapplydetai_partner 
LIMIT 10;
```

### 2. 按合作伙伴统计中选次数
```sql
SELECT 
    partnerName,
    COUNT(*) as selected_count,
    COUNT(CASE WHEN bidMoneyValue IS NOT NULL THEN 1 END) as with_bid_amount
FROM v_zhenxuan_queryselectapplydetai_partner 
GROUP BY partnerName
ORDER BY selected_count DESC;
```

### 3. 查询特定项目的中选合作伙伴
```sql
SELECT 
    project_no, partnerName, bidMoneyValue, 
    contactsName, contactsPhone
FROM v_zhenxuan_queryselectapplydetai_partner 
WHERE project_no = 'CMGDZSICT20240715013';
```

### 4. 按年份统计中选情况
```sql
SELECT 
    SUBSTRING(project_no, 10, 4) as year,
    COUNT(*) as selected_count,
    COUNT(DISTINCT partnerMsgId) as unique_partners
FROM v_zhenxuan_queryselectapplydetai_partner 
WHERE project_no LIKE 'CMGDZSICT%'
GROUP BY year
ORDER BY year;
```

### 5. 查询合作伙伴的联系信息
```sql
SELECT 
    partnerName, contactsName, contactsPhone, contactsEmail,
    COUNT(*) as selected_count
FROM v_zhenxuan_queryselectapplydetai_partner 
GROUP BY partnerName, contactsName, contactsPhone, contactsEmail
HAVING selected_count > 5
ORDER BY selected_count DESC;
```

### 6. 投标金额分析
```sql
SELECT 
    partnerName,
    COUNT(*) as project_count,
    AVG(CAST(REPLACE(bidMoneyValue, ',', '') AS DECIMAL(15,2))) as avg_bid_amount,
    SUM(CAST(REPLACE(bidMoneyValue, ',', '') AS DECIMAL(15,2))) as total_bid_amount
FROM v_zhenxuan_queryselectapplydetai_partner 
WHERE bidMoneyValue IS NOT NULL 
AND bidMoneyValue REGEXP '^[0-9,]+(\.[0-9]+)?$'
GROUP BY partnerName
HAVING project_count >= 5
ORDER BY total_bid_amount DESC;
```

## 🔧 技术实现

### JSON_TABLE 函数使用
视图使用 MySQL 的 `JSON_TABLE` 函数来展开 JSON 数组：
```sql
JSON_TABLE(
    t2.raw_data,
    '$.resultBody.selectApplyResultDetailVos[*]' 
    COLUMNS (
        partner_info JSON PATH '$'
    )
) AS jt
```

### 筛选逻辑
```sql
WHERE JSON_UNQUOTE(JSON_EXTRACT(jt.partner_info, '$.decideResultValue')) = '中选'
```

## ⚠️ 注意事项

1. **性能考虑**: JSON 解析和数组展开可能影响查询性能
2. **数据一致性**: 确保 `select_apply_id` 与 `selectApplyId` 的一致性
3. **金额格式**: `bidMoneyValue` 包含逗号分隔符，需要处理后进行数值计算
4. **NULL 值处理**: 部分字段可能为 NULL，查询时需要考虑
5. **数组长度**: 一个申请可能有多个中选合作伙伴

## 📝 维护建议

1. **索引优化**: 考虑在 `partnerMsgId` 和 `project_no` 上创建索引
2. **数据验证**: 定期检查 `decideResultValue` 的数据质量
3. **性能监控**: 监控复杂查询的执行时间
4. **数据同步**: 确保与源表数据的同步更新

## 🚀 扩展可能

1. **物化视图**: 考虑创建物化视图提高查询性能
2. **历史追踪**: 添加中选状态变更的历史记录
3. **关联查询**: 与其他业务表进行关联分析
4. **报表支持**: 为报表系统提供标准化数据接口

## 📊 业务价值

1. **合作伙伴管理**: 快速查询合作伙伴的中选情况
2. **项目分析**: 分析项目的合作伙伴选择模式
3. **绩效评估**: 评估合作伙伴的中选率和表现
4. **决策支持**: 为后续合作伙伴选择提供数据支持
