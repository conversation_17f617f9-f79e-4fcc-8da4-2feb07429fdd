"""
字典网站自动登录脚本
使用playwright框架和ddddocr验证码识别
"""

import asyncio
import time
import base64
from io import BytesIO
from playwright.async_api import async_playwright
import ddddocr
from PIL import Image


class DictLoginBot:
    def __init__(self):
        self.url = "https://dict.gmcc.net:30722/dictWeb/login"
        self.username = "liaochulin"
        self.password = "Liaochulin147!"
        self.ocr = ddddocr.DdddOcr()

    async def login(self):
        """执行登录流程"""
        async with async_playwright() as p:
            # 启动浏览器
            browser = await p.chromium.launch(headless=False)  # 设置为False可以看到浏览器操作
            context = await browser.new_context(ignore_https_errors=True)  # 忽略HTTPS证书错误
            page = await context.new_page()

            try:
                print("正在访问登录页面...")
                await page.goto(self.url)
                await page.wait_for_load_state('networkidle')

                # 等待页面加载完成
                await asyncio.sleep(2)

                # 输入用户名
                print("输入用户名...")
                username_input = await page.wait_for_selector('input[name="username"]', timeout=10000)
                await username_input.fill(self.username)

                # 输入密码
                print("输入密码...")
                password_input = await page.wait_for_selector('input[name="password"]', timeout=10000)
                await password_input.fill(self.password)

                # 处理验证码
                await self.handle_captcha(page)

                # 点击登录按钮
                print("点击登录按钮...")
                login_button = await page.wait_for_selector('button:has-text("登录")', timeout=10000)
                await login_button.click()

                # 等待登录结果
                print("等待登录结果...")
                await asyncio.sleep(5)

                # 检查登录是否成功
                current_url = page.url
                print(f"当前URL: {current_url}")

                # 检查是否有错误提示
                try:
                    error_elements = await page.query_selector_all('.el-message--error, .error-message, .login-error')
                    if error_elements:
                        for error_elem in error_elements:
                            error_text = await error_elem.inner_text()
                            if error_text.strip():
                                print(f"❌ 登录错误: {error_text}")
                except:
                    pass

                if "login" not in current_url.lower():
                    print("✅ 登录成功！")
                    print(f"成功跳转到: {current_url}")
                else:
                    print("❌ 登录失败，仍在登录页面")
                    # 尝试重新登录
                    await self.retry_login(page)

                # 保持浏览器打开一段时间以便查看结果
                await asyncio.sleep(15)

            except Exception as e:
                print(f"登录过程中出现错误: {e}")

            finally:
                await browser.close()

    async def handle_captcha(self, page):
        """处理验证码识别和输入"""
        max_attempts = 3

        for attempt in range(max_attempts):
            try:
                print(f"第 {attempt + 1} 次尝试识别验证码...")

                # 查找验证码图片 - 使用实际的ID
                captcha_img = await page.wait_for_selector('img[id="getCodeOfPicture"]', timeout=10000)

                if not captcha_img:
                    print("未找到验证码图片")
                    return

                # 截取验证码图片
                captcha_bytes = await captcha_img.screenshot()

                # 使用ddddocr识别验证码
                captcha_text = self.ocr.classification(captcha_bytes)
                print(f"识别到的验证码: {captcha_text}")

                # 清空验证码输入框并输入新的验证码
                captcha_input = await page.wait_for_selector('input[name="code"]', timeout=10000)
                await captcha_input.fill('')  # 先清空
                await captcha_input.fill(captcha_text)

                print("验证码输入完成")
                break

            except Exception as e:
                print(f"验证码处理失败 (尝试 {attempt + 1}/{max_attempts}): {e}")
                if attempt < max_attempts - 1:
                    # 如果不是最后一次尝试，点击刷新验证码
                    try:
                        refresh_btn = await page.query_selector('img[id="getCodeOfPicture"]')
                        if refresh_btn:
                            await refresh_btn.click()
                            await asyncio.sleep(2)  # 等待新验证码加载
                    except:
                        pass
                else:
                    print("验证码识别失败，已达到最大尝试次数")

    async def retry_login(self, page):
        """重试登录"""
        print("🔄 尝试重新登录...")
        try:
            # 重新处理验证码
            await self.handle_captcha(page)

            # 再次点击登录
            login_button = await page.wait_for_selector('button:has-text("登录")', timeout=5000)
            await login_button.click()

            # 等待结果
            await asyncio.sleep(5)

            current_url = page.url
            if "login" not in current_url.lower():
                print("✅ 重试登录成功！")
                print(f"成功跳转到: {current_url}")
            else:
                print("❌ 重试登录仍然失败")

        except Exception as e:
            print(f"重试登录时出错: {e}")


async def main():
    """主函数"""
    bot = DictLoginBot()
    await bot.login()


if __name__ == "__main__":
    print("🚀 启动字典网站自动登录程序...")
    asyncio.run(main())
