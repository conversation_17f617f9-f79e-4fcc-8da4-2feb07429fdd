"""
甄选需求管理页面数据字典分析器
深度分析页面结构，提取完整的数据字典
"""

import asyncio
import json
import os
import csv
from datetime import datetime
from playwright.async_api import async_playwright


class DataDictionaryAnalyzer:
    def __init__(self):
        self.target_url = "https://dict.gmcc.net:30722/ptn/main/selectDemand"
        self.cookie_dir = "cookies"
        self.analysis_dir = "data_analysis"
        self.cookies = None
        
        # 创建分析结果目录
        if not os.path.exists(self.analysis_dir):
            os.makedirs(self.analysis_dir)
        
    def load_cookies(self):
        """加载cookies"""
        try:
            latest_cookie_file = os.path.join(self.cookie_dir, "cookies_dict_zhenxuan.json")
            
            if os.path.exists(latest_cookie_file):
                with open(latest_cookie_file, 'r', encoding='utf-8') as f:
                    self.cookies = json.load(f)
                print(f"✅ 成功加载 {len(self.cookies)} 个cookie")
                return True
            else:
                print("❌ 未找到cookies文件")
                return False
                
        except Exception as e:
            print(f"❌ 加载cookies失败: {e}")
            return False
    
    async def analyze_page_structure(self):
        """深度分析页面结构"""
        print("🔍 开始深度分析页面结构...")
        
        if not self.load_cookies():
            return False
            
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False, slow_mo=500)
            context = await browser.new_context(ignore_https_errors=True)
            
            try:
                await context.add_cookies(self.cookies)
                page = await context.new_page()
                
                print(f"🌐 访问页面: {self.target_url}")
                await page.goto(self.target_url, wait_until='networkidle')
                await asyncio.sleep(3)
                
                # 分析结果字典
                analysis_result = {
                    "page_info": {},
                    "tables": [],
                    "forms": [],
                    "buttons": [],
                    "inputs": [],
                    "data_dictionary": {}
                }
                
                # 1. 页面基本信息
                analysis_result["page_info"] = await self.analyze_page_info(page)
                
                # 2. 表格结构分析
                analysis_result["tables"] = await self.analyze_tables(page)
                
                # 3. 表单分析
                analysis_result["forms"] = await self.analyze_forms(page)
                
                # 4. 按钮分析
                analysis_result["buttons"] = await self.analyze_buttons(page)
                
                # 5. 输入框分析
                analysis_result["inputs"] = await self.analyze_inputs(page)
                
                # 6. 生成数据字典
                analysis_result["data_dictionary"] = await self.generate_data_dictionary(page)
                
                # 保存分析结果
                await self.save_analysis_result(analysis_result)
                
                # 生成数据字典文档
                await self.generate_data_dictionary_doc(analysis_result)
                
                print("✅ 页面结构分析完成")
                await asyncio.sleep(5)
                return True
                
            except Exception as e:
                print(f"❌ 分析过程中出错: {e}")
                return False
                
            finally:
                await browser.close()
    
    async def analyze_page_info(self, page):
        """分析页面基本信息"""
        print("📄 分析页面基本信息...")
        
        page_info = {
            "title": await page.title(),
            "url": page.url,
            "timestamp": datetime.now().isoformat()
        }
        
        return page_info
    
    async def analyze_tables(self, page):
        """深度分析表格结构"""
        print("📊 分析表格结构...")
        
        tables = await page.query_selector_all('table')
        table_analysis = []
        
        for i, table in enumerate(tables):
            print(f"  分析表格 {i+1}...")
            
            table_info = {
                "table_index": i + 1,
                "headers": [],
                "sample_data": [],
                "row_count": 0,
                "column_count": 0
            }
            
            # 获取表头
            header_rows = await table.query_selector_all('thead tr, tr:first-child')
            if header_rows:
                header_cells = await header_rows[0].query_selector_all('th, td')
                for cell in header_cells:
                    header_text = await cell.inner_text()
                    table_info["headers"].append(header_text.strip())
                
                table_info["column_count"] = len(table_info["headers"])
            
            # 获取数据行
            data_rows = await table.query_selector_all('tbody tr, tr')
            if not header_rows:  # 如果没有明确的表头，跳过第一行
                data_rows = data_rows[1:] if data_rows else []
            
            table_info["row_count"] = len(data_rows)
            
            # 获取前3行样本数据
            for j, row in enumerate(data_rows[:3]):
                cells = await row.query_selector_all('td, th')
                row_data = []
                for cell in cells:
                    cell_text = await cell.inner_text()
                    row_data.append(cell_text.strip())
                
                if row_data:  # 只添加非空行
                    table_info["sample_data"].append(row_data)
            
            table_analysis.append(table_info)
        
        return table_analysis
    
    async def analyze_forms(self, page):
        """分析表单结构"""
        print("📝 分析表单结构...")
        
        forms = await page.query_selector_all('form')
        form_analysis = []
        
        for i, form in enumerate(forms):
            form_info = {
                "form_index": i + 1,
                "action": await form.get_attribute('action') or "",
                "method": await form.get_attribute('method') or "",
                "inputs": []
            }
            
            # 分析表单内的输入元素
            inputs = await form.query_selector_all('input, select, textarea')
            for input_elem in inputs:
                input_info = {
                    "type": await input_elem.get_attribute('type') or "",
                    "name": await input_elem.get_attribute('name') or "",
                    "placeholder": await input_elem.get_attribute('placeholder') or "",
                    "value": await input_elem.get_attribute('value') or ""
                }
                form_info["inputs"].append(input_info)
            
            form_analysis.append(form_info)
        
        return form_analysis
    
    async def analyze_buttons(self, page):
        """分析按钮功能"""
        print("🔘 分析按钮功能...")
        
        buttons = await page.query_selector_all('button, input[type="button"], input[type="submit"]')
        button_analysis = []
        
        for i, button in enumerate(buttons):
            button_text = await button.inner_text()
            button_type = await button.get_attribute('type') or ""
            button_class = await button.get_attribute('class') or ""
            
            button_info = {
                "index": i + 1,
                "text": button_text.strip(),
                "type": button_type,
                "class": button_class
            }
            
            button_analysis.append(button_info)
        
        return button_analysis
    
    async def analyze_inputs(self, page):
        """分析输入框"""
        print("📝 分析输入框...")
        
        inputs = await page.query_selector_all('input, select, textarea')
        input_analysis = []
        
        for i, input_elem in enumerate(inputs):
            input_info = {
                "index": i + 1,
                "type": await input_elem.get_attribute('type') or "",
                "name": await input_elem.get_attribute('name') or "",
                "placeholder": await input_elem.get_attribute('placeholder') or "",
                "id": await input_elem.get_attribute('id') or "",
                "class": await input_elem.get_attribute('class') or ""
            }
            
            input_analysis.append(input_info)
        
        return input_analysis
    
    async def generate_data_dictionary(self, page):
        """生成数据字典"""
        print("📚 生成数据字典...")
        
        # 重新分析主要数据表格，获取更详细的字段信息
        tables = await page.query_selector_all('table')
        data_dictionary = {}
        
        for i, table in enumerate(tables):
            # 获取表头
            header_rows = await table.query_selector_all('thead tr, tr:first-child')
            if not header_rows:
                continue
                
            header_cells = await header_rows[0].query_selector_all('th, td')
            headers = []
            for cell in header_cells:
                header_text = await cell.inner_text()
                headers.append(header_text.strip())
            
            if not headers:
                continue
            
            # 分析数据行，推断字段类型和特征
            data_rows = await table.query_selector_all('tbody tr, tr')
            if not data_rows:
                continue
                
            # 跳过表头行
            data_rows = data_rows[1:] if len(data_rows) > 1 else []
            
            table_dict = {}
            for j, header in enumerate(headers):
                if not header:
                    continue
                    
                field_info = {
                    "field_name": header,
                    "data_type": "string",
                    "sample_values": [],
                    "unique_values": set(),
                    "max_length": 0,
                    "is_nullable": False
                }
                
                # 分析该列的数据
                for row in data_rows[:10]:  # 分析前10行
                    cells = await row.query_selector_all('td, th')
                    if j < len(cells):
                        cell_text = await cells[j].inner_text()
                        cell_text = cell_text.strip()
                        
                        if cell_text:
                            field_info["sample_values"].append(cell_text)
                            field_info["unique_values"].add(cell_text)
                            field_info["max_length"] = max(field_info["max_length"], len(cell_text))
                        else:
                            field_info["is_nullable"] = True
                
                # 推断数据类型
                field_info["data_type"] = self.infer_data_type(field_info["sample_values"])
                field_info["unique_values"] = list(field_info["unique_values"])[:10]  # 只保留前10个唯一值
                
                table_dict[header] = field_info
            
            if table_dict:
                data_dictionary[f"table_{i+1}"] = table_dict
        
        return data_dictionary
    
    def infer_data_type(self, sample_values):
        """推断数据类型"""
        if not sample_values:
            return "string"
        
        # 检查是否为日期时间
        datetime_patterns = ["2025-", "2024-", "2023-"]
        if any(any(pattern in str(value) for pattern in datetime_patterns) for value in sample_values):
            return "datetime"
        
        # 检查是否为数字
        numeric_count = 0
        for value in sample_values:
            try:
                float(str(value))
                numeric_count += 1
            except:
                pass
        
        if numeric_count > len(sample_values) * 0.8:  # 80%以上是数字
            return "numeric"
        
        # 检查是否为布尔值
        boolean_values = {"是", "否", "true", "false", "1", "0", "启用", "禁用"}
        if all(str(value).lower() in boolean_values for value in sample_values):
            return "boolean"
        
        return "string"
    
    async def save_analysis_result(self, analysis_result):
        """保存分析结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = f"{self.analysis_dir}/page_analysis_{timestamp}.json"
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, indent=2, ensure_ascii=False)
        
        print(f"💾 分析结果已保存: {result_file}")
    
    async def generate_data_dictionary_doc(self, analysis_result):
        """生成数据字典文档"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        doc_file = f"{self.analysis_dir}/data_dictionary_{timestamp}.md"
        
        with open(doc_file, 'w', encoding='utf-8') as f:
            f.write("# 甄选需求管理页面数据字典\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"页面URL: {analysis_result['page_info']['url']}\n\n")
            
            # 页面概览
            f.write("## 页面概览\n\n")
            f.write(f"- **页面标题**: {analysis_result['page_info']['title']}\n")
            f.write(f"- **表格数量**: {len(analysis_result['tables'])}\n")
            f.write(f"- **表单数量**: {len(analysis_result['forms'])}\n")
            f.write(f"- **按钮数量**: {len(analysis_result['buttons'])}\n")
            f.write(f"- **输入框数量**: {len(analysis_result['inputs'])}\n\n")
            
            # 表格结构
            f.write("## 表格结构分析\n\n")
            for table in analysis_result['tables']:
                f.write(f"### 表格 {table['table_index']}\n\n")
                f.write(f"- **行数**: {table['row_count']}\n")
                f.write(f"- **列数**: {table['column_count']}\n")
                f.write(f"- **表头**: {', '.join(table['headers'])}\n\n")
                
                if table['sample_data']:
                    f.write("**样本数据**:\n")
                    for i, row in enumerate(table['sample_data']):
                        f.write(f"{i+1}. {' | '.join(row)}\n")
                    f.write("\n")
            
            # 数据字典
            f.write("## 数据字典\n\n")
            for table_name, fields in analysis_result['data_dictionary'].items():
                f.write(f"### {table_name}\n\n")
                f.write("| 字段名 | 数据类型 | 最大长度 | 可为空 | 样本值 |\n")
                f.write("|--------|----------|----------|--------|--------|\n")
                
                for field_name, field_info in fields.items():
                    sample_values = ', '.join(field_info['sample_values'][:3])
                    f.write(f"| {field_name} | {field_info['data_type']} | {field_info['max_length']} | {'是' if field_info['is_nullable'] else '否'} | {sample_values} |\n")
                
                f.write("\n")
            
            # 按钮功能
            f.write("## 按钮功能\n\n")
            for button in analysis_result['buttons']:
                if button['text']:
                    f.write(f"- **{button['text']}** (类型: {button['type']})\n")
            f.write("\n")
            
            # 输入框
            f.write("## 输入框字段\n\n")
            for input_field in analysis_result['inputs']:
                if input_field['placeholder'] or input_field['name']:
                    name = input_field['name'] or input_field['placeholder']
                    f.write(f"- **{name}** (类型: {input_field['type']})\n")
        
        print(f"📚 数据字典文档已生成: {doc_file}")


async def main():
    """主函数"""
    analyzer = DataDictionaryAnalyzer()
    success = await analyzer.analyze_page_structure()
    
    if success:
        print("🎉 数据字典分析完成！")
    else:
        print("❌ 分析失败")


if __name__ == "__main__":
    print("🔍 甄选需求管理页面数据字典分析器")
    print("📊 将深度分析页面结构并生成完整的数据字典")
    print("=" * 60)
    asyncio.run(main())
