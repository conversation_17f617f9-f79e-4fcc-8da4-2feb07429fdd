"""
高级Cookie访问器 - 支持数据抓取和页面交互
使用Playwright框架和保存的cookies
"""

import asyncio
import json
import os
import csv
from datetime import datetime
from playwright.async_api import async_playwright


class AdvancedCookieVisitor:
    def __init__(self):
        self.target_url = "https://dict.gmcc.net:30722/ptn/main/selectDemand"
        self.cookie_dir = "cookies"
        self.data_dir = "scraped_data"
        self.cookies = None
        
        # 创建数据保存目录
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
        
    def load_latest_cookies(self):
        """加载最新的cookies"""
        try:
            latest_cookie_file = os.path.join(self.cookie_dir, "cookies_dict_zhenxuan.json")
            
            if os.path.exists(latest_cookie_file):
                print(f"📂 加载最新cookies文件: {latest_cookie_file}")
                with open(latest_cookie_file, 'r', encoding='utf-8') as f:
                    self.cookies = json.load(f)
                print(f"✅ 成功加载 {len(self.cookies)} 个cookie")
                return True
            else:
                # 查找最新的时间戳文件
                cookie_files = []
                if os.path.exists(self.cookie_dir):
                    for filename in os.listdir(self.cookie_dir):
                        if filename.startswith("cookies_zhenxuan_") and filename.endswith(".json"):
                            cookie_files.append(filename)
                
                if cookie_files:
                    cookie_files.sort(reverse=True)
                    latest_file = os.path.join(self.cookie_dir, cookie_files[0])
                    print(f"📂 加载cookies文件: {latest_file}")
                    
                    with open(latest_file, 'r', encoding='utf-8') as f:
                        self.cookies = json.load(f)
                    print(f"✅ 成功加载 {len(self.cookies)} 个cookie")
                    return True
                else:
                    print("❌ 未找到任何cookies文件")
                    return False
                    
        except Exception as e:
            print(f"❌ 加载cookies失败: {e}")
            return False
    
    async def visit_and_scrape(self, headless=False, action="view"):
        """
        使用cookies访问页面并执行指定操作
        
        Args:
            headless: 是否无头模式运行
            action: 执行的操作 ("view", "scrape", "interact")
        """
        if not self.load_latest_cookies():
            print("❌ 无法加载cookies，请先运行登录脚本")
            return False
            
        async with async_playwright() as p:
            browser = await p.chromium.launch(
                headless=headless,
                slow_mo=300,
                args=['--disable-blink-features=AutomationControlled']
            )
            
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                ignore_https_errors=True
            )
            
            try:
                # 添加cookies
                print("🍪 正在添加cookies...")
                await context.add_cookies(self.cookies)
                
                page = await context.new_page()
                
                # 访问目标页面
                print(f"🌐 访问: {self.target_url}")
                await page.goto(self.target_url, wait_until='networkidle')
                await asyncio.sleep(3)
                
                current_url = page.url
                print(f"📍 当前URL: {current_url}")
                
                if "selectDemand" not in current_url:
                    print("❌ 未能访问到目标页面，可能需要重新登录")
                    return False
                
                print("✅ 成功访问甄选需求管理页面！")
                
                # 根据指定的操作执行不同功能
                if action == "view":
                    await self.view_page_content(page)
                elif action == "scrape":
                    await self.scrape_data(page)
                elif action == "interact":
                    await self.interactive_mode(page)
                
                return True
                
            except Exception as e:
                print(f"❌ 执行过程中出现错误: {e}")
                return False
                
            finally:
                if not headless:
                    print("⏰ 浏览器将在10秒后关闭...")
                    await asyncio.sleep(10)
                await browser.close()
    
    async def view_page_content(self, page):
        """查看页面内容"""
        print("👀 查看页面内容模式")
        
        # 获取页面基本信息
        title = await page.title()
        print(f"📄 页面标题: {title}")
        
        # 检查页面元素
        await self.analyze_page_structure(page)
        
        # 截图保存
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        screenshot_path = f"{self.data_dir}/screenshot_{timestamp}.png"
        await page.screenshot(path=screenshot_path, full_page=True)
        print(f"📸 页面截图已保存: {screenshot_path}")
    
    async def scrape_data(self, page):
        """抓取页面数据"""
        print("🕷️ 数据抓取模式")
        
        try:
            # 等待表格加载
            await page.wait_for_selector('table, .el-table, .ant-table', timeout=10000)
            
            # 抓取表格数据
            tables = await page.query_selector_all('table')
            all_data = []
            
            for i, table in enumerate(tables):
                print(f"📊 处理第 {i+1} 个表格...")
                table_data = await self.extract_table_data(table)
                if table_data:
                    all_data.extend(table_data)
            
            if all_data:
                # 保存数据到CSV
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                csv_path = f"{self.data_dir}/scraped_data_{timestamp}.csv"

                # 收集所有可能的字段名
                all_fieldnames = set()
                for row in all_data:
                    all_fieldnames.update(row.keys())

                with open(csv_path, 'w', newline='', encoding='utf-8-sig') as f:
                    if all_data:
                        writer = csv.DictWriter(f, fieldnames=list(all_fieldnames))
                        writer.writeheader()
                        writer.writerows(all_data)
                
                print(f"💾 数据已保存到: {csv_path}")
                print(f"📈 共抓取 {len(all_data)} 条记录")
            else:
                print("⚠️ 未找到可抓取的表格数据")
                
        except Exception as e:
            print(f"❌ 数据抓取失败: {e}")
    
    async def extract_table_data(self, table):
        """提取表格数据"""
        try:
            rows = await table.query_selector_all('tr')
            if not rows:
                return []
            
            # 获取表头
            header_row = rows[0]
            headers = []
            header_cells = await header_row.query_selector_all('th, td')
            for cell in header_cells:
                text = await cell.inner_text()
                headers.append(text.strip())
            
            if not headers:
                return []
            
            # 获取数据行
            data = []
            for row in rows[1:]:
                cells = await row.query_selector_all('td')
                if len(cells) == len(headers):
                    row_data = {}
                    for i, cell in enumerate(cells):
                        text = await cell.inner_text()
                        row_data[headers[i]] = text.strip()
                    data.append(row_data)
            
            return data
            
        except Exception as e:
            print(f"⚠️ 提取表格数据时出错: {e}")
            return []
    
    async def interactive_mode(self, page):
        """交互模式 - 可以执行搜索、筛选等操作"""
        print("🎮 交互模式")
        
        # 查找搜索框
        search_inputs = await page.query_selector_all('input[type="text"], input[placeholder*="搜索"], input[placeholder*="查询"]')
        
        if search_inputs:
            print(f"🔍 发现 {len(search_inputs)} 个搜索框")
            
            # 示例：在第一个搜索框中输入搜索内容
            search_input = search_inputs[0]
            placeholder = await search_input.get_attribute('placeholder')
            print(f"📝 搜索框提示: {placeholder}")
            
            # 这里可以根据需要输入搜索关键词
            # await search_input.fill("搜索关键词")
            # await page.keyboard.press('Enter')
        
        # 查找按钮
        buttons = await page.query_selector_all('button')
        print(f"🔘 发现 {len(buttons)} 个按钮")
        
        # 分析按钮功能
        for i, button in enumerate(buttons[:5]):  # 只分析前5个按钮
            text = await button.inner_text()
            if text.strip():
                print(f"  按钮 {i+1}: {text.strip()}")
    
    async def analyze_page_structure(self, page):
        """分析页面结构"""
        try:
            # 统计各种元素
            elements_count = {
                '表格': len(await page.query_selector_all('table')),
                '按钮': len(await page.query_selector_all('button')),
                '输入框': len(await page.query_selector_all('input')),
                '链接': len(await page.query_selector_all('a')),
                '表单': len(await page.query_selector_all('form')),
            }
            
            print("📊 页面结构分析:")
            for element_type, count in elements_count.items():
                print(f"  {element_type}: {count} 个")
                
        except Exception as e:
            print(f"⚠️ 分析页面结构时出错: {e}")


async def main():
    """主函数"""
    visitor = AdvancedCookieVisitor()
    
    print("请选择操作模式:")
    print("1. 查看页面 (view)")
    print("2. 抓取数据 (scrape)")
    print("3. 交互模式 (interact)")
    
    # 这里可以根据需要修改默认操作
    action = "scrape"  # 默认为抓取模式
    
    success = await visitor.visit_and_scrape(headless=False, action=action)
    
    if success:
        print("🎉 操作完成！")
    else:
        print("❌ 操作失败")


if __name__ == "__main__":
    print("🚀 启动高级Cookie访问器...")
    print("🍪 支持查看、抓取、交互三种模式")
    print("=" * 60)
    asyncio.run(main())
