# 甄选信息-评审小组 本地审核跟踪历史数据获取

## 📋 功能概述

本模块用于获取甄选信息中评审小组的本地审核跟踪历史数据，通过调用 `queryLocalAuditTrackHistory` 接口，使用 `zhenxuan_querySelectApplyDetail` 表中的 `scoreRuleId` 和 `scoreOrderMsgId` 作为API参数。

## 🎯 核心特性

- ✅ **专用数据表**: `zhenxuan_queryLocalAuditTrackHistory_ps` 
- ✅ **参数来源**: 从 `zhenxuan_querySelectApplyDetail` 表获取 `scoreRuleId` 和 `scoreOrderMsgId`
- ✅ **完整字段映射**: 包含所有API返回字段和扩展字段
- ✅ **Cookie认证**: 支持从JSON文件加载Cookie进行认证
- ✅ **批量处理**: 支持全量和限量数据同步
- ✅ **数据去重**: 通过唯一约束防止重复数据

## 📊 数据结构

### API请求参数
```json
{
  "businessId": "1904002654635212800",     // 使用scoreRuleId
  "workOrderMsgId": "GD76020250326100556464322",  // 使用scoreOrderMsgId
  "stepName": ""                           // 固定为空字符串
}
```

### API响应示例
```json
{
  "busiDate": "2025-07-09 01:44:47",
  "code": "000000",
  "message": null,
  "resultBody": [
    {
      "auditProcessTrackId": "1942641026534391808",
      "businessId": "1904002654635212800",
      "stepName": "虚拟开始子流程",
      "createTime": "2025-03-26 10:05:56",
      "finishTime": "2025-03-26 10:05:57",
      "status": "通过",
      "auditHandler": "何浩明(hehaoming)",
      "auditRemark": "提交申请"
    },
    {
      "auditProcessTrackId": "1942641026513420288",
      "businessId": "1904002654635212800",
      "stepName": "甄选评审结果专家会签审核",
      "createTime": "2025-03-26 10:05:57",
      "finishTime": "2025-03-26 10:20:30",
      "status": "通过",
      "auditHandler": "梁潮辉(liangchaohui2)",
      "auditRemark": "通过"
    }
  ]
}
```

## 🗄️ 数据库表结构

### 主要字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `business_id` | VARCHAR(50) | 业务ID（使用scoreRuleId） |
| `work_order_msg_id` | VARCHAR(100) | 工单消息ID（使用scoreOrderMsgId） |
| `audit_process_track_id` | VARCHAR(50) | 审核流程跟踪ID |
| `step_name` | VARCHAR(100) | 步骤名称 |
| `status` | VARCHAR(50) | 状态 |
| `audit_handler` | VARCHAR(100) | 审核处理人 |
| `audit_remark` | TEXT | 审核备注 |
| `score_rule_id` | VARCHAR(50) | 评分规则ID |
| `score_order_msg_id` | VARCHAR(50) | 评分工单消息ID |

### 索引设计
- 业务ID索引
- 工单消息ID索引
- 审核流程跟踪ID索引
- 评分规则ID和工单消息ID复合索引
- 唯一约束防重复

## 🚀 使用方法

### 1. 创建数据库表
```bash
python create_ps_table_simple.py
```

### 2. 数据获取

#### 限量数据同步（默认10条）
```bash
python zhenxuan_queryLocalAuditTrackHistory_ps.py
```

#### 全量数据同步
```bash
python zhenxuan_queryLocalAuditTrackHistory_ps.py -all
```

#### 指定参数获取
```bash
python zhenxuan_queryLocalAuditTrackHistory_ps.py --score-rule-id "1904002654635212800" --score-order-msg-id "GD76020250326100556464322"
```

#### 自定义延迟时间
```bash
python zhenxuan_queryLocalAuditTrackHistory_ps.py -all --delay 2.0
```

### 3. 数据验证
```bash
python verify_ps_data.py
```

## 📁 文件结构

```
├── database/
│   └── create_zhenxuan_queryLocalAuditTrackHistory_ps.sql  # 数据表创建脚本
├── zhenxuan_queryLocalAuditTrackHistory_ps.py             # 主程序
├── create_ps_table_simple.py                              # 表创建工具
├── verify_ps_data.py                                      # 数据验证工具
└── README_queryLocalAuditTrackHistory_ps.md               # 本文档
```

## 🔧 配置要求

### 数据库配置
- MySQL 8.0+
- 数据库: `zhenxuandb`
- 字符集: `utf8mb4_general_ci`

### Cookie文件
- 路径: `cookies/cookies_dict_zhenxuan.json`
- 格式: JSON数组，包含name、value、path等字段

### 依赖表
- `zhenxuan_querySelectApplyDetail`: 提供scoreRuleId和scoreOrderMsgId参数

## 📊 数据统计

根据最新验证结果：
- ✅ 数据表创建成功
- ✅ 成功获取6条审核跟踪记录
- ✅ 字段完整性100%
- ✅ 包含2种步骤类型：
  - 甄选评审结果专家会签审核（5条）
  - 虚拟开始子流程（1条）
- ✅ 所有记录状态均为"通过"

## 🔄 更新Cookie

如果遇到Cookie失效或用户登录问题：
```bash
python login2zhenxuan_cookie.py
```

## ⚠️ 注意事项

1. **参数依赖**: 需要先确保 `zhenxuan_querySelectApplyDetail` 表有数据
2. **Cookie有效性**: 定期检查Cookie是否过期
3. **网络连接**: 确保能访问 `dict.gmcc.net:30722`
4. **数据去重**: 程序会自动处理重复数据
5. **请求频率**: 默认1秒间隔，避免请求过快

## 🎉 成功案例

```
2025-07-09 14:49:49 - ✅ 成功获取数据: code=000000
2025-07-09 14:49:49 - ✅ 数据转换完成: 6条记录
2025-07-09 14:49:50 - ✅ 成功插入/更新 6 条记录
2025-07-09 14:49:50 - 🎉 批量处理完成: 成功 1/1
```

## 📞 技术支持

如有问题，请检查：
1. 数据库连接配置
2. Cookie文件有效性
3. 依赖表数据完整性
4. 网络连接状态
