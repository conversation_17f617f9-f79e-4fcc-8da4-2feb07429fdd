#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析 zhenxuan_queryselectapplydetail 表中合作伙伴相关的JSON数据结构
"""

import json
import sys
import os
from database.db_config import DatabaseManager, ZHENXUAN_DB_CONFIG

def analyze_partner_data():
    """分析合作伙伴相关的JSON数据结构"""
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    if not db_manager.connect():
        print("❌ 数据库连接失败")
        return
    
    try:
        print("📋 分析合作伙伴相关的JSON数据结构")
        print("="*80)
        
        # 查找包含合作伙伴信息的记录
        sql = """
        SELECT id, select_apply_id, raw_data 
        FROM zhenxuan_queryselectapplydetail 
        WHERE raw_data IS NOT NULL 
        AND JSON_VALID(raw_data) = 1
        AND JSON_CONTAINS_PATH(raw_data, 'one', '$.resultBody.selectApplyResultDetailVos')
        LIMIT 5
        """
        
        with db_manager.get_cursor() as cursor:
            cursor.execute(sql)
            results = cursor.fetchall()
            
            if not results:
                print("❌ 没有找到包含合作伙伴信息的记录")
                return
            
            for i, result in enumerate(results, 1):
                print(f"\n📋 记录 {i} (ID: {result['id']}, 申请ID: {result['select_apply_id']})")
                
                try:
                    raw_data = json.loads(result['raw_data'])
                    result_body = raw_data.get('resultBody', {})
                    
                    # 查找可能包含合作伙伴信息的字段
                    partner_fields = [
                        'selectApplyResultDetailVos',
                        'selectApplyResultDetailVosDraft', 
                        'selectApplyResultDetailVosForDraft'
                    ]
                    
                    for field in partner_fields:
                        if field in result_body and result_body[field]:
                            print(f"\n🔍 找到字段: {field}")
                            partner_list = result_body[field]
                            
                            if isinstance(partner_list, list) and len(partner_list) > 0:
                                print(f"   数组长度: {len(partner_list)}")
                                
                                # 分析第一个元素的结构
                                first_item = partner_list[0]
                                print(f"   第一个元素的字段:")
                                for key, value in first_item.items():
                                    print(f"     - {key}: {type(value)} = {str(value)[:50]}...")
                                
                                # 查找包含"中选"的记录
                                selected_partners = []
                                for item in partner_list:
                                    if item.get('decideResultValue') == '中选':
                                        selected_partners.append(item)
                                
                                if selected_partners:
                                    print(f"\n✅ 找到 {len(selected_partners)} 个中选的合作伙伴:")
                                    for j, partner in enumerate(selected_partners, 1):
                                        print(f"     中选伙伴 {j}:")
                                        target_fields = [
                                            'selectApplyResultId', 'selectApplyId', 'partnerMsgId', 
                                            'partnerName', 'decideResultValue', 'bidMoneyValue'
                                        ]
                                        for tf in target_fields:
                                            if tf in partner:
                                                print(f"       {tf}: {partner[tf]}")
                                            else:
                                                print(f"       {tf}: ❌ 不存在")
                                else:
                                    print(f"   ⚠️ 没有找到中选的合作伙伴")
                            else:
                                print(f"   ⚠️ 字段为空或不是数组")
                    
                    # 检查项目编号
                    project_no = result_body.get('projectNo')
                    print(f"\n📋 项目编号: {project_no}")
                    
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                except Exception as e:
                    print(f"❌ 处理失败: {e}")
                
                print("-" * 60)
        
        # 统计包含中选合作伙伴的记录数量
        print(f"\n📊 统计分析:")
        
        stat_sql = """
        SELECT 
            COUNT(*) as total_records,
            SUM(CASE 
                WHEN JSON_CONTAINS_PATH(raw_data, 'one', '$.resultBody.selectApplyResultDetailVos') 
                THEN 1 ELSE 0 
            END) as has_result_detail_vos,
            SUM(CASE 
                WHEN JSON_CONTAINS_PATH(raw_data, 'one', '$.resultBody.selectApplyResultDetailVosDraft') 
                THEN 1 ELSE 0 
            END) as has_result_detail_vos_draft
        FROM zhenxuan_queryselectapplydetail 
        WHERE raw_data IS NOT NULL AND JSON_VALID(raw_data) = 1
        """
        
        with db_manager.get_cursor() as cursor:
            cursor.execute(stat_sql)
            stats = cursor.fetchone()
            
            print(f"总记录数: {stats['total_records']}")
            print(f"包含 selectApplyResultDetailVos: {stats['has_result_detail_vos']}")
            print(f"包含 selectApplyResultDetailVosDraft: {stats['has_result_detail_vos_draft']}")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    analyze_partner_data()
