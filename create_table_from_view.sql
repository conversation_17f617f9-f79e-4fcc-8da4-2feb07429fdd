-- 创建 t_zhenxuan_queryselectapplydetail_done 表
-- 基于 v_zhenxuan_queryselectapplydetail_done 视图结构

-- 删除表（如果存在）
DROP TABLE IF EXISTS t_zhenxuan_queryselectapplydetail_done;

-- 创建表结构
CREATE TABLE t_zhenxuan_queryselectapplydetail_done (
    -- 主键字段
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    
    -- 基础字段
    select_apply_id VARCHAR(50) NOT NULL COMMENT '甄选申请ID',
    request_params JSON COMMENT '请求参数',
    
    -- 甄选基本信息
    select_rev_id VARCHAR(50) COMMENT '甄选版本ID',
    project_name TEXT COMMENT '项目名称',
    customer_name TEXT COMMENT '客户名称',
    project_code VARCHAR(100) COMMENT '项目编号',
    project_no VARCHAR(100) COMMENT '项目号',
    
    -- 甄选类型相关
    select_type VARCHAR(20) COMMENT '甄选类型',
    select_name TEXT COMMENT '甄选名称',
    select_type_value VARCHAR(50) COMMENT '甄选类型值',
    project_type_value VARCHAR(50) COMMENT '项目类型值',
    
    -- 时间字段
    create_time DATETIME COMMENT '创建时间',
    start_time DATE COMMENT '开始时间',
    end_time DATE COMMENT '结束时间',
    
    -- 状态字段
    apply_status_value VARCHAR(50) COMMENT '申请状态值',
    apply_review_status_value VARCHAR(50) COMMENT '申请审核状态值',
    
    -- 业务ID字段
    review_file_business_id VARCHAR(50) COMMENT '审核文件业务ID',
    work_order_msg_id VARCHAR(100) COMMENT '工单消息ID',
    score_order_msg_id VARCHAR(100) COMMENT '评分工单消息ID',
    score_rule_id VARCHAR(50) COMMENT '评分规则ID',
    
    -- 验证和预算字段
    is_need_verification VARCHAR(10) COMMENT '是否需要验证',
    is_finish_verification VARCHAR(10) COMMENT '是否完成验证',
    non_tax_select_budget VARCHAR(50) COMMENT '非税甄选预算',
    
    -- 备注和通知字段
    action_remark TEXT COMMENT '操作备注',
    push_notice TEXT COMMENT '推送通知',
    
    -- 技术审核和投标字段
    is_technical_review VARCHAR(10) COMMENT '是否技术审核',
    bid_flag_desc VARCHAR(20) COMMENT '投标标志描述',
    bid_opening_time DATETIME COMMENT '开标时间',
    rating VARCHAR(10) COMMENT '评级',
    is_pre_review VARCHAR(10) COMMENT '是否预审',
    
    -- 结果文档字段
    select_result_doc TEXT COMMENT '甄选结果文档',
    result_input_type VARCHAR(50) COMMENT '结果输入类型',
    result_title TEXT COMMENT '结果标题',
    result_content TEXT COMMENT '结果内容',
    doc_number_sub VARCHAR(100) COMMENT '文档编号子号',
    doc_number VARCHAR(100) COMMENT '文档编号',
    
    -- 会议结果字段
    select_result_meet TEXT COMMENT '甄选结果会议',
    select_result_meet_list JSON COMMENT '甄选结果会议列表',
    
    -- 消息ID字段
    select_msg_id VARCHAR(50) COMMENT '甄选消息ID',
    realEndTime DATETIME COMMENT '实际结束时间',
    systemEndSelectTime DATETIME COMMENT '系统结束甄选时间',
    selectMsgId VARCHAR(50) COMMENT '甄选消息ID',
    
    -- 额外业务字段
    business_area VARCHAR(20) COMMENT '业务区域',
    business_area_value VARCHAR(50) COMMENT '业务区域值',
    project_type VARCHAR(20) COMMENT '项目类型',
    select_budget VARCHAR(50) COMMENT '甄选预算',
    decide_opinion TEXT COMMENT '决策意见',
    end_select_time DATETIME COMMENT '结束甄选时间',
    
    -- 合作伙伴相关字段
    selectApplyResultId VARCHAR(50) COMMENT '甄选申请结果ID',
    selectApplyId VARCHAR(50) COMMENT '甄选申请ID',
    partnerMsgId VARCHAR(50) COMMENT '合作伙伴消息ID',
    partnerName VARCHAR(200) COMMENT '合作伙伴名称',
    decideResultValue VARCHAR(20) COMMENT '决策结果值',
    bidMoneyValue VARCHAR(50) COMMENT '投标金额值',
    contactsName VARCHAR(100) COMMENT '联系人姓名',
    contactsPhone VARCHAR(50) COMMENT '联系电话',
    contactsEmail VARCHAR(100) COMMENT '联系邮箱',
    partnerType VARCHAR(20) COMMENT '合作伙伴类型',
    reviewScore VARCHAR(20) COMMENT '评审分数',
    businessScore VARCHAR(20) COMMENT '商务分数',
    technologyScore VARCHAR(20) COMMENT '技术分数',
    selectResultNumber VARCHAR(20) COMMENT '甄选结果编号',
    
    -- 原始数据和元数据
    raw_data JSON COMMENT '原始JSON数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    
    -- 索引
    INDEX idx_select_apply_id (select_apply_id),
    INDEX idx_project_code (project_code),
    INDEX idx_project_no (project_no),
    INDEX idx_partner_msg_id (partnerMsgId),
    INDEX idx_create_time (create_time),
    INDEX idx_apply_status (apply_status_value),
    INDEX idx_rating (rating),
    INDEX idx_decide_result (decideResultValue)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='甄选申请详情完整数据表';
