#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版本：创建表并导入数据，处理所有数据类型问题
"""

import sys
import os
import time
from database.db_config import DatabaseManager, ZHENXUAN_DB_CONFIG

def create_table_final():
    """最终版本创建表"""
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    if not db_manager.connect():
        print("❌ 数据库连接失败")
        return False
    
    try:
        print("🚀 最终版本：创建表并导入数据")
        print("="*80)
        
        with db_manager.get_cursor() as cursor:
            
            # 步骤1: 删除已存在的表
            print("📋 步骤1: 删除已存在的表")
            cursor.execute("DROP TABLE IF EXISTS t_zhenxuan_queryselectapplydetail_done")
            print("✅ 表删除完成")
            
            # 步骤2: 创建表结构
            print(f"\n📋 步骤2: 创建表结构")
            
            create_table_sql = """
            CREATE TABLE t_zhenxuan_queryselectapplydetail_done (
                id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
                select_apply_id VARCHAR(50) NOT NULL COMMENT '甄选申请ID',
                request_params JSON COMMENT '请求参数',
                select_rev_id TEXT COMMENT '甄选版本ID',
                project_name TEXT COMMENT '项目名称',
                customer_name TEXT COMMENT '客户名称',
                project_code VARCHAR(100) COMMENT '项目编号',
                project_no VARCHAR(100) COMMENT '项目号',
                select_type VARCHAR(20) COMMENT '甄选类型',
                select_name TEXT COMMENT '甄选名称',
                select_type_value VARCHAR(50) COMMENT '甄选类型值',
                project_type_value VARCHAR(50) COMMENT '项目类型值',
                create_time DATETIME COMMENT '创建时间',
                start_time DATE COMMENT '开始时间',
                end_time DATE COMMENT '结束时间',
                apply_status_value VARCHAR(50) COMMENT '申请状态值',
                apply_review_status_value VARCHAR(50) COMMENT '申请审核状态值',
                review_file_business_id TEXT COMMENT '审核文件业务ID',
                work_order_msg_id TEXT COMMENT '工单消息ID',
                score_order_msg_id TEXT COMMENT '评分工单消息ID',
                score_rule_id TEXT COMMENT '评分规则ID',
                is_need_verification VARCHAR(10) COMMENT '是否需要验证',
                is_finish_verification VARCHAR(10) COMMENT '是否完成验证',
                non_tax_select_budget TEXT COMMENT '非税甄选预算',
                action_remark TEXT COMMENT '操作备注',
                push_notice TEXT COMMENT '推送通知',
                is_technical_review VARCHAR(10) COMMENT '是否技术审核',
                bid_flag_desc VARCHAR(20) COMMENT '投标标志描述',
                bid_opening_time DATETIME COMMENT '开标时间',
                rating VARCHAR(10) COMMENT '评级',
                is_pre_review VARCHAR(10) COMMENT '是否预审',
                select_result_doc TEXT COMMENT '甄选结果文档',
                result_input_type VARCHAR(50) COMMENT '结果输入类型',
                result_title TEXT COMMENT '结果标题',
                result_content TEXT COMMENT '结果内容',
                doc_number_sub VARCHAR(100) COMMENT '文档编号子号',
                doc_number VARCHAR(100) COMMENT '文档编号',
                select_result_meet TEXT COMMENT '甄选结果会议',
                select_result_meet_list JSON COMMENT '甄选结果会议列表',
                select_msg_id TEXT COMMENT '甄选消息ID',
                realEndTime DATETIME COMMENT '实际结束时间',
                systemEndSelectTime DATETIME COMMENT '系统结束甄选时间',
                selectMsgId TEXT COMMENT '甄选消息ID',
                business_area VARCHAR(20) COMMENT '业务区域',
                business_area_value VARCHAR(50) COMMENT '业务区域值',
                project_type VARCHAR(20) COMMENT '项目类型',
                select_budget TEXT COMMENT '甄选预算',
                decide_opinion TEXT COMMENT '决策意见',
                end_select_time DATETIME COMMENT '结束甄选时间',
                selectApplyResultId TEXT COMMENT '甄选申请结果ID',
                selectApplyId TEXT COMMENT '甄选申请ID',
                partnerMsgId VARCHAR(100) COMMENT '合作伙伴消息ID',
                partnerName VARCHAR(200) COMMENT '合作伙伴名称',
                decideResultValue VARCHAR(20) COMMENT '决策结果值',
                bidMoneyValue VARCHAR(50) COMMENT '投标金额值',
                contactsName VARCHAR(100) COMMENT '联系人姓名',
                contactsPhone VARCHAR(50) COMMENT '联系电话',
                contactsEmail VARCHAR(100) COMMENT '联系邮箱',
                partnerType VARCHAR(20) COMMENT '合作伙伴类型',
                reviewScore VARCHAR(20) COMMENT '评审分数',
                businessScore VARCHAR(20) COMMENT '商务分数',
                technologyScore VARCHAR(20) COMMENT '技术分数',
                selectResultNumber VARCHAR(20) COMMENT '甄选结果编号',
                raw_data JSON COMMENT '原始JSON数据',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
                
                INDEX idx_select_apply_id (select_apply_id),
                INDEX idx_project_code (project_code),
                INDEX idx_project_no (project_no),
                INDEX idx_partner_msg_id (partnerMsgId),
                INDEX idx_create_time (create_time),
                INDEX idx_apply_status (apply_status_value),
                INDEX idx_rating (rating),
                INDEX idx_decide_result (decideResultValue)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='甄选申请详情完整数据表'
            """
            
            cursor.execute(create_table_sql)
            print("✅ 表结构创建完成")
            
            # 步骤3: 分批导入数据
            print(f"\n📋 步骤3: 分批导入数据")
            
            # 先获取总记录数
            cursor.execute("SELECT COUNT(*) as count FROM v_zhenxuan_queryselectapplydetail_done")
            total_count = cursor.fetchone()['count']
            print(f"总记录数: {total_count}")
            
            # 分批处理，每批100条
            batch_size = 100
            total_batches = (total_count + batch_size - 1) // batch_size
            
            start_time = time.time()
            imported_count = 0
            
            for batch in range(total_batches):
                offset = batch * batch_size
                print(f"🔄 处理第 {batch + 1}/{total_batches} 批，偏移量: {offset}")
                
                # 获取一批数据
                cursor.execute(f"""
                SELECT * FROM v_zhenxuan_queryselectapplydetail_done 
                LIMIT {batch_size} OFFSET {offset}
                """)
                
                batch_data = cursor.fetchall()
                
                if not batch_data:
                    break
                
                # 插入数据
                for row in batch_data:
                    try:
                        insert_sql = """
                        INSERT INTO t_zhenxuan_queryselectapplydetail_done (
                            select_apply_id, request_params, select_rev_id, project_name, customer_name,
                            project_code, project_no, select_type, select_name, select_type_value,
                            project_type_value, create_time, start_time, end_time, apply_status_value,
                            apply_review_status_value, review_file_business_id, work_order_msg_id,
                            score_order_msg_id, score_rule_id, is_need_verification, is_finish_verification,
                            non_tax_select_budget, action_remark, push_notice, is_technical_review,
                            bid_flag_desc, bid_opening_time, rating, is_pre_review, select_result_doc,
                            result_input_type, result_title, result_content, doc_number_sub, doc_number,
                            select_result_meet, select_result_meet_list, select_msg_id, realEndTime,
                            systemEndSelectTime, selectMsgId, business_area, business_area_value,
                            project_type, select_budget, decide_opinion, end_select_time,
                            selectApplyResultId, selectApplyId, partnerMsgId, partnerName,
                            decideResultValue, bidMoneyValue, contactsName, contactsPhone,
                            contactsEmail, partnerType, reviewScore, businessScore,
                            technologyScore, selectResultNumber, raw_data
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                        )
                        """
                        
                        # 处理DECIMAL字段，转换为字符串
                        non_tax_budget = str(row['non_tax_select_budget']) if row['non_tax_select_budget'] is not None else None
                        select_budget = str(row['select_budget']) if row['select_budget'] is not None else None
                        
                        cursor.execute(insert_sql, (
                            row['select_apply_id'], row['request_params'], row['select_rev_id'], 
                            row['project_name'], row['customer_name'], row['project_code'], 
                            row['project_no'], row['select_type'], row['select_name'], 
                            row['select_type_value'], row['project_type_value'], row['create_time'], 
                            row['start_time'], row['end_time'], row['apply_status_value'],
                            row['apply_review_status_value'], row['review_file_business_id'], 
                            row['work_order_msg_id'], row['score_order_msg_id'], row['score_rule_id'],
                            row['is_need_verification'], row['is_finish_verification'], non_tax_budget,
                            row['action_remark'], row['push_notice'], row['is_technical_review'],
                            row['bid_flag_desc'], row['bid_opening_time'], row['rating'], 
                            row['is_pre_review'], row['select_result_doc'], row['result_input_type'],
                            row['result_title'], row['result_content'], row['doc_number_sub'], 
                            row['doc_number'], row['select_result_meet'], row['select_result_meet_list'],
                            row['select_msg_id'], row['realEndTime'], row['systemEndSelectTime'], 
                            row['selectMsgId'], row['business_area'], row['business_area_value'],
                            row['project_type'], select_budget, row['decide_opinion'], 
                            row['end_select_time'], row['selectApplyResultId'], row['selectApplyId'],
                            row['partnerMsgId'], row['partnerName'], row['decideResultValue'], 
                            row['bidMoneyValue'], row['contactsName'], row['contactsPhone'],
                            row['contactsEmail'], row['partnerType'], row['reviewScore'], 
                            row['businessScore'], row['technologyScore'], row['selectResultNumber'], 
                            row['raw_data']
                        ))
                        
                        imported_count += 1
                        
                    except Exception as e:
                        print(f"⚠️ 插入记录失败: {e}")
                        continue
                
                # 每批提交一次
                db_manager.connection.commit()
                print(f"✅ 第 {batch + 1} 批完成，已导入 {imported_count} 条记录")
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✅ 数据导入完成，总计导入 {imported_count} 条记录，耗时: {duration:.2f} 秒")
            
            # 步骤4: 验证结果
            print(f"\n📋 步骤4: 验证结果")
            
            # 检查表记录数
            cursor.execute("SELECT COUNT(*) as count FROM t_zhenxuan_queryselectapplydetail_done")
            table_count = cursor.fetchone()['count']
            print(f"表记录数: {table_count}")
            print(f"视图记录数: {total_count}")
            
            # 比较记录数
            if table_count == total_count:
                print("✅ 记录数匹配，导入成功")
            else:
                print(f"⚠️ 记录数不匹配，视图:{total_count}, 表:{table_count}")
            
            # 查看几条示例数据
            print(f"\n📋 示例数据（前3条）:")
            cursor.execute("""
            SELECT 
                id, select_apply_id, project_name, customer_name, 
                apply_status_value, rating, partnerName, decideResultValue,
                non_tax_select_budget, select_budget
            FROM t_zhenxuan_queryselectapplydetail_done 
            ORDER BY id 
            LIMIT 3
            """)
            
            sample_data = cursor.fetchall()
            for i, row in enumerate(sample_data, 1):
                print(f"\n记录 {i}:")
                print(f"  ID: {row['id']}")
                print(f"  申请ID: {row['select_apply_id']}")
                print(f"  项目名称: {row['project_name'][:50]}...")
                print(f"  客户名称: {row['customer_name'][:50]}...")
                print(f"  申请状态: {row['apply_status_value']}")
                print(f"  评级: {row['rating']}")
                print(f"  合作伙伴: {row['partnerName']}")
                print(f"  决策结果: {row['decideResultValue']}")
                print(f"  非税预算: {row['non_tax_select_budget']}")
                print(f"  甄选预算: {row['select_budget']}")
        
        print(f"\n🎉 表创建和数据导入完成！")
        return True
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        return False
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    success = create_table_final()
    if success:
        print("\n✅ 所有操作成功完成！")
    else:
        print("\n❌ 操作失败！")
