"""
手动验证码输入登录脚本
用于测试用户名密码是否正确
"""

import asyncio
from playwright.async_api import async_playwright


class ManualLoginBot:
    def __init__(self):
        self.url = "https://dict.gmcc.net:30722/dictWeb/login"
        self.username = "liaochulin"
        self.password = "Liaochulin147!"
        
    async def login_with_manual_captcha(self):
        """手动输入验证码进行登录测试"""
        async with async_playwright() as p:
            # 启动浏览器（非无头模式，便于观察）
            browser = await p.chromium.launch(headless=False, slow_mo=1000)
            context = await browser.new_context(ignore_https_errors=True)  # 忽略HTTPS证书错误
            page = await context.new_page()
            
            try:
                print("🌐 正在访问登录页面...")
                await page.goto(self.url, wait_until='networkidle')
                await asyncio.sleep(2)
                
                # 输入用户名
                print("👤 输入用户名...")
                username_input = await page.wait_for_selector('input[name="username"]', timeout=10000)
                await username_input.fill(self.username)
                print(f"✅ 用户名已输入: {self.username}")
                
                # 输入密码
                print("🔒 输入密码...")
                password_input = await page.wait_for_selector('input[name="password"]', timeout=10000)
                await password_input.fill(self.password)
                print("✅ 密码已输入")
                
                # 等待用户手动输入验证码
                print("\n" + "="*60)
                print("🔍 请在浏览器中查看验证码图片")
                print("⌨️ 请手动在验证码输入框中输入正确的验证码")
                print("✋ 输入完成后，按回车键继续...")
                print("="*60)
                
                input("按回车键继续登录...")
                
                # 点击登录按钮
                print("🖱️ 点击登录按钮...")
                login_button = await page.wait_for_selector('button:has-text("登录")', timeout=10000)
                await login_button.click()
                print("✅ 登录按钮已点击")
                
                # 等待登录结果
                print("⏳ 等待登录结果...")
                await asyncio.sleep(5)
                
                # 检查登录结果
                current_url = page.url
                print(f"📍 当前URL: {current_url}")
                
                if "login" not in current_url.lower():
                    print("🎉 登录成功！")
                    print(f"✅ 成功跳转到: {current_url}")
                    
                    # 尝试获取页面标题
                    try:
                        title = await page.title()
                        print(f"📄 页面标题: {title}")
                    except:
                        pass
                        
                else:
                    print("❌ 登录失败，仍在登录页面")
                    
                    # 检查是否有错误消息
                    try:
                        await asyncio.sleep(2)
                        error_elements = await page.query_selector_all('.el-message, .error-message, [class*="error"]')
                        for error_elem in error_elements:
                            error_text = await error_elem.inner_text()
                            if error_text.strip():
                                print(f"❌ 错误信息: {error_text}")
                    except Exception as e:
                        print(f"⚠️ 检查错误信息时出错: {e}")
                
                # 保持浏览器打开以便查看
                print("\n⏰ 保持浏览器打开60秒以便查看结果...")
                await asyncio.sleep(60)
                
            except Exception as e:
                print(f"❌ 登录过程中出现错误: {e}")
                await asyncio.sleep(10)
                
            finally:
                await browser.close()
    
    async def test_page_elements(self):
        """测试页面元素是否正确"""
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)
            context = await browser.new_context(ignore_https_errors=True)  # 忽略HTTPS证书错误
            page = await context.new_page()
            
            try:
                print("🌐 正在访问登录页面...")
                await page.goto(self.url, wait_until='networkidle')
                await asyncio.sleep(3)
                
                print("\n🔍 检查页面元素...")
                
                # 检查用户名输入框
                username_input = await page.query_selector('input[name="username"]')
                if username_input:
                    placeholder = await username_input.get_attribute('placeholder')
                    print(f"✅ 用户名输入框存在，placeholder: {placeholder}")
                else:
                    print("❌ 用户名输入框不存在")
                
                # 检查密码输入框
                password_input = await page.query_selector('input[name="password"]')
                if password_input:
                    placeholder = await password_input.get_attribute('placeholder')
                    print(f"✅ 密码输入框存在，placeholder: {placeholder}")
                else:
                    print("❌ 密码输入框不存在")
                
                # 检查验证码输入框
                captcha_input = await page.query_selector('input[name="code"]')
                if captcha_input:
                    placeholder = await captcha_input.get_attribute('placeholder')
                    print(f"✅ 验证码输入框存在，placeholder: {placeholder}")
                else:
                    print("❌ 验证码输入框不存在")
                
                # 检查验证码图片
                captcha_img = await page.query_selector('img[id="getCodeOfPicture"]')
                if captcha_img:
                    src = await captcha_img.get_attribute('src')
                    print(f"✅ 验证码图片存在，src: {src[:50]}...")
                else:
                    print("❌ 验证码图片不存在")
                
                # 检查登录按钮
                login_button = await page.query_selector('button:has-text("登录")')
                if login_button:
                    button_text = await login_button.inner_text()
                    print(f"✅ 登录按钮存在，文本: {button_text}")
                else:
                    print("❌ 登录按钮不存在")
                
                print("\n⏰ 保持浏览器打开30秒以便查看...")
                await asyncio.sleep(30)
                
            except Exception as e:
                print(f"❌ 测试过程中出现错误: {e}")
                
            finally:
                await browser.close()


async def main():
    """主函数"""
    bot = ManualLoginBot()
    
    print("🚀 手动验证码登录测试程序")
    print("=" * 60)
    print("1. 测试页面元素")
    print("2. 手动输入验证码登录")
    print("=" * 60)
    
    choice = input("请选择测试模式 (1 或 2): ").strip()
    
    if choice == "1":
        await bot.test_page_elements()
    elif choice == "2":
        await bot.login_with_manual_captcha()
    else:
        print("❌ 无效选择")


if __name__ == "__main__":
    asyncio.run(main())
