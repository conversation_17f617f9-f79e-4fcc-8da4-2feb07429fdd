#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用简单的CREATE TABLE AS SELECT方式创建表
"""

import sys
import os
import time
from database.db_config import DatabaseManager, ZHENXUAN_DB_CONFIG

def create_table_simple():
    """使用CREATE TABLE AS SELECT创建表"""
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    if not db_manager.connect():
        print("❌ 数据库连接失败")
        return False
    
    try:
        print("🚀 使用CREATE TABLE AS SELECT创建表")
        print("="*80)
        
        with db_manager.get_cursor() as cursor:
            
            # 步骤1: 删除已存在的表
            print("📋 步骤1: 删除已存在的表")
            cursor.execute("DROP TABLE IF EXISTS t_zhenxuan_queryselectapplydetail_done")
            print("✅ 表删除完成")
            
            # 步骤2: 使用CREATE TABLE AS SELECT创建表
            print(f"\n📋 步骤2: 创建表并导入数据")
            start_time = time.time()
            
            create_sql = """
            CREATE TABLE t_zhenxuan_queryselectapplydetail_done AS
            SELECT 
                ROW_NUMBER() OVER (ORDER BY select_apply_id) as id,
                select_apply_id,
                request_params,
                select_rev_id,
                project_name,
                customer_name,
                project_code,
                project_no,
                select_type,
                select_name,
                select_type_value,
                project_type_value,
                create_time,
                start_time,
                end_time,
                apply_status_value,
                apply_review_status_value,
                review_file_business_id,
                work_order_msg_id,
                score_order_msg_id,
                score_rule_id,
                is_need_verification,
                is_finish_verification,
                CAST(non_tax_select_budget AS CHAR) as non_tax_select_budget,
                action_remark,
                push_notice,
                is_technical_review,
                bid_flag_desc,
                bid_opening_time,
                rating,
                is_pre_review,
                select_result_doc,
                result_input_type,
                result_title,
                result_content,
                doc_number_sub,
                doc_number,
                select_result_meet,
                select_result_meet_list,
                select_msg_id,
                realEndTime,
                systemEndSelectTime,
                selectMsgId,
                business_area,
                business_area_value,
                project_type,
                CAST(select_budget AS CHAR) as select_budget,
                decide_opinion,
                end_select_time,
                selectApplyResultId,
                selectApplyId,
                partnerMsgId,
                partnerName,
                decideResultValue,
                bidMoneyValue,
                contactsName,
                contactsPhone,
                contactsEmail,
                partnerType,
                reviewScore,
                businessScore,
                technologyScore,
                selectResultNumber,
                raw_data,
                created_at,
                updated_at
            FROM v_zhenxuan_queryselectapplydetail_done
            """
            
            print("🔄 开始创建表...")
            cursor.execute(create_sql)
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✅ 表创建完成，耗时: {duration:.2f} 秒")
            
            # 步骤3: 添加主键和索引
            print(f"\n📋 步骤3: 添加主键和索引")
            
            # 添加主键
            cursor.execute("ALTER TABLE t_zhenxuan_queryselectapplydetail_done ADD PRIMARY KEY (id)")
            print("✅ 主键添加完成")
            
            # 添加索引
            indexes = [
                "CREATE INDEX idx_select_apply_id ON t_zhenxuan_queryselectapplydetail_done (select_apply_id)",
                "CREATE INDEX idx_project_code ON t_zhenxuan_queryselectapplydetail_done (project_code)",
                "CREATE INDEX idx_project_no ON t_zhenxuan_queryselectapplydetail_done (project_no)",
                "CREATE INDEX idx_partner_msg_id ON t_zhenxuan_queryselectapplydetail_done (partnerMsgId)",
                "CREATE INDEX idx_create_time ON t_zhenxuan_queryselectapplydetail_done (create_time)",
                "CREATE INDEX idx_apply_status ON t_zhenxuan_queryselectapplydetail_done (apply_status_value)",
                "CREATE INDEX idx_rating ON t_zhenxuan_queryselectapplydetail_done (rating)",
                "CREATE INDEX idx_decide_result ON t_zhenxuan_queryselectapplydetail_done (decideResultValue)"
            ]
            
            for i, index_sql in enumerate(indexes, 1):
                try:
                    cursor.execute(index_sql)
                    print(f"✅ 索引 {i} 创建完成")
                except Exception as e:
                    print(f"⚠️ 索引 {i} 创建失败: {e}")
            
            # 提交所有操作
            db_manager.connection.commit()
            
            # 步骤4: 验证结果
            print(f"\n📋 步骤4: 验证结果")
            
            # 检查表记录数
            cursor.execute("SELECT COUNT(*) as count FROM t_zhenxuan_queryselectapplydetail_done")
            table_count = cursor.fetchone()['count']
            print(f"表记录数: {table_count}")
            
            # 检查视图记录数
            cursor.execute("SELECT COUNT(*) as count FROM v_zhenxuan_queryselectapplydetail_done")
            view_count = cursor.fetchone()['count']
            print(f"视图记录数: {view_count}")
            
            # 比较记录数
            if table_count == view_count:
                print("✅ 记录数匹配，导入成功")
            else:
                print(f"⚠️ 记录数不匹配，视图:{view_count}, 表:{table_count}")
            
            # 查看表结构
            print(f"\n📋 表结构信息:")
            cursor.execute("DESCRIBE t_zhenxuan_queryselectapplydetail_done")
            columns = cursor.fetchall()
            print(f"字段总数: {len(columns)}")
            
            # 查看几条示例数据
            print(f"\n📋 示例数据（前3条）:")
            cursor.execute("""
            SELECT 
                id, select_apply_id, project_name, customer_name, 
                apply_status_value, rating, partnerName, decideResultValue,
                non_tax_select_budget, select_budget
            FROM t_zhenxuan_queryselectapplydetail_done 
            ORDER BY id 
            LIMIT 3
            """)
            
            sample_data = cursor.fetchall()
            for i, row in enumerate(sample_data, 1):
                print(f"\n记录 {i}:")
                print(f"  ID: {row['id']}")
                print(f"  申请ID: {row['select_apply_id']}")
                print(f"  项目名称: {row['project_name'][:50]}...")
                print(f"  客户名称: {row['customer_name'][:50]}...")
                print(f"  申请状态: {row['apply_status_value']}")
                print(f"  评级: {row['rating']}")
                print(f"  合作伙伴: {row['partnerName']}")
                print(f"  决策结果: {row['decideResultValue']}")
                print(f"  非税预算: {row['non_tax_select_budget']}")
                print(f"  甄选预算: {row['select_budget']}")
            
            # 统计分析
            print(f"\n📊 数据统计分析:")
            
            # 按状态统计
            cursor.execute("""
            SELECT 
                apply_status_value,
                COUNT(*) as count
            FROM t_zhenxuan_queryselectapplydetail_done 
            GROUP BY apply_status_value
            ORDER BY count DESC
            """)
            status_stats = cursor.fetchall()
            print("按申请状态统计:")
            for stat in status_stats:
                print(f"  {stat['apply_status_value']}: {stat['count']} 条")
            
            # 按评级统计
            cursor.execute("""
            SELECT 
                rating,
                COUNT(*) as count
            FROM t_zhenxuan_queryselectapplydetail_done 
            WHERE rating IS NOT NULL
            GROUP BY rating
            ORDER BY rating
            """)
            rating_stats = cursor.fetchall()
            print("\n按评级统计:")
            for stat in rating_stats:
                print(f"  评级 {stat['rating']}: {stat['count']} 条")
        
        print(f"\n🎉 表创建和数据导入完成！")
        return True
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        return False
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    success = create_table_simple()
    if success:
        print("\n✅ 所有操作成功完成！")
    else:
        print("\n❌ 操作失败！")
