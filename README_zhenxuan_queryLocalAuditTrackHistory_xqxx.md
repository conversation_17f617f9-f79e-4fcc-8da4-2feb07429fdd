# 甄选本地审核跟踪历史数据获取程序（需求信息版本）

## 📋 项目概述

本项目实现了甄选系统中本地审核跟踪历史数据的自动获取和入库功能，特别针对需求信息（xqxx）版本的API接口。

## 🎯 功能特性

### 核心功能
- ✅ 自动从 `zhenxuan_querySelectProjectList` 表获取所有项目消息ID
- ✅ 使用项目消息ID作为 `businessId` 参数调用API
- ✅ 支持批量数据获取和单个项目数据获取
- ✅ 自动解析JSON响应并保存到MySQL数据库
- ✅ 完整的错误处理和日志记录
- ✅ 支持Cookie文件自动加载和手动更新

### 技术特性
- 🔐 **认证方式**: Cookie + Authorization Bearer
- 🗄️ **数据库**: MySQL 8.0 with utf8mb4 字符集
- 📊 **数据格式**: JSON响应自动解析
- 🔄 **去重机制**: 基于唯一约束防止重复数据
- ⏱️ **请求限流**: 1秒间隔防止过快请求
- 📝 **日志记录**: 详细的操作日志和错误追踪

## 📁 文件结构

```
├── zhenxuan_queryLocalAuditTrackHistory_xqxx.py    # 主程序
├── database/
│   └── create_zhenxuan_queryLocalAuditTrackHistory_xqxx.sql  # 数据表创建脚本
├── create_xqxx_table_simple.py                     # 简化表创建脚本
├── test_single_project.py                          # API测试脚本
├── login2zhenxuan_cookie.py                        # Cookie更新脚本
└── logs/
    └── zhenxuan_queryLocalAuditTrackHistory_xqxx.log  # 程序日志
```

## 🗄️ 数据库表结构

### 表名: `zhenxuan_querylocalaudittrackhistory_xqxx`

#### 主要字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| `id` | BIGINT | 自增主键 |
| `project_msg_id` | VARCHAR(50) | 项目消息ID（来源表+入参） |
| `business_id` | VARCHAR(50) | 业务ID（使用projectMsgId值） |
| `work_order_msg_id` | VARCHAR(100) | 工单消息ID（固定为null） |
| `step_name_filter` | VARCHAR(100) | 步骤名称过滤（固定为空字符串） |
| `audit_process_track_id` | VARCHAR(50) | 审核流程跟踪ID |
| `step_name` | VARCHAR(100) | 步骤名称 |
| `create_time` | DATETIME | 创建时间 |
| `finish_time` | DATETIME | 完成时间 |
| `status` | VARCHAR(50) | 状态 |
| `audit_handler` | VARCHAR(100) | 审核处理人 |
| `audit_remark` | TEXT | 审核备注 |
| `raw_data` | JSON | 原始JSON数据 |

#### 索引设计
- 主键索引: `id`
- 唯一约束: `(project_msg_id, business_id, audit_process_track_id)`
- 普通索引: `project_msg_id`, `business_id`, `step_name`, `status`, `create_time`
- 复合索引: `(project_msg_id, business_id)`, `(business_id, step_name)`

## 🚀 使用方法

### 1. 环境准备

确保已安装依赖：
```bash
pip install requests pymysql
```

### 2. 创建数据表

```bash
python create_xqxx_table_simple.py
```

### 3. 数据获取

#### 获取所有数据（推荐）
```bash
python zhenxuan_queryLocalAuditTrackHistory_xqxx.py -all
```

#### 获取单个项目数据
```bash
python zhenxuan_queryLocalAuditTrackHistory_xqxx.py --project-msg-id 1812755334174785536
```

#### 查询已有数据
```bash
python zhenxuan_queryLocalAuditTrackHistory_xqxx.py --query --limit 10
```

#### 更新Cookie
```bash
python zhenxuan_queryLocalAuditTrackHistory_xqxx.py --cookie "BSS-SESSION=xxx; jsession_id_4_boss=yyy"
```

### 4. Cookie更新（如需要）

如果遇到认证问题，运行登录脚本更新Cookie：
```bash
python login2zhenxuan_cookie.py
```

## 📊 API接口说明

### 接口地址
```
POST https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/queryLocalAuditTrackHistory
```

### 请求参数
```json
{
  "businessId": "项目消息ID",
  "workOrderMsgId": null,
  "stepName": ""
}
```

### 响应格式
```json
{
  "busiDate": "2025-07-09 02:10:54",
  "code": "000000",
  "message": null,
  "resultBody": [
    {
      "auditProcessTrackId": "1812755334174785537",
      "businessId": "1812755334174785536",
      "stepName": "发起甄选需求",
      "createTime": "2024-07-15 15:45:25",
      "finishTime": "2024-07-15 15:45:25",
      "status": "已处理",
      "auditHandler": "hehaoming",
      "auditRemark": null
    }
  ]
}
```

## 🔧 配置说明

### 数据库配置
- **主机**: 127.0.0.1
- **端口**: 3306
- **用户**: root
- **密码**: cmcc12345
- **数据库**: zhenxuandb
- **字符集**: utf8mb4

### Cookie配置
- **文件路径**: `cookies/cookies_dict_zhenxuan.json`
- **格式**: JSON数组，包含name、value、domain、path等字段
- **自动加载**: 程序启动时自动加载Cookie文件

## 📈 运行示例

### 成功运行日志
```
2025-07-09 18:40:49,476 - INFO - ✅ 成功加载Cookie文件
2025-07-09 18:40:49,476 - INFO - 📊 Cookie统计: 9 个原始Cookie，6 个去重Cookie
2025-07-09 18:40:49,476 - INFO - 🎯 获取单个项目数据: 1812755334174785536
2025-07-09 18:40:49,616 - INFO - ✅ 成功获取项目数据
2025-07-09 18:40:49,654 - INFO - ✅ 项目保存了 3 条审核跟踪记录
2025-07-09 18:40:49,655 - INFO - ✅ 数据获取和保存成功
```

### 查询结果示例
```
📋 查询到 3 条记录:
  1. 项目ID: 1812755334174785536
     步骤: 甄选需求发起审批-二级
     状态: 已处理-通过
     处理人: huangbo7
     创建时间: 2024-07-15 17:11:39
```

## ⚠️ 注意事项

1. **Cookie有效期**: Cookie可能会过期，需要定期更新
2. **请求频率**: 程序设置了1秒间隔，避免过快请求
3. **数据去重**: 基于唯一约束自动处理重复数据
4. **错误处理**: 遇到网络错误会自动重试
5. **日志记录**: 所有操作都有详细日志记录

## 🔍 故障排除

### 常见问题

1. **Cookie过期**
   ```bash
   python login2zhenxuan_cookie.py
   ```

2. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证数据库配置信息

3. **API请求失败**
   - 检查网络连接
   - 验证Cookie是否有效
   - 确认API接口地址正确

4. **表不存在**
   ```bash
   python create_xqxx_table_simple.py
   ```

## 📝 更新日志

### v1.0.0 (2025-07-09)
- ✅ 初始版本发布
- ✅ 实现基本的数据获取和入库功能
- ✅ 支持批量和单个项目数据获取
- ✅ 完整的错误处理和日志记录
- ✅ 自动Cookie加载和管理

## 👥 维护信息

- **开发者**: Augment Agent
- **创建时间**: 2025-07-09
- **版本**: v1.0.0
- **依赖**: Python 3.7+, requests, pymysql
