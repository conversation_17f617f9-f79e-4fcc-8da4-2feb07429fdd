"""
更新 zhenxuan_queryLocalAuditTrackHistory 表结构
添加 scoreRuleId 和 scoreOrderMsgId 字段
"""

import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

from database.db_config import ZHENXUAN_DB_CONFIG, DatabaseManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('update_audit_track_table.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def update_table_structure():
    """更新表结构"""
    
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    try:
        # 连接数据库
        if not db_manager.connect():
            logger.error("❌ 数据库连接失败")
            return False
        
        logger.info("✅ 数据库连接成功")
        
        # 检查字段是否已存在
        check_columns_sql = """
        SELECT COLUMN_NAME 
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = 'zhenxuandb' 
        AND TABLE_NAME = 'zhenxuan_queryLocalAuditTrackHistory' 
        AND COLUMN_NAME IN ('score_rule_id', 'score_order_msg_id')
        """
        
        with db_manager.get_cursor() as cursor:
            cursor.execute(check_columns_sql)
            existing_columns = [row['COLUMN_NAME'] for row in cursor.fetchall()]
            
            logger.info(f"📊 已存在的字段: {existing_columns}")
            
            # 添加字段（如果不存在）
            if 'score_rule_id' not in existing_columns:
                add_score_rule_id_sql = """
                ALTER TABLE zhenxuan_queryLocalAuditTrackHistory 
                ADD COLUMN score_rule_id VARCHAR(50) DEFAULT NULL 
                COMMENT '评分规则ID - 来源于zhenxuan_querySelectApplyDetail.scoreRuleId'
                """
                cursor.execute(add_score_rule_id_sql)
                logger.info("✅ 成功添加 score_rule_id 字段")
            else:
                logger.info("ℹ️ score_rule_id 字段已存在")
            
            if 'score_order_msg_id' not in existing_columns:
                add_score_order_msg_id_sql = """
                ALTER TABLE zhenxuan_queryLocalAuditTrackHistory 
                ADD COLUMN score_order_msg_id VARCHAR(50) DEFAULT NULL 
                COMMENT '评分工单消息ID - 来源于zhenxuan_querySelectApplyDetail.scoreOrderMsgId'
                """
                cursor.execute(add_score_order_msg_id_sql)
                logger.info("✅ 成功添加 score_order_msg_id 字段")
            else:
                logger.info("ℹ️ score_order_msg_id 字段已存在")
        
        # 检查索引是否已存在
        check_indexes_sql = """
        SELECT INDEX_NAME 
        FROM information_schema.STATISTICS 
        WHERE TABLE_SCHEMA = 'zhenxuandb' 
        AND TABLE_NAME = 'zhenxuan_queryLocalAuditTrackHistory' 
        AND INDEX_NAME IN ('idx_score_rule_id', 'idx_score_order_msg_id')
        """
        
        with db_manager.get_cursor() as cursor:
            cursor.execute(check_indexes_sql)
            existing_indexes = [row['INDEX_NAME'] for row in cursor.fetchall()]
            
            logger.info(f"📊 已存在的索引: {existing_indexes}")
            
            # 添加索引（如果不存在）
            if 'idx_score_rule_id' not in existing_indexes:
                add_score_rule_id_index_sql = """
                ALTER TABLE zhenxuan_queryLocalAuditTrackHistory 
                ADD INDEX idx_score_rule_id (score_rule_id) 
                COMMENT '评分规则ID索引'
                """
                cursor.execute(add_score_rule_id_index_sql)
                logger.info("✅ 成功添加 idx_score_rule_id 索引")
            else:
                logger.info("ℹ️ idx_score_rule_id 索引已存在")
            
            if 'idx_score_order_msg_id' not in existing_indexes:
                add_score_order_msg_id_index_sql = """
                ALTER TABLE zhenxuan_queryLocalAuditTrackHistory 
                ADD INDEX idx_score_order_msg_id (score_order_msg_id) 
                COMMENT '评分工单消息ID索引'
                """
                cursor.execute(add_score_order_msg_id_index_sql)
                logger.info("✅ 成功添加 idx_score_order_msg_id 索引")
            else:
                logger.info("ℹ️ idx_score_order_msg_id 索引已存在")
        
        # 验证表结构
        verify_sql = """
        DESCRIBE zhenxuan_queryLocalAuditTrackHistory
        """
        
        with db_manager.get_cursor() as cursor:
            cursor.execute(verify_sql)
            columns = cursor.fetchall()
            
            logger.info("📋 当前表结构:")
            for column in columns:
                logger.info(f"   {column['Field']} - {column['Type']} - {column['Comment'] if 'Comment' in column else 'N/A'}")
        
        logger.info("🎉 表结构更新完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 更新表结构失败: {e}")
        return False
    finally:
        db_manager.disconnect()

def main():
    """主函数"""
    logger.info("=" * 80)
    logger.info("🚀 更新 zhenxuan_queryLocalAuditTrackHistory 表结构")
    logger.info("=" * 80)
    
    start_time = datetime.now()
    
    try:
        success = update_table_structure()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if success:
            logger.info("=" * 80)
            logger.info("🎉 表结构更新成功！")
            logger.info(f"⏱️ 总耗时: {duration:.2f} 秒")
            logger.info("=" * 80)
        else:
            logger.error("❌ 表结构更新失败")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"❌ 程序执行异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
