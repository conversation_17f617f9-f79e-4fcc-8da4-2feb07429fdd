# 甄选需求管理页面API接口分析总结报告

## 🎯 分析目标

深度分析甄选需求管理页面加载时使用的接口，包括：
- 接口URL和请求方法
- 入参结构和字段定义
- 返回结果的字段结构
- 中英文字段映射关系

## 📊 接口概览

### 总体统计
- **总请求数**: 10个
- **API接口数**: 9个
- **静态资源**: 1个
- **成功率**: 100% (所有接口返回200状态码)

### 接口分类
1. **页面加载接口**: 1个 (主页面HTML)
2. **字典数据接口**: 3个 (下拉选项数据)
3. **业务数据接口**: 5个 (甄选项目列表查询)

## 🌐 核心API接口详解

### 1. 主数据查询接口

**接口信息**:
- **URL**: `POST /partner/materialManage/pnrSelectProject/querySelectProjectList`
- **功能**: 查询甄选项目列表
- **调用频率**: 页面加载时自动调用，搜索时手动调用

#### 📤 请求参数结构

**基础查询参数**:
```json
{
  "selecCategory": "1",           // 甄选类别 (简化版)
  "currentPage": 1,               // 当前页码
  "pageSize": 10                  // 每页大小
}
```

**完整查询参数**:
```json
{
  "selectCategory": ["1", "3"],   // 甄选类别数组
  "projectName": "",              // 项目名称 (支持模糊搜索)
  "projectNo": "",                // 项目编码
  "selectName": "",               // 需求名称
  "businessArea": "",             // 归属地市
  "selectStatus": "",             // 甄选需求状态
  "projectMsgId": "",             // 项目消息ID
  "currentPage": 1,               // 当前页码
  "pageSize": 10                  // 每页大小
}
```

#### 📥 响应结果结构

**响应格式**:
```json
{
  "busiDate": "2025-07-08 19:51:02",    // 业务日期
  "code": "000000",                     // 响应码 (000000表示成功)
  "message": null,                      // 错误消息
  "resultBody": {                       // 结果主体
    "total": 1445,                      // 总记录数
    "size": 10,                         // 每页大小
    "current": 1,                       // 当前页码
    "pages": 145,                       // 总页数
    "records": [...]                    // 数据记录数组
  }
}
```

**数据记录结构** (records数组中的每个对象):
```json
{
  "projectMsgId": "1942422593200898048",              // 项目消息ID
  "workOrderMsgId": "GD76020250708111442151796",     // 工单消息ID
  "shutOrderMsgId": null,                            // 关闭工单ID
  "selectMsgId": null,                               // 甄选消息ID
  "selectApplyId": null,                             // 甄选申请ID
  "projectName": "中山市坤鹏电子科技有限公司信息化建设项目",  // 项目名称
  "selectName": "中山移动某智慧园区项目",                // 需求名称
  "count": "0",                                      // 甄选方案数量
  "projectNo": "CMGDZSICT20250707037",               // 项目编码
  "selectType": null,                                // 甄选类型
  "selectTypeValue": null,                           // 甄选类型值
  "projectType": "10",                               // 项目类型
  "projectLabel": "54",                              // 项目标签
  "businessArea": "760",                             // 归属地市编码
  "businessAreaValue": "中山",                        // 归属地市名称
  "startTime": "2025-07-10 00:00:00",               // 开始时间
  "selectStatus": "1001",                            // 甄选需求状态编码
  "selectStatusValue": "审核通过",                     // 甄选需求状态名称
  "initiateDepartment": null,                        // 发起部门
  "createTime": "2025-07-08 11:16:49",              // 创建时间
  "isFixedSoftness": "0",                           // 是否固定软件
  "createStaff": "liuhuanxu",                       // 创建人员工号
  "createStaffValue": "刘桓旭",                       // 创建人员姓名
  "nextTodoHandler": "liuhuanxu",                   // 下一步处理人工号
  "nextTodoHandlerValue": "刘桓旭",                   // 下一步处理人姓名
  "isOperable": "0",                                // 是否可操作
  "changeType1": null,                              // 变更类型1
  "changeType2": null,                              // 变更类型2
  "isTerminable": "0",                              // 是否可终止
  "isAllowSecond": null,                            // 是否允许二次
  "selectCategory": "1",                            // 甄选类别编码
  "selectCategoryValue": "项目甄选",                  // 甄选类别名称
  "dpcsSelectSecondNegotiate": null                 // DPCS甄选二次协商
}
```

### 2. 字典数据接口

#### 2.1 甄选类别字典
- **URL**: `GET /partner/materialManage/sys/rCache/getDictListByGroupId2?groupId=200025`
- **功能**: 获取甄选类别选项

#### 2.2 地市列表接口
- **URL**: `GET /partner/materialManage/dataManage/partnerRecruitController/getCityListByType?opType=1`
- **功能**: 获取归属地市选项

#### 2.3 状态字典
- **URL**: `GET /partner/materialManage/sys/rCache/getDictListByGroupId2?groupId=2000222`
- **功能**: 获取甄选需求状态选项

**字典数据结构**:
```json
[
  {
    "dictId": "1",                    // 字典ID
    "dictName": "项目甄选",            // 字典名称
    "groupId": "200025",              // 分组ID
    "descrp": "项目甄选",             // 描述
    "otherInfo": null                 // 其他信息
  }
]
```

## 🔗 中英文字段映射关系

### 核心发现
通过深度分析API接口，我们发现了**真实的英文字段名**！与前端页面不同，后端API使用了标准的英文字段命名。

### 主要字段映射表

| 中文显示名 | API英文字段名 | 数据类型 | 说明 | 样本值 |
|-----------|-------------|---------|------|--------|
| 序号 | - | - | 前端生成，非API字段 | 1, 2, 3 |
| 项目名称 | `projectName` | String | 项目完整名称 | "中山市坤鹏电子科技有限公司信息化建设项目" |
| 项目编码 | `projectNo` | String | 项目唯一编码 | "CMGDZSICT20250707037" |
| 需求名称 | `selectName` | String | 甄选需求名称 | "中山移动某智慧园区项目" |
| 甄选方案数量 | `count` | String | 方案数量 | "0", "1" |
| 需求编码 | `projectMsgId` | String | 项目消息ID | "1942422593200898048" |
| 甄选类别 | `selectCategoryValue` | String | 甄选类别显示值 | "项目甄选" |
| 归属地市 | `businessAreaValue` | String | 地市显示值 | "中山" |
| 创建时间 | `createTime` | String | 记录创建时间 | "2025-07-08 11:16:49" |
| 甄选需求状态 | `selectStatusValue` | String | 状态显示值 | "审核通过" |

### 扩展字段映射

| 中文含义 | API英文字段名 | 数据类型 | 说明 |
|---------|-------------|---------|------|
| 甄选类别编码 | `selectCategory` | String | 类别编码值 |
| 归属地市编码 | `businessArea` | String | 地市编码值 |
| 甄选状态编码 | `selectStatus` | String | 状态编码值 |
| 工单消息ID | `workOrderMsgId` | String | 关联工单ID |
| 甄选消息ID | `selectMsgId` | String | 甄选流程ID |
| 项目类型 | `projectType` | String | 项目分类 |
| 项目标签 | `projectLabel` | String | 项目标签 |
| 开始时间 | `startTime` | String | 项目开始时间 |
| 创建人员 | `createStaff` | String | 创建人工号 |
| 创建人姓名 | `createStaffValue` | String | 创建人姓名 |

## 📋 搜索参数映射

### 前端到后端参数映射

| 前端中文字段 | 后端API参数 | 参数类型 | 说明 |
|-------------|------------|---------|------|
| 项目名称 | `projectName` | String | 支持模糊搜索 |
| 项目编码 | `projectNo` | String | 精确匹配 |
| 需求名称 | `selectName` | String | 支持模糊搜索 |
| 甄选类别 | `selectCategory` | Array | 多选数组 |
| 归属地市 | `businessArea` | String | 地市编码 |
| 甄选需求状态 | `selectStatus` | String | 状态编码 |
| 需求编码 | `projectMsgId` | String | 项目消息ID |

## 🔧 技术实现细节

### 分页机制
- **参数**: `currentPage` (当前页), `pageSize` (每页大小)
- **响应**: `total` (总数), `pages` (总页数), `current` (当前页)

### 编码值与显示值
系统采用"编码+显示值"的双字段模式：
- 编码字段：用于数据传输和存储 (如 `selectStatus`: "1001")
- 显示值字段：用于前端显示 (如 `selectStatusValue`: "审核通过")

### 数据类型特点
- 大部分字段使用String类型，包括数字类型的字段
- 时间字段使用标准格式："YYYY-MM-DD HH:mm:ss"
- 空值统一使用null表示

## 🎯 总结与建议

### 主要发现
1. **✅ 发现英文字段名**: 后端API使用标准英文字段命名
2. **✅ 完整字段映射**: 建立了中英文字段的完整映射关系
3. **✅ 数据结构清晰**: API响应结构规范，字段定义明确
4. **✅ 编码规范**: 采用编码+显示值的标准模式

### 技术架构
- **前端**: Vue.js + Element UI (中文显示)
- **后端**: RESTful API (英文字段名)
- **数据传输**: JSON格式，标准HTTP协议

### 应用建议
1. **API集成**: 可直接使用发现的英文字段名进行API调用
2. **数据映射**: 建议建立前后端字段映射配置
3. **系统对接**: 外部系统可基于这些API进行数据集成
4. **文档维护**: 建议维护API字段的中英文对照文档

---

**重要结论**: 通过API接口分析，我们成功发现了甄选需求管理系统的**真实英文字段名**，这些字段名在后端API中使用，与前端的中文显示形成了完整的映射关系。
