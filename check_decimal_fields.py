#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查视图中DECIMAL字段的数据类型和值
"""

import sys
import os
from database.db_config import DatabaseManager, ZHENXUAN_DB_CONFIG

def check_decimal_fields():
    """检查DECIMAL字段"""
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    if not db_manager.connect():
        print("❌ 数据库连接失败")
        return
    
    try:
        print("📋 检查视图中的DECIMAL字段")
        print("="*80)
        
        with db_manager.get_cursor() as cursor:
            # 查看视图结构中的DECIMAL字段
            cursor.execute("DESCRIBE v_zhenxuan_queryselectapplydetail_done")
            columns = cursor.fetchall()
            
            decimal_fields = [col for col in columns if 'decimal' in col['Type'].lower()]
            print(f"找到 {len(decimal_fields)} 个DECIMAL字段:")
            for col in decimal_fields:
                print(f"  {col['Field']}: {col['Type']}")
            
            # 检查这些字段的实际数据
            for col in decimal_fields:
                field_name = col['Field']
                print(f"\n📊 检查字段: {field_name}")
                
                # 查看数据类型和样例值
                cursor.execute(f"""
                SELECT 
                    {field_name},
                    TYPEOF({field_name}) as data_type
                FROM v_zhenxuan_queryselectapplydetail_done 
                WHERE {field_name} IS NOT NULL 
                LIMIT 5
                """)
                
                try:
                    results = cursor.fetchall()
                    if results:
                        print(f"  样例数据:")
                        for i, row in enumerate(results, 1):
                            print(f"    {i}. 值: {row[field_name]}, 类型: {row.get('data_type', 'unknown')}")
                    else:
                        print(f"  ❌ 没有非NULL数据")
                except Exception as e:
                    print(f"  ❌ 查询失败: {e}")
                    
                    # 尝试简单查询
                    try:
                        cursor.execute(f"""
                        SELECT {field_name}
                        FROM v_zhenxuan_queryselectapplydetail_done 
                        WHERE {field_name} IS NOT NULL 
                        LIMIT 3
                        """)
                        results = cursor.fetchall()
                        print(f"  样例值:")
                        for i, row in enumerate(results, 1):
                            print(f"    {i}. {row[field_name]} (类型: {type(row[field_name])})")
                    except Exception as e2:
                        print(f"  ❌ 简单查询也失败: {e2}")
            
            # 尝试直接创建一个简化的表来测试
            print(f"\n🧪 测试创建简化表")
            
            # 删除测试表
            cursor.execute("DROP TABLE IF EXISTS test_decimal_table")
            
            # 创建测试表，使用TEXT类型代替DECIMAL
            cursor.execute("""
            CREATE TABLE test_decimal_table (
                id INT AUTO_INCREMENT PRIMARY KEY,
                select_apply_id VARCHAR(50),
                non_tax_select_budget TEXT,
                select_budget TEXT
            )
            """)
            
            # 测试插入数据
            cursor.execute("""
            INSERT INTO test_decimal_table (select_apply_id, non_tax_select_budget, select_budget)
            SELECT 
                select_apply_id,
                non_tax_select_budget,
                select_budget
            FROM v_zhenxuan_queryselectapplydetail_done 
            LIMIT 5
            """)
            
            # 查看插入的数据
            cursor.execute("SELECT * FROM test_decimal_table")
            test_results = cursor.fetchall()
            
            print("测试表数据:")
            for row in test_results:
                print(f"  ID: {row['id']}, 申请ID: {row['select_apply_id']}")
                print(f"    非税预算: {row['non_tax_select_budget']} (类型: {type(row['non_tax_select_budget'])})")
                print(f"    甄选预算: {row['select_budget']} (类型: {type(row['select_budget'])})")
                print()
            
            # 清理测试表
            cursor.execute("DROP TABLE test_decimal_table")
            
            db_manager.connection.commit()
            print("✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    check_decimal_fields()
