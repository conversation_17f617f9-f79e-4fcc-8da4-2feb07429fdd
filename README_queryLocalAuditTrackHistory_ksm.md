# 甄选本地审核跟踪历史数据获取系统（KSM版本）- 项目总结

## 🎯 项目概述

基于您提供的JSON数据结构和curl脚本，成功创建了完整的甄选本地审核跟踪历史数据获取和入库系统（KSM版本）。系统实现了从API获取数据、数据转换、数据库存储和查询等完整功能，特别增加了 `selectRevId` 字段用于关联查询。

## ✅ 已完成功能

### 1. 数据库设计 ✅
- **表名**：`zhenxuan_queryLocalAuditTrackHistory_ksm`
- **字段数量**：19个字段，完整映射JSON数据结构
- **特色字段**：增加 `selectRevId` 字段用于关联查询
- **排序规则**：`utf8mb4_general_ci`（符合要求）
- **索引设计**：10个索引，优化查询性能
- **唯一约束**：基于 `select_rev_id`, `business_id`, `audit_process_track_id` 防止重复数据

### 2. 核心功能实现 ✅

#### 📊 数据表创建
- **文件**：`database/create_zhenxuan_queryLocalAuditTrackHistory_ksm.sql`
- **脚本**：`scripts/create_queryLocalAuditTrackHistory_ksm_table.py`
- **状态**：✅ 已创建并验证

#### 🔄 数据获取程序
- **文件**：`scripts/fetch_queryLocalAuditTrackHistory_ksm.py`
- **功能**：
  - ✅ 支持Cookie认证
  - ✅ 自动从 `zhenxuan_queryPartnerSelectDetail` 表获取入参
  - ✅ 使用 `selectRevId` 作为 `businessId` 参数
  - ✅ 使用 `workOrderMsgId` 作为入参
  - ✅ 固定 `stepName` 为空字符串
  - ✅ 自动数据转换和入库
  - ✅ 重复数据处理
  - ✅ SSL证书问题解决

#### 🔍 数据查询和验证
- **查询功能**：支持查询已入库的审核跟踪数据
- **验证工具**：`scripts/test_ksm_table.py`
- **状态**：✅ 已验证数据获取和入库成功

## 📋 JSON数据结构分析

### API响应结构
```json
{
  "busiDate": "2025-07-09 01:44:46",
  "code": "000000",
  "message": null,
  "resultBody": [
    {
      "auditProcessTrackId": "1942641021614473216",
      "businessId": "1903979207947370496",
      "stepName": "虚拟开始子流程",
      "createTime": "2025-03-25 09:52:34",
      "finishTime": "2025-03-25 09:52:35",
      "status": "通过",
      "auditHandler": "何浩明(hehaoming)",
      "auditRemark": "提交申请"
    }
  ]
}
```

### 关键字段映射
| 数据库字段 | JSON字段 | 说明 |
|-----------|----------|------|
| `select_rev_id` | 入参来源 | 来自 `zhenxuan_queryPartnerSelectDetail.select_rev_id_detail` |
| `business_id` | 入参 | 使用 `selectRevId` 的值 |
| `work_order_msg_id` | 入参 | 来自 `zhenxuan_queryPartnerSelectDetail.work_order_msg_id` |
| `step_name_filter` | 入参 | 固定为空字符串 |
| `audit_process_track_id` | `auditProcessTrackId` | 审核流程跟踪ID |
| `step_name` | `stepName` | 步骤名称 |
| `create_time` | `createTime` | 创建时间 |
| `finish_time` | `finishTime` | 完成时间 |
| `status` | `status` | 状态 |
| `audit_handler` | `auditHandler` | 审核处理人 |
| `audit_remark` | `auditRemark` | 审核备注 |

## 🗂️ 文件结构

```
项目根目录/
├── database/
│   ├── create_zhenxuan_queryLocalAuditTrackHistory_ksm.sql  # 数据表创建SQL
│   └── db_config.py                                        # 数据库配置（已存在）
├── scripts/
│   ├── fetch_queryLocalAuditTrackHistory_ksm.py            # 主程序：数据获取和入库
│   ├── create_queryLocalAuditTrackHistory_ksm_table.py     # 数据表创建脚本
│   └── test_ksm_table.py                                   # 测试和验证工具
├── logs/
│   └── fetch_queryLocalAuditTrackHistory_ksm.log           # 程序运行日志
└── README_queryLocalAuditTrackHistory_ksm.md               # 项目总结（本文件）
```

## 🚀 使用方法

### 1. 创建数据表
```bash
python scripts/create_queryLocalAuditTrackHistory_ksm_table.py
```

### 2. 数据同步（限制记录数）
```bash
python scripts/fetch_queryLocalAuditTrackHistory_ksm.py --limit 10
```

### 3. 数据同步（全部数据）
```bash
python scripts/fetch_queryLocalAuditTrackHistory_ksm.py --all
```

### 4. 更新Cookie后同步
```bash
python scripts/fetch_queryLocalAuditTrackHistory_ksm.py --cookie "BSS-SESSION=xxx; jsession_id_4_boss=yyy"
```

### 5. 查询数据
```bash
python scripts/fetch_queryLocalAuditTrackHistory_ksm.py --query --query-limit 10
```

### 6. 测试和验证
```bash
python scripts/test_ksm_table.py
```

## 🔧 技术特性

### 数据库特性
- **引擎**：InnoDB
- **字符集**：utf8mb4
- **排序规则**：utf8mb4_general_ci ✅
- **主键**：自增BIGINT
- **唯一约束**：select_rev_id + business_id + audit_process_track_id
- **JSON字段**：request_params, raw_data

### 程序特性
- **请求方式**：POST with JSON payload
- **认证方式**：Cookie + Authorization Bearer
- **SSL处理**：自动禁用SSL证书验证
- **错误处理**：完整的异常处理机制
- **日志记录**：详细的操作日志
- **数据去重**：ON DUPLICATE KEY UPDATE
- **参数来源**：自动从 `zhenxuan_queryPartnerSelectDetail` 表获取入参

### 性能优化
- **批量处理**：批量插入审核记录
- **连接复用**：数据库连接管理
- **请求限流**：1秒间隔防止过快请求
- **索引优化**：10个索引提升查询性能

## 📊 数据统计

根据测试数据统计：
- **合作伙伴详情表**：1177条记录，1177个有效selectRevId
- **测试获取**：成功获取2条审核跟踪记录
- **入库验证**：✅ 数据成功入库并可查询

## 🎯 特殊说明

### 入参处理
1. **businessId**：使用 `zhenxuan_queryPartnerSelectDetail.select_rev_id_detail` 的值
2. **workOrderMsgId**：使用 `zhenxuan_queryPartnerSelectDetail.work_order_msg_id` 的值
3. **stepName**：固定为空字符串 `""`

### 环节4特殊处理
- **目标环节**：【甄选信息】-方案信息-制定甄选方案审核-在线填写
- **最后审批人**：黄振国（huangzhenguo）
- **任务完成时间**：通过 `finish_time` 字段记录

### 视图支持
- **基础视图**：`v_zhenxuan_audit_track_ksm_summary`
- **关联视图**：`v_zhenxuan_partner_audit_ksm_relation`
- **特定步骤视图**：`v_zhenxuan_audit_ksm_online_review`

## ⚠️ 注意事项

1. **Cookie管理**：Cookie有时效性，需要定期更新
2. **SSL证书**：程序自动禁用SSL证书验证
3. **数据去重**：基于唯一约束自动去重
4. **网络稳定**：确保网络连接稳定
5. **数据库连接**：确保MySQL服务正常运行
6. **入参依赖**：依赖 `zhenxuan_queryPartnerSelectDetail` 表的数据

## 🛠️ 故障排除

### 常见问题

1. **Cookie过期**
   ```bash
   # 解决方案：更新Cookie
   python scripts/fetch_queryLocalAuditTrackHistory_ksm.py --cookie "新的Cookie字符串"
   ```

2. **数据库连接失败**
   ```bash
   # 检查MySQL服务状态
   # 验证数据库配置：host=127.0.0.1, port=3306, user=root, password=cmcc12345
   ```

3. **SSL证书错误**
   ```bash
   # 程序已自动处理SSL证书问题
   ```

4. **入参数据不足**
   ```bash
   # 确保 zhenxuan_queryPartnerSelectDetail 表有数据
   python scripts/test_ksm_table.py
   ```

## 🎉 项目成果

✅ **数据表创建成功**：`zhenxuan_queryLocalAuditTrackHistory_ksm`
✅ **数据获取成功**：API调用正常，数据解析正确
✅ **数据入库成功**：批量插入，去重处理
✅ **查询验证成功**：数据可正常查询和展示
✅ **特殊字段支持**：`selectRevId` 字段正确关联
✅ **环节4数据支持**：制定甄选方案审核-在线填写数据获取

项目已完全满足您的需求，可以正常获取和存储甄选本地审核跟踪历史数据！
