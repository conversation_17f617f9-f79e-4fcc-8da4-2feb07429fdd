# 甄选审核跟踪历史数据获取项目

## 📋 项目概述

本项目实现了甄选审核跟踪历史数据的自动获取和入库功能，基于 `querySelectAuditTrackHistory` API接口。

## 🎯 功能特点

### 核心功能
- ✅ **完整数据映射**：严格按照JSON数据结构映射所有字段
- ✅ **嵌套数据处理**：支持 `CUR_REVIEW_LEVEL_LIST` 数组，每个审核步骤生成一条记录
- ✅ **灵活参数支持**：支持指定工单、轮询所有工单、数据查询等模式
- ✅ **Cookie管理**：自动加载和管理认证Cookie
- ✅ **错误处理**：完善的异常处理和日志记录

### 数据结构
- **基础信息**：业务日期、响应代码、消息等
- **审批信息**：起草单位、分发单位、审批时间等
- **审核步骤**：每个审核级别的详细信息（操作员、部门、状态、意见等）
- **原始数据**：完整的JSON原始数据保存

## 🗂️ 文件结构

```
项目根目录/
├── database/
│   ├── create_zhenxuan_querySelectAuditTrackHistory.sql  # 数据表创建SQL
│   └── db_config.py                                      # 数据库配置
├── scripts/
│   ├── fetch_querySelectAuditTrackHistory.py             # 主程序：数据获取和入库
│   ├── create_audit_track_table.py                       # 数据表创建脚本
│   └── test_audit_track_api.py                           # API连接测试脚本
├── cookies/
│   └── cookies_dict_zhenxuan.json                        # Cookie文件
└── README_querySelectAuditTrackHistory.md                # 项目说明（本文件）
```

## 🚀 使用方法

### 1. 创建数据表
```bash
python scripts/create_audit_track_table.py
```

### 2. 测试API连接
```bash
python scripts/test_audit_track_api.py
```

### 3. 数据同步（指定工单）
```bash
python scripts/fetch_querySelectAuditTrackHistory.py --work-order-msg-id GD76020250325095234501317
```

### 4. 数据同步（轮询所有工单）
```bash
python scripts/fetch_querySelectAuditTrackHistory.py --all
```

### 5. 查询已同步数据
```bash
# 查询所有数据（默认10条）
python scripts/fetch_querySelectAuditTrackHistory.py --query

# 查询指定工单数据
python scripts/fetch_querySelectAuditTrackHistory.py --query --work-order-msg-id GD76020250325095234501317

# 查询更多记录
python scripts/fetch_querySelectAuditTrackHistory.py --query --limit 50
```

### 6. 更新Cookie
```bash
python scripts/fetch_querySelectAuditTrackHistory.py --cookie "key1=value1; key2=value2"
```

## 📊 数据库表结构

### 主表：`zhenxuan_queryselectaudittrackhistory`

| 字段类型 | 主要字段 | 说明 |
|---------|---------|------|
| **请求参数** | `work_order_msg_id` | 工单消息ID（入参） |
| **响应基础** | `busi_date`, `code`, `message` | API响应基础信息 |
| **审批信息** | `drafting_unit`, `distribute_unit` | 起草单位、分发单位 |
| **审核步骤** | `total_seq`, `cur_riview_oper_name` | 审核序号、操作员姓名 |
| **审核详情** | `cur_review_level_name`, `riview_state` | 审核级别、审核状态 |
| **时间信息** | `end_date`, `approval_end_time` | 结束时间、审批时间 |
| **原始数据** | `raw_data`, `review_level_raw_data` | 完整JSON数据 |

### 视图：`v_zhenxuan_audit_track_summary`
简化查询视图，包含主要字段，按工单ID和审核序号排序。

## 🔧 技术实现

### API接口
- **URL**: `https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectAuditTrackHistory`
- **方法**: POST
- **参数**: `{"workOrderMsgId": "工单ID"}`
- **认证**: Cookie + Bearer Token

### 数据处理流程
1. **获取工单ID**：从 `zhenxuan_querypartnerselectdetail` 表获取所有工单ID
2. **API调用**：逐个调用API获取审核跟踪历史
3. **数据转换**：将JSON数据转换为数据库记录格式
4. **数据入库**：支持新增和更新，避免重复数据

### 特殊处理
- **时间格式**：支持多种时间格式自动转换
- **嵌套数组**：`CUR_REVIEW_LEVEL_LIST` 每个元素生成一条记录
- **JSON存储**：原始数据完整保存，便于后续分析
- **去重机制**：基于工单ID和审核序号避免重复

## ⚠️ 当前状态

### ✅ 已完成
- [x] 数据库表结构设计和创建
- [x] 完整的Python获取脚本
- [x] 数据转换和入库逻辑
- [x] Cookie管理和认证处理
- [x] 错误处理和日志记录
- [x] 查询和验证功能

### ❌ 待解决问题
- [ ] **API认证问题**：当前返回401错误，需要更新认证信息
- [ ] **Cookie更新**：可能需要重新获取有效的Cookie和Token

### 🔄 解决方案
1. **更新Cookie**：重新登录系统获取最新Cookie
2. **更新Token**：获取有效的Bearer Token
3. **测试验证**：使用测试脚本验证API连接

## 💡 使用建议

1. **认证更新**：定期更新Cookie和Token以保持API访问权限
2. **分批处理**：大量数据同步时建议分批处理，避免系统压力
3. **监控日志**：关注日志输出，及时发现和处理异常
4. **数据验证**：定期验证数据完整性和准确性

## 📞 技术支持

如需技术支持或遇到问题，请检查：
1. Cookie文件是否存在且有效
2. 数据库连接是否正常
3. API认证信息是否最新
4. 网络连接是否稳定

---

**项目状态**：✅ 代码完成，⚠️ 待认证更新  
**最后更新**：2025-07-09  
**版本**：v1.0.0
