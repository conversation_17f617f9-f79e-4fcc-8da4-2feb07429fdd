"""
使用保存的认证信息进行API测试
演示如何使用cookies和headers进行API调用
"""

import requests
import json
from auth_helper import AuthHelper


def test_api_with_auth():
    """使用认证信息测试API调用"""
    
    # 加载认证信息
    auth_helper = AuthHelper()
    if not auth_helper.load_auth_data():
        print("❌ 无法加载认证信息，请先运行登录脚本")
        return
    
    # 获取认证配置
    headers = auth_helper.get_headers()
    cookies = auth_helper.get_cookies_dict()
    
    print("🔐 使用保存的认证信息进行API测试...")
    auth_helper.print_auth_info()
    
    # 测试API端点
    base_url = "https://dict.gmcc.net:30722"
    
    # 示例API调用
    test_endpoints = [
        {
            "name": "查询甄选项目列表",
            "url": f"{base_url}/ptn/selectDemand/querySelectProjectList",
            "method": "POST",
            "data": {
                "pageNum": 1,
                "pageSize": 10
            }
        }
    ]
    
    # 创建session
    session = requests.Session()
    session.headers.update(headers)
    session.cookies.update(cookies)
    
    # 忽略SSL证书验证（如果需要）
    session.verify = False
    
    print(f"\n🚀 开始测试API调用...")
    
    for endpoint in test_endpoints:
        print(f"\n📡 测试: {endpoint['name']}")
        print(f"🌐 URL: {endpoint['url']}")
        
        try:
            if endpoint['method'].upper() == 'POST':
                response = session.post(
                    endpoint['url'],
                    json=endpoint.get('data', {}),
                    timeout=30
                )
            else:
                response = session.get(
                    endpoint['url'],
                    params=endpoint.get('data', {}),
                    timeout=30
                )
            
            print(f"📊 状态码: {response.status_code}")
            print(f"📏 响应长度: {len(response.text)} 字符")
            
            if response.status_code == 200:
                try:
                    json_data = response.json()
                    print(f"✅ JSON响应成功")
                    
                    # 打印响应结构
                    if isinstance(json_data, dict):
                        print(f"📋 响应字段: {list(json_data.keys())}")
                        
                        # 如果有resultBody，显示其结构
                        if 'resultBody' in json_data:
                            result_body = json_data['resultBody']
                            if isinstance(result_body, dict):
                                print(f"📦 resultBody字段: {list(result_body.keys())}")
                                
                                # 如果有records数组，显示记录数
                                if 'records' in result_body:
                                    records = result_body['records']
                                    if isinstance(records, list):
                                        print(f"📝 记录数量: {len(records)}")
                                        if records:
                                            print(f"🔍 第一条记录字段: {list(records[0].keys())}")
                    
                    # 保存响应到文件
                    filename = f"api_response_{endpoint['name'].replace(' ', '_')}.json"
                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(json_data, f, indent=2, ensure_ascii=False)
                    print(f"💾 响应已保存到: {filename}")
                    
                except json.JSONDecodeError:
                    print(f"⚠️ 响应不是有效的JSON格式")
                    print(f"📄 响应内容前200字符: {response.text[:200]}")
            else:
                print(f"❌ 请求失败")
                print(f"📄 错误内容: {response.text[:200]}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")
    
    print(f"\n✅ API测试完成")


def show_auth_usage_examples():
    """显示认证信息使用示例"""
    auth_helper = AuthHelper()
    if not auth_helper.load_auth_data():
        return
    
    print("\n" + "="*60)
    print("🔧 认证信息使用示例")
    print("="*60)
    
    # 示例1：requests库使用
    print("\n📚 示例1: 使用requests库")
    print("```python")
    print("import requests")
    print("from auth_helper import AuthHelper")
    print("")
    print("auth_helper = AuthHelper()")
    print("auth_helper.load_auth_data()")
    print("")
    print("headers = auth_helper.get_headers()")
    print("cookies = auth_helper.get_cookies_dict()")
    print("")
    print("response = requests.post(")
    print("    'https://dict.gmcc.net:30722/api/endpoint',")
    print("    headers=headers,")
    print("    cookies=cookies,")
    print("    json={'key': 'value'}")
    print(")")
    print("```")
    
    # 示例2：curl命令
    print("\n🖥️ 示例2: curl命令格式")
    print("```bash")
    cookie_string = auth_helper.get_cookies_string()
    headers = auth_helper.get_headers()
    
    print("curl -X POST \\")
    print("  'https://dict.gmcc.net:30722/api/endpoint' \\")
    print(f"  -H 'Cookie: {cookie_string[:100]}...' \\")
    for key, value in list(headers.items())[:3]:
        if key != 'Cookie':
            print(f"  -H '{key}: {value}' \\")
    print("  -d '{\"key\": \"value\"}'")
    print("```")
    
    print("\n" + "="*60)


if __name__ == "__main__":
    print("🧪 API认证测试工具")
    print("使用保存的cookies和headers进行API调用测试")
    print("="*60)
    
    # 显示使用示例
    show_auth_usage_examples()
    
    # 执行API测试
    test_api_with_auth()
