# BGM程序优化完成报告

## 📋 任务概述

根据用户要求，成功优化了 `zhenxuan_queryLocalAuditTrackHistory_bgm.py` 程序，主要修改了源字段配置，将原来使用 `scoreRuleId` 改为使用 `select_apply_id` 作为 `businessId` 参数。

## ✅ 已完成的优化工作

### 1. 🗄️ 数据库表结构更新

**表名**: `zhenxuan_querylocalaudittrackhistory_bgm`

**主要字段修改**:
- ✅ 增加了 `score_order_msg_id` 字段
- ✅ 更新了字段注释，明确数据来源
- ✅ 添加了新的索引：`idx_score_order_msg_id`
- ✅ 更新了复合索引：`idx_select_score_order`

**字段映射关系**:
```
API参数来源：
- businessId ← zhenxuan_querySelectApplyDetail.select_apply_id
- workOrderMsgId ← zhenxuan_querySelectApplyDetail.score_order_msg_id
- stepName ← 固定为空字符串 ""

存储字段：
- business_id: API入参businessId的值
- work_order_msg_id: API入参workOrderMsgId的值  
- select_apply_id: 源表的select_apply_id字段值
- score_order_msg_id: 源表的score_order_msg_id字段值（新增）
```

### 2. 🔧 Python程序优化

**文件**: `zhenxuan_queryLocalAuditTrackHistory_bgm.py`

**主要修改**:
- ✅ 修改了 `get_apply_detail_params()` 方法中的SQL查询
- ✅ 将 `score_rule_id` 改为 `select_apply_id` 作为 `businessId`
- ✅ 保持 `score_order_msg_id` 作为 `workOrderMsgId`
- ✅ 更新了数据插入SQL，增加 `score_order_msg_id` 字段
- ✅ 修正了表名为小写（MySQL自动转换）
- ✅ 更新了注释和文档

**关键代码变更**:
```sql
-- 修改前
SELECT DISTINCT
    score_rule_id as business_id,
    score_order_msg_id as work_order_msg_id,
    select_apply_id
FROM zhenxuan_querySelectApplyDetail
WHERE score_rule_id IS NOT NULL
AND score_order_msg_id IS NOT NULL

-- 修改后  
SELECT DISTINCT
    select_apply_id as business_id,
    score_order_msg_id as work_order_msg_id,
    select_apply_id
FROM zhenxuan_querySelectApplyDetail
WHERE select_apply_id IS NOT NULL
AND score_order_msg_id IS NOT NULL
```

### 3. 📊 数据验证

**数据源验证**:
- ✅ 确认 `zhenxuan_querySelectApplyDetail` 表存在
- ✅ 总记录数：895条
- ✅ 有效参数组合：115个
- ✅ 所有参数组合都有完整的 `select_apply_id` 和 `score_order_msg_id`

**示例数据**:
```
申请ID: 1904468953890996224
scoreOrderMsgId: GD76020250326100556464322
项目名称: 中山市小榄分局车载警务法眼系统项目
客户名称: 中山市公安局交通警察支队小榄大队
```

## 🚀 使用方法

### 1. 数据表已创建
数据表 `zhenxuan_querylocalaudittrackhistory_bgm` 已成功创建，包含所有必要字段和索引。

### 2. 运行程序
```bash
# 获取所有BGM审核跟踪历史数据
python zhenxuan_queryLocalAuditTrackHistory_bgm.py --all

# 查询已有数据
python zhenxuan_queryLocalAuditTrackHistory_bgm.py --query --limit 10

# 获取单个申请的数据
python zhenxuan_queryLocalAuditTrackHistory_bgm.py --business-id 1904468953890996224 --work-order-msg-id GD76020250326100556464322

# 更新Cookie（如需要）
python login2zhenxuan_cookie.py
```

### 3. 程序特性
- ✅ 自动从 `zhenxuan_querySelectApplyDetail` 表获取参数
- ✅ 使用 `select_apply_id` 作为 `businessId`
- ✅ 使用 `score_order_msg_id` 作为 `workOrderMsgId`
- ✅ 支持批量处理115个有效参数组合
- ✅ 完整的错误处理和日志记录
- ✅ 自动去重机制

## 📈 API接口说明

### 接口地址
```
POST https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/queryLocalAuditTrackHistory
```

### 请求参数格式
```json
{
  "businessId": "select_apply_id的值",
  "workOrderMsgId": "score_order_msg_id的值", 
  "stepName": ""
}
```

### 响应数据示例
```json
{
  "busiDate": "2025-07-09 01:44:48",
  "code": "000000", 
  "message": null,
  "resultBody": [
    {
      "auditProcessTrackId": "1942641027180314624",
      "businessId": "1904468953890996224",
      "stepName": "合作伙伴甄选结果反馈审核（有供应商应答情况下）",
      "createTime": "2025-03-27 09:48:23",
      "finishTime": "2025-03-27 09:48:24", 
      "status": "通过",
      "auditHandler": "何浩明(hehaoming)",
      "auditRemark": "提交申请"
    }
  ]
}
```

## 🔍 关键改进点

### 1. 参数来源优化
- **修改前**: 使用 `scoreRuleId` 作为 `businessId`
- **修改后**: 使用 `select_apply_id` 作为 `businessId`
- **优势**: 更直接的数据关联，避免了中间字段的依赖

### 2. 字段完整性
- **新增**: `score_order_msg_id` 字段存储
- **保持**: 原有的 `select_apply_id` 字段
- **优势**: 完整保存所有关键标识字段

### 3. 数据一致性
- **唯一约束**: `(business_id, work_order_msg_id, audit_process_track_id)`
- **索引优化**: 针对查询模式优化的复合索引
- **优势**: 防止重复数据，提高查询性能

## ⚠️ 注意事项

1. **网络连接**: 程序运行时需要稳定的网络连接到 `dict.gmcc.net:30722`
2. **Cookie有效期**: 如遇到认证问题，运行 `login2zhenxuan_cookie.py` 更新Cookie
3. **数据量**: 当前有115个有效参数组合，预计处理时间约2-3分钟
4. **请求频率**: 程序设置了适当的请求间隔，避免过快请求

## 📝 测试建议

1. **单个测试**: 先测试单个参数组合确认API正常
2. **小批量测试**: 测试前10个参数组合验证程序逻辑
3. **全量同步**: 确认无误后运行完整的数据同步

## 🎉 优化完成

所有要求的修改已完成：
- ✅ 源字段从 `scoreRuleId` 改为 `select_apply_id`
- ✅ 增加了 `score_order_msg_id` 字段存储
- ✅ 数据库表结构已更新
- ✅ Python程序已优化
- ✅ 数据验证已完成

程序现在可以正常使用 `--all` 参数进行批量数据同步！
