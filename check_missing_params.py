"""
检查zhenxuan_querySelectApplyDetail表中缺失参数的记录
"""

import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

from database.db_config import ZHENXUAN_DB_CONFIG, DatabaseManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('check_missing_params.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def check_missing_params():
    """检查缺失参数的记录"""
    
    logger.info("🚀 开始检查缺失参数的记录...")
    
    # 连接数据库
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    if not db_manager.connect():
        logger.error("❌ 数据库连接失败")
        return False
    
    try:
        with db_manager.get_connection() as conn:
            with conn.cursor() as cursor:
                
                # 1. 检查缺失scoreRuleId的记录
                logger.info("🔄 检查缺失scoreRuleId的记录...")
                cursor.execute("""
                SELECT COUNT(*) 
                FROM zhenxuan_querySelectApplyDetail
                WHERE score_rule_id IS NULL OR score_rule_id = ''
                """)
                
                missing_score_rule_id = cursor.fetchone()[0]
                logger.info(f"📊 缺失scoreRuleId的记录: {missing_score_rule_id} 条")
                
                # 2. 检查缺失scoreOrderMsgId的记录
                logger.info("🔄 检查缺失scoreOrderMsgId的记录...")
                cursor.execute("""
                SELECT COUNT(*) 
                FROM zhenxuan_querySelectApplyDetail
                WHERE score_order_msg_id IS NULL OR score_order_msg_id = ''
                """)
                
                missing_score_order_msg_id = cursor.fetchone()[0]
                logger.info(f"📊 缺失scoreOrderMsgId的记录: {missing_score_order_msg_id} 条")
                
                # 3. 查看一些缺失参数的记录样本
                logger.info("🔄 查看缺失参数的记录样本...")
                cursor.execute("""
                SELECT 
                    select_apply_id,
                    project_name,
                    customer_name,
                    score_rule_id,
                    score_order_msg_id,
                    apply_status_value,
                    created_at
                FROM zhenxuan_querySelectApplyDetail
                WHERE (score_rule_id IS NULL OR score_rule_id = '')
                   OR (score_order_msg_id IS NULL OR score_order_msg_id = '')
                ORDER BY created_at DESC
                LIMIT 10
                """)
                
                records = cursor.fetchall()
                logger.info(f"📋 缺失参数的记录样本 ({len(records)} 条):")
                
                for i, record in enumerate(records, 1):
                    select_apply_id, project_name, customer_name, score_rule_id, score_order_msg_id, apply_status_value, created_at = record
                    logger.info(f"   记录 {i}:")
                    logger.info(f"     申请ID: {select_apply_id}")
                    logger.info(f"     项目名称: {project_name}")
                    logger.info(f"     客户名称: {customer_name}")
                    logger.info(f"     scoreRuleId: {score_rule_id if score_rule_id else '❌ 缺失'}")
                    logger.info(f"     scoreOrderMsgId: {score_order_msg_id if score_order_msg_id else '❌ 缺失'}")
                    logger.info(f"     申请状态: {apply_status_value}")
                    logger.info(f"     创建时间: {created_at}")
                    logger.info("")
                
                # 4. 检查不同申请状态的分布
                logger.info("🔄 检查申请状态分布...")
                cursor.execute("""
                SELECT 
                    apply_status_value,
                    COUNT(*) as count,
                    COUNT(CASE WHEN score_rule_id IS NOT NULL AND score_rule_id != '' THEN 1 END) as has_score_rule_id,
                    COUNT(CASE WHEN score_order_msg_id IS NOT NULL AND score_order_msg_id != '' THEN 1 END) as has_score_order_msg_id
                FROM zhenxuan_querySelectApplyDetail
                GROUP BY apply_status_value
                ORDER BY count DESC
                """)
                
                status_stats = cursor.fetchall()
                logger.info(f"📊 申请状态分布:")
                for status, count, has_rule_id, has_order_id in status_stats:
                    logger.info(f"   {status}: {count}条 (有scoreRuleId: {has_rule_id}, 有scoreOrderMsgId: {has_order_id})")
                
                # 5. 检查最近的记录
                logger.info("🔄 检查最近的记录...")
                cursor.execute("""
                SELECT 
                    select_apply_id,
                    project_name,
                    score_rule_id,
                    score_order_msg_id,
                    apply_status_value,
                    created_at
                FROM zhenxuan_querySelectApplyDetail
                ORDER BY created_at DESC
                LIMIT 5
                """)
                
                recent_records = cursor.fetchall()
                logger.info(f"📋 最近的5条记录:")
                
                for i, record in enumerate(recent_records, 1):
                    select_apply_id, project_name, score_rule_id, score_order_msg_id, apply_status_value, created_at = record
                    logger.info(f"   记录 {i}:")
                    logger.info(f"     申请ID: {select_apply_id}")
                    logger.info(f"     项目名称: {project_name[:50]}..." if project_name and len(project_name) > 50 else f"     项目名称: {project_name}")
                    logger.info(f"     scoreRuleId: {score_rule_id if score_rule_id else '❌ 缺失'}")
                    logger.info(f"     scoreOrderMsgId: {score_order_msg_id if score_order_msg_id else '❌ 缺失'}")
                    logger.info(f"     申请状态: {apply_status_value}")
                    logger.info(f"     创建时间: {created_at}")
                    logger.info("")
                
                logger.info("✅ 检查完成")
                return True
                
    except Exception as e:
        logger.error(f"❌ 检查失败: {e}")
        return False
    finally:
        db_manager.disconnect()

def main():
    """主函数"""
    logger.info("=" * 80)
    logger.info("🚀 缺失参数记录检查程序")
    logger.info("=" * 80)
    
    start_time = datetime.now()
    
    try:
        success = check_missing_params()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if success:
            logger.info("=" * 80)
            logger.info("🎉 检查完成！")
            logger.info(f"⏱️ 总耗时: {duration:.2f} 秒")
            logger.info("=" * 80)
        else:
            logger.error("=" * 80)
            logger.error("❌ 检查失败！")
            logger.error(f"⏱️ 总耗时: {duration:.2f} 秒")
            logger.error("=" * 80)
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("\n⚠️ 程序被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 程序执行异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
