"""
简化版本：直接创建甄选信息-评审小组数据表
"""

import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

from database.db_config import ZHENXUAN_DB_CONFIG, DatabaseManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('create_ps_table_simple.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_ps_table():
    """创建甄选信息-评审小组数据表"""
    
    logger.info("🚀 开始创建甄选信息-评审小组数据表...")
    
    # 连接数据库
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    if not db_manager.connect():
        logger.error("❌ 数据库连接失败")
        return False
    
    try:
        with db_manager.get_connection() as conn:
            with conn.cursor() as cursor:
                
                # 1. 使用数据库
                logger.info("🔄 使用数据库 zhenxuandb...")
                cursor.execute("USE zhenxuandb")
                
                # 2. 删除表（如果存在）
                logger.info("🔄 删除旧表（如果存在）...")
                cursor.execute("DROP TABLE IF EXISTS `zhenxuan_queryLocalAuditTrackHistory_ps`")
                
                # 3. 创建表
                logger.info("🔄 创建新表...")
                create_table_sql = """
                CREATE TABLE `zhenxuan_queryLocalAuditTrackHistory_ps` (
                  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',

                  -- API请求参数（扩展字段）
                  `business_id` VARCHAR(50) NOT NULL COMMENT '业务ID（入参）- 使用scoreRuleId的值',
                  `work_order_msg_id` VARCHAR(100) NOT NULL COMMENT '工单消息ID（入参）- 使用scoreOrderMsgId的值',
                  `step_name_filter` VARCHAR(100) DEFAULT '' COMMENT '步骤名称过滤（入参）- 固定为空字符串',
                  `request_params` JSON DEFAULT NULL COMMENT '请求参数JSON',

                  -- 响应基础信息（根级字段）
                  `busi_date` DATETIME DEFAULT NULL COMMENT '业务日期 - busiDate',
                  `code` VARCHAR(20) DEFAULT NULL COMMENT '响应代码 - code',
                  `message` TEXT DEFAULT NULL COMMENT '响应消息 - message',

                  -- resultBody中的审核跟踪历史信息
                  `audit_process_track_id` VARCHAR(50) DEFAULT NULL COMMENT '审核流程跟踪ID - auditProcessTrackId',
                  `step_name` VARCHAR(100) DEFAULT NULL COMMENT '步骤名称 - stepName',
                  `create_time` DATETIME DEFAULT NULL COMMENT '创建时间 - createTime',
                  `finish_time` DATETIME DEFAULT NULL COMMENT '完成时间 - finishTime',
                  `status` VARCHAR(50) DEFAULT NULL COMMENT '状态 - status',
                  `audit_handler` VARCHAR(100) DEFAULT NULL COMMENT '审核处理人 - auditHandler',
                  `audit_remark` TEXT DEFAULT NULL COMMENT '审核备注 - auditRemark',

                  -- 新增字段：来源于zhenxuan_querySelectApplyDetail表
                  `score_rule_id` VARCHAR(50) NOT NULL COMMENT '评分规则ID - 来源于zhenxuan_querySelectApplyDetail.scoreRuleId',
                  `score_order_msg_id` VARCHAR(50) NOT NULL COMMENT '评分工单消息ID - 来源于zhenxuan_querySelectApplyDetail.scoreOrderMsgId',

                  -- 原始数据
                  `raw_data` JSON DEFAULT NULL COMMENT '原始JSON数据',

                  -- 系统字段
                  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',

                  -- 索引设计
                  INDEX `idx_business_id` (`business_id`) COMMENT '业务ID索引',
                  INDEX `idx_work_order_msg_id` (`work_order_msg_id`) COMMENT '工单消息ID索引',
                  INDEX `idx_audit_process_track_id` (`audit_process_track_id`) COMMENT '审核流程跟踪ID索引',
                  INDEX `idx_step_name` (`step_name`) COMMENT '步骤名称索引',
                  INDEX `idx_create_time` (`create_time`) COMMENT '创建时间索引',
                  INDEX `idx_status` (`status`) COMMENT '状态索引',
                  INDEX `idx_created_at` (`created_at`) COMMENT '记录创建时间索引',
                  INDEX `idx_score_rule_id` (`score_rule_id`) COMMENT '评分规则ID索引',
                  INDEX `idx_score_order_msg_id` (`score_order_msg_id`) COMMENT '评分工单消息ID索引',

                  -- 复合索引
                  INDEX `idx_business_work_order` (`business_id`, `work_order_msg_id`) COMMENT '业务ID和工单消息ID复合索引',
                  INDEX `idx_business_step` (`business_id`, `step_name`) COMMENT '业务ID和步骤名称复合索引',
                  INDEX `idx_score_rule_order` (`score_rule_id`, `score_order_msg_id`) COMMENT '评分规则ID和工单消息ID复合索引',

                  -- 唯一约束：防止重复数据
                  UNIQUE KEY `uk_audit_track_ps_unique` (`business_id`, `work_order_msg_id`, `audit_process_track_id`) COMMENT '审核跟踪唯一约束'

                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='甄选信息-评审小组 本地审核跟踪历史数据表'
                """
                
                cursor.execute(create_table_sql)
                logger.info("✅ 表创建成功")
                
                # 4. 验证表创建
                logger.info("🔄 验证表创建...")
                cursor.execute("""
                SELECT 
                    TABLE_NAME,
                    TABLE_COMMENT,
                    TABLE_COLLATION
                FROM information_schema.TABLES 
                WHERE TABLE_SCHEMA = 'zhenxuandb' 
                AND TABLE_NAME = 'zhenxuan_queryLocalAuditTrackHistory_ps'
                """)
                
                result = cursor.fetchone()
                if result:
                    logger.info(f"✅ 表验证成功: {result}")
                else:
                    logger.warning("⚠️ 表验证失败")
                
                # 5. 查看表结构
                logger.info("🔄 查看表结构...")
                cursor.execute("DESCRIBE `zhenxuan_queryLocalAuditTrackHistory_ps`")
                columns = cursor.fetchall()
                logger.info(f"📋 表结构 ({len(columns)} 个字段):")
                for col in columns:
                    logger.info(f"   {col[0]} - {col[1]} - {col[5]}")
                
                # 提交事务
                conn.commit()
                logger.info("✅ 所有操作完成")
                
                return True
                
    except Exception as e:
        logger.error(f"❌ 创建表失败: {e}")
        return False
    finally:
        db_manager.disconnect()

def main():
    """主函数"""
    logger.info("=" * 80)
    logger.info("🚀 甄选信息-评审小组数据表创建程序（简化版）")
    logger.info("=" * 80)
    
    start_time = datetime.now()
    
    try:
        success = create_ps_table()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if success:
            logger.info("=" * 80)
            logger.info("🎉 数据表创建成功！")
            logger.info(f"⏱️ 总耗时: {duration:.2f} 秒")
            logger.info("=" * 80)
        else:
            logger.error("=" * 80)
            logger.error("❌ 数据表创建失败！")
            logger.error(f"⏱️ 总耗时: {duration:.2f} 秒")
            logger.error("=" * 80)
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("\n⚠️ 程序被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 程序执行异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
