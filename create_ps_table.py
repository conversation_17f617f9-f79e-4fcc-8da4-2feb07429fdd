"""
创建甄选信息-评审小组 本地审核跟踪历史数据表
"""

import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

from database.db_config import ZHENXUAN_DB_CONFIG, DatabaseManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('create_ps_table.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_ps_table():
    """创建甄选信息-评审小组数据表"""
    
    logger.info("🚀 开始创建甄选信息-评审小组数据表...")
    
    # 读取SQL文件
    sql_file_path = os.path.join(project_root, 'database', 'create_zhenxuan_queryLocalAuditTrackHistory_ps.sql')
    
    try:
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        logger.info(f"✅ 成功读取SQL文件: {sql_file_path}")
        
        # 连接数据库
        db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)

        if not db_manager.connect():
            logger.error("❌ 数据库连接失败")
            return False

        try:
            with db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 直接执行整个SQL内容
                    logger.info("🔄 执行SQL脚本...")

                    # 分割SQL语句，但保持完整性
                    sql_parts = sql_content.split(';')
                    success_count = 0

                    for i, sql_part in enumerate(sql_parts):
                        sql_stmt = sql_part.strip()

                        # 跳过空语句和注释
                        if not sql_stmt or sql_stmt.startswith('--'):
                            continue

                        try:
                            logger.info(f"🔄 执行SQL语句 {i+1}: {sql_stmt[:50]}...")
                            cursor.execute(sql_stmt)

                            # 如果是查询语句，显示结果
                            if sql_stmt.upper().startswith('SELECT') or sql_stmt.upper().startswith('DESCRIBE'):
                                results = cursor.fetchall()
                                if results:
                                    logger.info(f"📋 查询结果 ({len(results)} 行):")
                                    for row in results[:5]:  # 只显示前5行
                                        logger.info(f"   {row}")
                                    if len(results) > 5:
                                        logger.info(f"   ... 还有 {len(results) - 5} 行")
                                else:
                                    logger.info("📋 查询结果: 无数据")
                            else:
                                logger.info(f"✅ SQL语句执行成功")

                            success_count += 1

                        except Exception as e:
                            logger.warning(f"⚠️ SQL语句执行失败: {e}")
                            logger.warning(f"SQL: {sql_stmt[:100]}...")

                    # 提交事务
                    conn.commit()
                    logger.info(f"✅ 成功执行 {success_count} 条SQL语句")
                    
                    return True
                    
        except Exception as e:
            logger.error(f"❌ 执行SQL失败: {e}")
            return False
        finally:
            db_manager.disconnect()
            
    except Exception as e:
        logger.error(f"❌ 读取SQL文件失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("=" * 80)
    logger.info("🚀 甄选信息-评审小组数据表创建程序")
    logger.info("=" * 80)
    
    start_time = datetime.now()
    
    try:
        success = create_ps_table()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if success:
            logger.info("=" * 80)
            logger.info("🎉 数据表创建成功！")
            logger.info(f"⏱️ 总耗时: {duration:.2f} 秒")
            logger.info("=" * 80)
        else:
            logger.error("=" * 80)
            logger.error("❌ 数据表创建失败！")
            logger.error(f"⏱️ 总耗时: {duration:.2f} 秒")
            logger.error("=" * 80)
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("\n⚠️ 程序被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 程序执行异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
