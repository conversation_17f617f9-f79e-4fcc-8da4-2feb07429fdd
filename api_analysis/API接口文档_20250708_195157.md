# 甄选需求管理页面API接口分析

生成时间: 2025-07-08 19:51:57

## 📊 接口概览

- **总请求数**: 10
- **总响应数**: 10
- **API请求数**: 9
- **静态资源请求数**: 1

## 🌐 API接口列表

| 序号 | 方法 | URL | 状态 |
|------|------|-----|------|
| 1 | GET | https://dict.gmcc.net:30722/ptn/main/selectDemand | 200 |
| 2 | GET | https://dict.gmcc.net:30722/partner/materialManage/sys/rCache/getDictListByGroupId2?groupId=200025 | 200 |
| 3 | GET | https://dict.gmcc.net:30722/partner/materialManage/dataManage/partnerRecruitController/getCityListByType?opType=1 | 200 |
| 4 | GET | https://dict.gmcc.net:30722/partner/materialManage/sys/rCache/getDictListByGroupId2?groupId=2000222 | 200 |
| 5 | POST | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList | 200 |
| 6 | POST | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList | 200 |
| 7 | POST | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList | 200 |
| 8 | POST | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList | 200 |
| 9 | POST | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList | 200 |

## 📋 接口详细分析

### 接口 1: GET https://dict.gmcc.net:30722/ptn/main/selectDemand

#### 📤 请求信息

- **URL**: `https://dict.gmcc.net:30722/ptn/main/selectDemand`
- **方法**: `GET`

#### 📥 响应信息

- **状态码**: 200
- **内容类型**: text/html; charset=utf-8

---

### 接口 3: GET https://dict.gmcc.net:30722/partner/materialManage/sys/rCache/getDictListByGroupId2?groupId=200025

#### 📤 请求信息

- **URL**: `https://dict.gmcc.net:30722/partner/materialManage/sys/rCache/getDictListByGroupId2?groupId=200025`
- **方法**: `GET`
- **查询参数**:
  - `groupId`: 200025

#### 📥 响应信息

- **状态码**: 200
- **内容类型**: application/json;charset=UTF-8
- **响应体结构**:
```json
{
  "type": "array",
  "length": 8,
  "item_structure": {
    "dictId": {
      "type": "str",
      "path": "[0].dictId"
    },
    "dictName": {
      "type": "str",
      "path": "[0].dictName"
    },
    "groupId": {
      "type": "str",
      "path": "[0].groupId"
    },
    "descrp": {
      "type": "str",
      "path": "[0].descrp"
    },
    "otherInfo": {
      "type": "NoneType",
      "path": "[0].otherInfo"
    }
  }
}
```

---

### 接口 4: GET https://dict.gmcc.net:30722/partner/materialManage/dataManage/partnerRecruitController/getCityListByType?opType=1

#### 📤 请求信息

- **URL**: `https://dict.gmcc.net:30722/partner/materialManage/dataManage/partnerRecruitController/getCityListByType?opType=1`
- **方法**: `GET`
- **查询参数**:
  - `opType`: 1

#### 📥 响应信息

- **状态码**: 200
- **内容类型**: application/json;charset=UTF-8
- **响应体结构**:
```json
{
  "type": "array",
  "length": 1,
  "item_structure": {
    "dictId": {
      "type": "str",
      "path": "[0].dictId"
    },
    "dictName": {
      "type": "str",
      "path": "[0].dictName"
    },
    "groupId": {
      "type": "str",
      "path": "[0].groupId"
    },
    "descrp": {
      "type": "str",
      "path": "[0].descrp"
    },
    "otherInfo": {
      "type": "NoneType",
      "path": "[0].otherInfo"
    }
  }
}
```

---

### 接口 5: GET https://dict.gmcc.net:30722/partner/materialManage/sys/rCache/getDictListByGroupId2?groupId=2000222

#### 📤 请求信息

- **URL**: `https://dict.gmcc.net:30722/partner/materialManage/sys/rCache/getDictListByGroupId2?groupId=2000222`
- **方法**: `GET`
- **查询参数**:
  - `groupId`: 2000222

#### 📥 响应信息

- **状态码**: 200
- **内容类型**: application/json;charset=UTF-8
- **响应体结构**:
```json
{
  "type": "array",
  "length": 3,
  "item_structure": {
    "dictId": {
      "type": "str",
      "path": "[0].dictId"
    },
    "dictName": {
      "type": "str",
      "path": "[0].dictName"
    },
    "groupId": {
      "type": "str",
      "path": "[0].groupId"
    },
    "descrp": {
      "type": "str",
      "path": "[0].descrp"
    },
    "otherInfo": {
      "type": "NoneType",
      "path": "[0].otherInfo"
    }
  }
}
```

---

### 接口 6: POST https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList

#### 📤 请求信息

- **URL**: `https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList`
- **方法**: `POST`
- **请求体参数**:
```json
{
  "selecCategory": "1",
  "currentPage": 1,
  "pageSize": 10
}
```
- **请求头**:
  - `content-type`: application/json;charset=UTF-8

#### 📥 响应信息

- **状态码**: 200
- **内容类型**: application/json;charset=UTF-8
- **响应体结构**:
```json
{
  "busiDate": {
    "type": "str",
    "path": "busiDate"
  },
  "code": {
    "type": "str",
    "path": "code"
  },
  "message": {
    "type": "NoneType",
    "path": "message"
  },
  "resultBody": {
    "type": "dict",
    "path": "resultBody"
  }
}
```
- **数据字段**:

| 字段名 | 数据类型 | 字段路径 | 样本值 |
|--------|----------|----------|--------|
| busiDate | str | busiDate | 2025-07-08 19:51:02 |
| code | str | code | 000000 |
| message | NoneType | message | None |
| resultBody | dict | resultBody | {'total': 1445, 'size': 10, 'current': 1, 'records... |
| total | int | resultBody.total | 1445 |
| size | int | resultBody.size | 10 |
| current | int | resultBody.current | 1 |
| records | list | resultBody.records | [{'projectMsgId': '1942422593200898048', 'workOrde... |
| projectMsgId | str | resultBody.records[0].projectMsgId | 1942422593200898048 |
| workOrderMsgId | str | resultBody.records[0].workOrderMsgId | GD76020250708111442151796 |
| shutOrderMsgId | NoneType | resultBody.records[0].shutOrderMsgId | None |
| selectMsgId | NoneType | resultBody.records[0].selectMsgId | None |
| selectApplyId | NoneType | resultBody.records[0].selectApplyId | None |
| projectName | str | resultBody.records[0].projectName | 中山市坤鹏电子科技有限公司信息化建设项目 |
| selectName | str | resultBody.records[0].selectName | 中山移动某智慧园区项目 |
| count | str | resultBody.records[0].count | 0 |
| projectNo | str | resultBody.records[0].projectNo | CMGDZSICT20250707037 |
| selectType | NoneType | resultBody.records[0].selectType | None |
| selectTypeValue | NoneType | resultBody.records[0].selectTypeValue | None |
| projectType | str | resultBody.records[0].projectType | 10 |

---

### 接口 7: POST https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList

#### 📤 请求信息

- **URL**: `https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList`
- **方法**: `POST`
- **请求体参数**:
```json
{
  "selecCategory": "1",
  "currentPage": 1,
  "pageSize": 10
}
```
- **请求头**:
  - `content-type`: application/json;charset=UTF-8

#### 📥 响应信息

- **状态码**: 200
- **内容类型**: application/json;charset=UTF-8
- **响应体结构**:
```json
{
  "busiDate": {
    "type": "str",
    "path": "busiDate"
  },
  "code": {
    "type": "str",
    "path": "code"
  },
  "message": {
    "type": "NoneType",
    "path": "message"
  },
  "resultBody": {
    "type": "dict",
    "path": "resultBody"
  }
}
```
- **数据字段**:

| 字段名 | 数据类型 | 字段路径 | 样本值 |
|--------|----------|----------|--------|
| busiDate | str | busiDate | 2025-07-08 19:51:02 |
| code | str | code | 000000 |
| message | NoneType | message | None |
| resultBody | dict | resultBody | {'total': 1445, 'size': 10, 'current': 1, 'records... |
| total | int | resultBody.total | 1445 |
| size | int | resultBody.size | 10 |
| current | int | resultBody.current | 1 |
| records | list | resultBody.records | [{'projectMsgId': '1942422593200898048', 'workOrde... |
| projectMsgId | str | resultBody.records[0].projectMsgId | 1942422593200898048 |
| workOrderMsgId | str | resultBody.records[0].workOrderMsgId | GD76020250708111442151796 |
| shutOrderMsgId | NoneType | resultBody.records[0].shutOrderMsgId | None |
| selectMsgId | NoneType | resultBody.records[0].selectMsgId | None |
| selectApplyId | NoneType | resultBody.records[0].selectApplyId | None |
| projectName | str | resultBody.records[0].projectName | 中山市坤鹏电子科技有限公司信息化建设项目 |
| selectName | str | resultBody.records[0].selectName | 中山移动某智慧园区项目 |
| count | str | resultBody.records[0].count | 0 |
| projectNo | str | resultBody.records[0].projectNo | CMGDZSICT20250707037 |
| selectType | NoneType | resultBody.records[0].selectType | None |
| selectTypeValue | NoneType | resultBody.records[0].selectTypeValue | None |
| projectType | str | resultBody.records[0].projectType | 10 |

---

### 接口 8: POST https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList

#### 📤 请求信息

- **URL**: `https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList`
- **方法**: `POST`
- **请求体参数**:
```json
{
  "selectCategory": [
    "1",
    "3"
  ],
  "projectName": "",
  "projectNo": "",
  "selectName": "",
  "businessArea": "",
  "selectStatus": "",
  "projectMsgId": "",
  "currentPage": 1,
  "pageSize": 10
}
```
- **请求头**:
  - `content-type`: application/json;charset=UTF-8

#### 📥 响应信息

- **状态码**: 200
- **内容类型**: application/json;charset=UTF-8
- **响应体结构**:
```json
{
  "busiDate": {
    "type": "str",
    "path": "busiDate"
  },
  "code": {
    "type": "str",
    "path": "code"
  },
  "message": {
    "type": "NoneType",
    "path": "message"
  },
  "resultBody": {
    "type": "dict",
    "path": "resultBody"
  }
}
```
- **数据字段**:

| 字段名 | 数据类型 | 字段路径 | 样本值 |
|--------|----------|----------|--------|
| busiDate | str | busiDate | 2025-07-08 19:51:12 |
| code | str | code | 000000 |
| message | NoneType | message | None |
| resultBody | dict | resultBody | {'total': 1445, 'size': 10, 'current': 1, 'records... |
| total | int | resultBody.total | 1445 |
| size | int | resultBody.size | 10 |
| current | int | resultBody.current | 1 |
| records | list | resultBody.records | [{'projectMsgId': '1942422593200898048', 'workOrde... |
| projectMsgId | str | resultBody.records[0].projectMsgId | 1942422593200898048 |
| workOrderMsgId | str | resultBody.records[0].workOrderMsgId | GD76020250708111442151796 |
| shutOrderMsgId | NoneType | resultBody.records[0].shutOrderMsgId | None |
| selectMsgId | NoneType | resultBody.records[0].selectMsgId | None |
| selectApplyId | NoneType | resultBody.records[0].selectApplyId | None |
| projectName | str | resultBody.records[0].projectName | 中山市坤鹏电子科技有限公司信息化建设项目 |
| selectName | str | resultBody.records[0].selectName | 中山移动某智慧园区项目 |
| count | str | resultBody.records[0].count | 0 |
| projectNo | str | resultBody.records[0].projectNo | CMGDZSICT20250707037 |
| selectType | NoneType | resultBody.records[0].selectType | None |
| selectTypeValue | NoneType | resultBody.records[0].selectTypeValue | None |
| projectType | str | resultBody.records[0].projectType | 10 |

---

### 接口 9: POST https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList

#### 📤 请求信息

- **URL**: `https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList`
- **方法**: `POST`
- **请求体参数**:
```json
{
  "selectCategory": [
    "1",
    "3"
  ],
  "projectName": "测试项目",
  "projectNo": "",
  "selectName": "",
  "businessArea": "",
  "selectStatus": "",
  "projectMsgId": "",
  "currentPage": 1,
  "pageSize": 10
}
```
- **请求头**:
  - `content-type`: application/json;charset=UTF-8

#### 📥 响应信息

- **状态码**: 200
- **内容类型**: application/json;charset=UTF-8
- **响应体结构**:
```json
{
  "busiDate": {
    "type": "str",
    "path": "busiDate"
  },
  "code": {
    "type": "str",
    "path": "code"
  },
  "message": {
    "type": "NoneType",
    "path": "message"
  },
  "resultBody": {
    "type": "dict",
    "path": "resultBody"
  }
}
```
- **数据字段**:

| 字段名 | 数据类型 | 字段路径 | 样本值 |
|--------|----------|----------|--------|
| busiDate | str | busiDate | 2025-07-08 19:51:12 |
| code | str | code | 000000 |
| message | NoneType | message | None |
| resultBody | dict | resultBody | {'total': 1445, 'size': 10, 'current': 1, 'records... |
| total | int | resultBody.total | 1445 |
| size | int | resultBody.size | 10 |
| current | int | resultBody.current | 1 |
| records | list | resultBody.records | [{'projectMsgId': '1942422593200898048', 'workOrde... |
| projectMsgId | str | resultBody.records[0].projectMsgId | 1942422593200898048 |
| workOrderMsgId | str | resultBody.records[0].workOrderMsgId | GD76020250708111442151796 |
| shutOrderMsgId | NoneType | resultBody.records[0].shutOrderMsgId | None |
| selectMsgId | NoneType | resultBody.records[0].selectMsgId | None |
| selectApplyId | NoneType | resultBody.records[0].selectApplyId | None |
| projectName | str | resultBody.records[0].projectName | 中山市坤鹏电子科技有限公司信息化建设项目 |
| selectName | str | resultBody.records[0].selectName | 中山移动某智慧园区项目 |
| count | str | resultBody.records[0].count | 0 |
| projectNo | str | resultBody.records[0].projectNo | CMGDZSICT20250707037 |
| selectType | NoneType | resultBody.records[0].selectType | None |
| selectTypeValue | NoneType | resultBody.records[0].selectTypeValue | None |
| projectType | str | resultBody.records[0].projectType | 10 |

---

### 接口 10: POST https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList

#### 📤 请求信息

- **URL**: `https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList`
- **方法**: `POST`
- **请求体参数**:
```json
{
  "selectCategory": [
    "1",
    "3"
  ],
  "projectName": "",
  "projectNo": "",
  "selectName": "",
  "businessArea": "",
  "selectStatus": "",
  "projectMsgId": "",
  "currentPage": 1,
  "pageSize": 10
}
```
- **请求头**:
  - `content-type`: application/json;charset=UTF-8

#### 📥 响应信息

- **状态码**: 200
- **内容类型**: application/json;charset=UTF-8
- **响应体结构**:
```json
{
  "busiDate": {
    "type": "str",
    "path": "busiDate"
  },
  "code": {
    "type": "str",
    "path": "code"
  },
  "message": {
    "type": "NoneType",
    "path": "message"
  },
  "resultBody": {
    "type": "dict",
    "path": "resultBody",
    "nested": {
      "total": {
        "type": "int",
        "path": "resultBody.total"
      },
      "size": {
        "type": "int",
        "path": "resultBody.size"
      },
      "current": {
        "type": "int",
        "path": "resultBody.current"
      },
      "records": {
        "type": "list",
        "path": "resultBody.records",
        "nested": {
          "type": "array",
          "length": 1,
          "item_structure": {
            "projectMsgId": {
              "type": "str",
              "path": "resultBody.records[0].projectMsgId"
            },
            "workOrderMsgId": {
              "type": "NoneType",
              "path": "resultBody.records[0].workOrderMsgId"
            },
            "shutOrderMsgId": {
              "type": "NoneType",
              "path": "resultBody.records[0].shutOrderMsgId"
            },
            "selectMsgId": {
              "type": "str",
              "path": "resultBody.records[0].selectMsgId"
            },
            "selectApplyId": {
              "type": "NoneType",
              "path": "resultBody.records[0].selectApplyId"
            },
            "projectName": {
              "type": "str",
              "path": "resultBody.records[0].projectName"
            },
            "selectName": {
              "type": "str",
              "path": "resultBody.records[0].selectName"
            },
            "count": {
              "type": "str",
              "path": "resultBody.records[0].count"
            },
            "projectNo": {
              "type": "str",
              "path": "resultBody.records[0].projectNo"
            },
            "selectType": {
              "type": "NoneType",
              "path": "resultBody.records[0].selectType"
            },
            "selectTypeValue": {
              "type": "NoneType",
              "path": "resultBody.records[0].selectTypeValue"
            },
            "projectType": {
              "type": "str",
              "path": "resultBody.records[0].projectType"
            },
            "projectLabel": {
              "type": "str",
              "path": "resultBody.records[0].projectLabel"
            },
            "businessArea": {
              "type": "str",
              "path": "resultBody.records[0].businessArea"
            },
            "businessAreaValue": {
              "type": "str",
              "path": "resultBody.records[0].businessAreaValue"
            },
            "startTime": {
              "type": "str",
              "path": "resultBody.records[0].startTime"
            },
            "selectStatus": {
              "type": "str",
              "path": "resultBody.records[0].selectStatus"
            },
            "selectStatusValue": {
              "type": "str",
              "path": "resultBody.records[0].selectStatusValue"
            },
            "initiateDepartment": {
              "type": "NoneType",
              "path": "resultBody.records[0].initiateDepartment"
            },
            "createTime": {
              "type": "str",
              "path": "resultBody.records[0].createTime"
            },
            "isFixedSoftness": {
              "type": "str",
              "path": "resultBody.records[0].isFixedSoftness"
            },
            "createStaff": {
              "type": "str",
              "path": "resultBody.records[0].createStaff"
            },
            "createStaffValue": {
              "type": "str",
              "path": "resultBody.records[0].createStaffValue"
            },
            "nextTodoHandler": {
              "type": "str",
              "path": "resultBody.records[0].nextTodoHandler"
            },
            "nextTodoHandlerValue": {
              "type": "str",
              "path": "resultBody.records[0].nextTodoHandlerValue"
            },
            "isOperable": {
              "type": "str",
              "path": "resultBody.records[0].isOperable"
            },
            "changeType1": {
              "type": "NoneType",
              "path": "resultBody.records[0].changeType1"
            },
            "changeType2": {
              "type": "NoneType",
              "path": "resultBody.records[0].changeType2"
            },
            "isTerminable": {
              "type": "str",
              "path": "resultBody.records[0].isTerminable"
            },
            "isAllowSecond": {
              "type": "NoneType",
              "path": "resultBody.records[0].isAllowSecond"
            },
            "selectCategory": {
              "type": "str",
              "path": "resultBody.records[0].selectCategory"
            },
            "selectCategoryValue": {
              "type": "str",
              "path": "resultBody.records[0].selectCategoryValue"
            },
            "dpcsSelectSecondNegotiate": {
              "type": "NoneType",
              "path": "resultBody.records[0].dpcsSelectSecondNegotiate"
            }
          }
        }
      },
      "pages": {
        "type": "int",
        "path": "resultBody.pages"
      }
    }
  }
}
```
- **数据字段**:

| 字段名 | 数据类型 | 字段路径 | 样本值 |
|--------|----------|----------|--------|
| busiDate | str | busiDate | 2025-07-08 19:51:17 |
| code | str | code | 000000 |
| message | NoneType | message | None |
| resultBody | dict | resultBody | {'total': 1, 'size': 10, 'current': 1, 'records': ... |
| total | int | resultBody.total | 1 |
| size | int | resultBody.size | 10 |
| current | int | resultBody.current | 1 |
| records | list | resultBody.records | [{'projectMsgId': '1669542762748231680', 'workOrde... |
| projectMsgId | str | resultBody.records[0].projectMsgId | 1669542762748231680 |
| workOrderMsgId | NoneType | resultBody.records[0].workOrderMsgId | None |
| shutOrderMsgId | NoneType | resultBody.records[0].shutOrderMsgId | None |
| selectMsgId | str | resultBody.records[0].selectMsgId | 1671373976840880128 |
| selectApplyId | NoneType | resultBody.records[0].selectApplyId | None |
| projectName | str | resultBody.records[0].projectName | 云安全渗透测试项目 |
| selectName | str | resultBody.records[0].selectName | 中山移动云安全渗透测试服务项目 |
| count | str | resultBody.records[0].count | 1 |
| projectNo | str | resultBody.records[0].projectNo | CMGDZSICT20230529015 |
| selectType | NoneType | resultBody.records[0].selectType | None |
| selectTypeValue | NoneType | resultBody.records[0].selectTypeValue | None |
| projectType | str | resultBody.records[0].projectType | 10 |

---

## 📊 数据结构汇总

### 📤 请求字段汇总

| 字段名 | 数据类型 | 参数类型 | 样本值 | 使用接口 |
|--------|----------|----------|--------|----------|
| groupId | str | query_parameters | 200025, 2000222 | https://dict.gmcc.net:30722/partner/materialManage/sys/rCache/getDictListByGroupId2?groupId=200025,  |
| opType | str | query_parameters | 1 | https://dict.gmcc.net:30722/partner/materialManage/dataManage/partnerRecruitController/getCityListBy |
| selecCategory | str | body_parameters | 1, 1 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| currentPage | int | body_parameters | 1, 1, 1 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| pageSize | int | body_parameters | 10, 10, 10 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| selectCategory | list | body_parameters | ['1', '3'], ['1', '3'], ['1', '3'] | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| projectName | str | body_parameters | , 测试项目,  | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| projectNo | str | body_parameters | , ,  | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| selectName | str | body_parameters | , ,  | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| businessArea | str | body_parameters | , ,  | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| selectStatus | str | body_parameters | , ,  | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| projectMsgId | str | body_parameters | , ,  | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |

### 📥 响应字段汇总

| 字段名 | 数据类型 | 字段路径 | 样本值 | 使用接口 |
|--------|----------|----------|--------|----------|
| busiDate | str | busiDate | 2025-07-08 19:51:02, 2025-07-08 19:51:02, 2025-07-08 19:51:12 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| code | str | code | 000000, 000000, 000000 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| message | NoneType | message |  | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| resultBody | dict | resultBody | {'total': 1445, 'size': 10, 'current': 1, 'records': [{'projectMsgId': '1942422593200898048', 'workO, {'total': 1445, 'size': 10, 'current': 1, 'records': [{'projectMsgId': '1942422593200898048', 'workO, {'total': 1445, 'size': 10, 'current': 1, 'records': [{'projectMsgId': '1942422593200898048', 'workO | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| total | int | resultBody.total | 1445, 1445, 1445 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| size | int | resultBody.size | 10, 10, 10 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| current | int | resultBody.current | 1, 1, 1 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| records | list | resultBody.records | [{'projectMsgId': '1942422593200898048', 'workOrderMsgId': 'GD76020250708111442151796', 'shutOrderMs, [{'projectMsgId': '1942422593200898048', 'workOrderMsgId': 'GD76020250708111442151796', 'shutOrderMs, [{'projectMsgId': '1942422593200898048', 'workOrderMsgId': 'GD76020250708111442151796', 'shutOrderMs | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| projectMsgId | str | resultBody.records[0].projectMsgId | 1942422593200898048, 1942422593200898048, 1942422593200898048 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| workOrderMsgId | str | resultBody.records[0].workOrderMsgId | GD76020250708111442151796, GD76020250708111442151796, GD76020250708111442151796 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| shutOrderMsgId | NoneType | resultBody.records[0].shutOrderMsgId |  | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| selectMsgId | NoneType | resultBody.records[0].selectMsgId | 1671373976840880128 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| selectApplyId | NoneType | resultBody.records[0].selectApplyId |  | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| projectName | str | resultBody.records[0].projectName | 中山市坤鹏电子科技有限公司信息化建设项目, 中山市坤鹏电子科技有限公司信息化建设项目, 中山市坤鹏电子科技有限公司信息化建设项目 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| selectName | str | resultBody.records[0].selectName | 中山移动某智慧园区项目, 中山移动某智慧园区项目, 中山移动某智慧园区项目 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| count | str | resultBody.records[0].count | 0, 0, 0 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| projectNo | str | resultBody.records[0].projectNo | CMGDZSICT20250707037, CMGDZSICT20250707037, CMGDZSICT20250707037 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| selectType | NoneType | resultBody.records[0].selectType |  | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| selectTypeValue | NoneType | resultBody.records[0].selectTypeValue |  | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| projectType | str | resultBody.records[0].projectType | 10, 10, 10 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| projectLabel | str | resultBody.records[0].projectLabel | 54, 54, 54 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| businessArea | str | resultBody.records[0].businessArea | 760, 760, 760 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| businessAreaValue | str | resultBody.records[0].businessAreaValue | 中山, 中山, 中山 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| startTime | str | resultBody.records[0].startTime | 2025-07-10 00:00:00, 2025-07-10 00:00:00, 2025-07-10 00:00:00 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| selectStatus | str | resultBody.records[0].selectStatus | 1001, 1001, 1001 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| selectStatusValue | str | resultBody.records[0].selectStatusValue | 审核通过, 审核通过, 审核通过 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| initiateDepartment | NoneType | resultBody.records[0].initiateDepartment |  | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| createTime | str | resultBody.records[0].createTime | 2025-07-08 11:16:49, 2025-07-08 11:16:49, 2025-07-08 11:16:49 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| isFixedSoftness | str | resultBody.records[0].isFixedSoftness | 0, 0, 0 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| createStaff | str | resultBody.records[0].createStaff | liuhuanxu, liuhuanxu, liuhuanxu | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| createStaffValue | str | resultBody.records[0].createStaffValue | 刘桓旭, 刘桓旭, 刘桓旭 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| nextTodoHandler | str | resultBody.records[0].nextTodoHandler | liuhuanxu, liuhuanxu, liuhuanxu | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| nextTodoHandlerValue | str | resultBody.records[0].nextTodoHandlerValue | 刘桓旭, 刘桓旭, 刘桓旭 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| isOperable | str | resultBody.records[0].isOperable | 0, 0, 0 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| changeType1 | NoneType | resultBody.records[0].changeType1 |  | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| changeType2 | NoneType | resultBody.records[0].changeType2 |  | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| isTerminable | str | resultBody.records[0].isTerminable | 0, 0, 0 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| isAllowSecond | NoneType | resultBody.records[0].isAllowSecond |  | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| selectCategory | str | resultBody.records[0].selectCategory | 1, 1, 1 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| selectCategoryValue | str | resultBody.records[0].selectCategoryValue | 项目甄选, 项目甄选, 项目甄选 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| dpcsSelectSecondNegotiate | NoneType | resultBody.records[0].dpcsSelectSecondNegotiate |  | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
| pages | int | resultBody.pages | 145, 145, 145 | https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList |
