{"summary": {"total_requests": 10, "total_responses": 10, "api_requests": [{"method": "GET", "url": "https://dict.gmcc.net:30722/ptn/main/selectDemand", "status": 200}, {"method": "GET", "url": "https://dict.gmcc.net:30722/partner/materialManage/sys/rCache/getDictListByGroupId2?groupId=200025", "status": 200}, {"method": "GET", "url": "https://dict.gmcc.net:30722/partner/materialManage/dataManage/partnerRecruitController/getCityListByType?opType=1", "status": 200}, {"method": "GET", "url": "https://dict.gmcc.net:30722/partner/materialManage/sys/rCache/getDictListByGroupId2?groupId=2000222", "status": 200}, {"method": "POST", "url": "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "status": 200}, {"method": "POST", "url": "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "status": 200}, {"method": "POST", "url": "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "status": 200}, {"method": "POST", "url": "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "status": 200}, {"method": "POST", "url": "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "status": 200}], "static_requests": [{"method": "GET", "url": "https://dict.gmcc.net:30722/ptn/main/static/prePage.gif"}]}, "api_interfaces": [{"index": 1, "url": "https://dict.gmcc.net:30722/ptn/main/selectDemand", "method": "GET", "request_data": {"timestamp": "2025-07-08T19:51:00.150670", "url": "https://dict.gmcc.net:30722/ptn/main/selectDemand", "method": "GET", "headers": {"upgrade-insecure-requests": "1", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"Windows\""}, "post_data": null, "post_data_json": null, "query_params": {}, "resource_type": "document"}, "response_data": {"timestamp": "2025-07-08T19:51:01.885596", "url": "https://dict.gmcc.net:30722/ptn/main/selectDemand", "status": 200, "status_text": "OK", "headers": {"accept-ranges": "bytes", "connection": "keep-alive", "content-length": "912", "content-type": "text/html; charset=utf-8", "date": "<PERSON><PERSON>, 08 Jul 2025 11:50:58 GMT", "etag": "\"686648df-390\"", "last-modified": "Thu, 03 Jul 2025 09:09:51 GMT", "server": "nginx", "location_gray": "location_rest_v1"}, "body": "<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\">\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge,chrome=1\" />\n    <title> 框架-主页面 </title>\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n<link href=\"/ptn/main/8.c6630a00fbf2aa9f74f2-1.css\" rel=\"stylesheet\"><link href=\"/ptn/main/8.c6630a00fbf2aa9f74f2-2.css\" rel=\"stylesheet\"><link href=\"/ptn/main/5.c6630a00fbf2aa9f74f2.css\" rel=\"stylesheet\"></head>\n\n<body>\n\t<div id=\"preLoad\">\n\t\t<img src=\"./static/prePage.gif\" alt=\"加载中\" style=\"position:absolute;left:46%;top:30%;\">\n\t</div>\n<script type=\"text/javascript\" src=\"/ptn/main/vendor.473a1156.js\"></script><script type=\"text/javascript\" src=\"/ptn/main/commons~app~login.e281dcda.js\"></script><script type=\"text/javascript\" src=\"/ptn/main/runtime~app.b083d1dd.js\"></script><script type=\"text/javascript\" src=\"/ptn/main/app.74e85789.js\"></script></body>\n\n</html>\n", "body_json": null, "content_type": "text/html; charset=utf-8"}, "parsed_request": {"url_path": "/ptn/main/selectDemand", "query_parameters": {}, "body_parameters": {}, "headers": {}}, "parsed_response": {"status_code": 200, "content_type": "text/html; charset=utf-8", "headers": {"content-type": "text/html; charset=utf-8", "content-length": "912"}, "body_structure": {}, "data_fields": []}}, {"index": 3, "url": "https://dict.gmcc.net:30722/partner/materialManage/sys/rCache/getDictListByGroupId2?groupId=200025", "method": "GET", "request_data": {"timestamp": "2025-07-08T19:51:03.303486", "url": "https://dict.gmcc.net:30722/partner/materialManage/sys/rCache/getDictListByGroupId2?groupId=200025", "method": "GET", "headers": {"sec-ch-ua-platform": "\"Windows\"", "referer": "https://dict.gmcc.net:30722/ptn/main/selectDemand", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\"", "sec-ch-ua-mobile": "?0"}, "post_data": null, "post_data_json": null, "query_params": {"groupId": ["200025"]}, "resource_type": "xhr"}, "response_data": {"timestamp": "2025-07-08T19:51:03.357177", "url": "https://dict.gmcc.net:30722/partner/materialManage/sys/rCache/getDictListByGroupId2?groupId=200025", "status": 200, "status_text": "", "headers": {"transfer-encoding": "chunked", "location_gray": "location_rest_v1", "date": "<PERSON><PERSON>, 08 Jul 2025 11:51:00 GMT", "content-type": "application/json;charset=UTF-8", "server": "nginx", "connection": "keep-alive"}, "body": "[{\"dictId\":\"1000\",\"dictName\":\"待审核\",\"groupId\":\"200025\",\"descrp\":\"甄选需求状态\",\"otherInfo\":null},{\"dictId\":\"1001\",\"dictName\":\"审核通过\",\"groupId\":\"200025\",\"descrp\":\"甄选需求状态\",\"otherInfo\":null},{\"dictId\":\"1003\",\"dictName\":\"审核不通过\",\"groupId\":\"200025\",\"descrp\":\"甄选需求状态\",\"otherInfo\":null},{\"dictId\":\"1002\",\"dictName\":\"审核通过(已制定方案)\",\"groupId\":\"200025\",\"descrp\":\"甄选需求状态\",\"otherInfo\":null},{\"dictId\":\"1101\",\"dictName\":\"终止待审核\",\"groupId\":\"200025\",\"descrp\":\"甄选需求状态\",\"otherInfo\":null},{\"dictId\":\"1102\",\"dictName\":\"终止不通过\",\"groupId\":\"200025\",\"descrp\":\"甄选需求状态\",\"otherInfo\":null},{\"dictId\":\"1004\",\"dictName\":\"退回待修改\",\"groupId\":\"200025\",\"descrp\":\"甄选需求状态\",\"otherInfo\":null},{\"dictId\":\"1100\",\"dictName\":\"已终止\",\"groupId\":\"200025\",\"descrp\":\"甄选需求状态\",\"otherInfo\":null}]", "body_json": [{"dictId": "1000", "dictName": "待审核", "groupId": "200025", "descrp": "甄选需求状态", "otherInfo": null}, {"dictId": "1001", "dictName": "审核通过", "groupId": "200025", "descrp": "甄选需求状态", "otherInfo": null}, {"dictId": "1003", "dictName": "审核不通过", "groupId": "200025", "descrp": "甄选需求状态", "otherInfo": null}, {"dictId": "1002", "dictName": "审核通过(已制定方案)", "groupId": "200025", "descrp": "甄选需求状态", "otherInfo": null}, {"dictId": "1101", "dictName": "终止待审核", "groupId": "200025", "descrp": "甄选需求状态", "otherInfo": null}, {"dictId": "1102", "dictName": "终止不通过", "groupId": "200025", "descrp": "甄选需求状态", "otherInfo": null}, {"dictId": "1004", "dictName": "退回待修改", "groupId": "200025", "descrp": "甄选需求状态", "otherInfo": null}, {"dictId": "1100", "dictName": "已终止", "groupId": "200025", "descrp": "甄选需求状态", "otherInfo": null}], "content_type": "application/json;charset=UTF-8"}, "parsed_request": {"url_path": "/partner/materialManage/sys/rCache/getDictListByGroupId2", "query_parameters": {"groupId": "200025"}, "body_parameters": {}, "headers": {}}, "parsed_response": {"status_code": 200, "content_type": "application/json;charset=UTF-8", "headers": {"content-type": "application/json;charset=UTF-8"}, "body_structure": {"type": "array", "length": 8, "item_structure": {"dictId": {"type": "str", "path": "[0].dictId"}, "dictName": {"type": "str", "path": "[0].dictName"}, "groupId": {"type": "str", "path": "[0].groupId"}, "descrp": {"type": "str", "path": "[0].descrp"}, "otherInfo": {"type": "NoneType", "path": "[0].otherInfo"}}}, "data_fields": []}}, {"index": 4, "url": "https://dict.gmcc.net:30722/partner/materialManage/dataManage/partnerRecruitController/getCityListByType?opType=1", "method": "GET", "request_data": {"timestamp": "2025-07-08T19:51:03.304536", "url": "https://dict.gmcc.net:30722/partner/materialManage/dataManage/partnerRecruitController/getCityListByType?opType=1", "method": "GET", "headers": {"sec-ch-ua-platform": "\"Windows\"", "referer": "https://dict.gmcc.net:30722/ptn/main/selectDemand", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\"", "sec-ch-ua-mobile": "?0"}, "post_data": null, "post_data_json": null, "query_params": {"opType": ["1"]}, "resource_type": "xhr"}, "response_data": {"timestamp": "2025-07-08T19:51:03.413782", "url": "https://dict.gmcc.net:30722/partner/materialManage/dataManage/partnerRecruitController/getCityListByType?opType=1", "status": 200, "status_text": "", "headers": {"transfer-encoding": "chunked", "location_gray": "location_rest_v1", "date": "<PERSON><PERSON>, 08 Jul 2025 11:51:00 GMT", "content-type": "application/json;charset=UTF-8", "server": "nginx", "connection": "keep-alive"}, "body": "[{\"dictId\":\"760\",\"dictName\":\"中山\",\"groupId\":\"200028\",\"descrp\":\"地市\",\"otherInfo\":null}]", "body_json": [{"dictId": "760", "dictName": "中山", "groupId": "200028", "descrp": "地市", "otherInfo": null}], "content_type": "application/json;charset=UTF-8"}, "parsed_request": {"url_path": "/partner/materialManage/dataManage/partnerRecruitController/getCityListByType", "query_parameters": {"opType": "1"}, "body_parameters": {}, "headers": {}}, "parsed_response": {"status_code": 200, "content_type": "application/json;charset=UTF-8", "headers": {"content-type": "application/json;charset=UTF-8"}, "body_structure": {"type": "array", "length": 1, "item_structure": {"dictId": {"type": "str", "path": "[0].dictId"}, "dictName": {"type": "str", "path": "[0].dictName"}, "groupId": {"type": "str", "path": "[0].groupId"}, "descrp": {"type": "str", "path": "[0].descrp"}, "otherInfo": {"type": "NoneType", "path": "[0].otherInfo"}}}, "data_fields": []}}, {"index": 5, "url": "https://dict.gmcc.net:30722/partner/materialManage/sys/rCache/getDictListByGroupId2?groupId=2000222", "method": "GET", "request_data": {"timestamp": "2025-07-08T19:51:03.304536", "url": "https://dict.gmcc.net:30722/partner/materialManage/sys/rCache/getDictListByGroupId2?groupId=2000222", "method": "GET", "headers": {"sec-ch-ua-platform": "\"Windows\"", "referer": "https://dict.gmcc.net:30722/ptn/main/selectDemand", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\"", "sec-ch-ua-mobile": "?0"}, "post_data": null, "post_data_json": null, "query_params": {"groupId": ["2000222"]}, "resource_type": "xhr"}, "response_data": {"timestamp": "2025-07-08T19:51:03.393138", "url": "https://dict.gmcc.net:30722/partner/materialManage/sys/rCache/getDictListByGroupId2?groupId=2000222", "status": 200, "status_text": "", "headers": {"transfer-encoding": "chunked", "location_gray": "location_rest_v1", "date": "<PERSON><PERSON>, 08 Jul 2025 11:51:00 GMT", "content-type": "application/json;charset=UTF-8", "server": "nginx", "connection": "keep-alive"}, "body": "[{\"dictId\":\"1\",\"dictName\":\"项目甄选\",\"groupId\":\"2000222\",\"descrp\":\"甄选类别（项目甄选、产品甄选）\",\"otherInfo\":null},{\"dictId\":\"2\",\"dictName\":\"产品甄选\",\"groupId\":\"2000222\",\"descrp\":\"甄选类别（项目甄选、产品甄选）\",\"otherInfo\":null},{\"dictId\":\"3\",\"dictName\":\"算力项目甄选\",\"groupId\":\"2000222\",\"descrp\":\"甄选类别（项目甄选、产品甄选）\",\"otherInfo\":null}]", "body_json": [{"dictId": "1", "dictName": "项目甄选", "groupId": "2000222", "descrp": "甄选类别（项目甄选、产品甄选）", "otherInfo": null}, {"dictId": "2", "dictName": "产品甄选", "groupId": "2000222", "descrp": "甄选类别（项目甄选、产品甄选）", "otherInfo": null}, {"dictId": "3", "dictName": "算力项目甄选", "groupId": "2000222", "descrp": "甄选类别（项目甄选、产品甄选）", "otherInfo": null}], "content_type": "application/json;charset=UTF-8"}, "parsed_request": {"url_path": "/partner/materialManage/sys/rCache/getDictListByGroupId2", "query_parameters": {"groupId": "2000222"}, "body_parameters": {}, "headers": {}}, "parsed_response": {"status_code": 200, "content_type": "application/json;charset=UTF-8", "headers": {"content-type": "application/json;charset=UTF-8"}, "body_structure": {"type": "array", "length": 3, "item_structure": {"dictId": {"type": "str", "path": "[0].dictId"}, "dictName": {"type": "str", "path": "[0].dictName"}, "groupId": {"type": "str", "path": "[0].groupId"}, "descrp": {"type": "str", "path": "[0].descrp"}, "otherInfo": {"type": "NoneType", "path": "[0].otherInfo"}}}, "data_fields": []}}, {"index": 6, "url": "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "method": "POST", "request_data": {"timestamp": "2025-07-08T19:51:03.305560", "url": "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "method": "POST", "headers": {"sec-ch-ua-platform": "\"Windows\"", "referer": "https://dict.gmcc.net:30722/ptn/main/selectDemand", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\"", "content-type": "application/json;charset=UTF-8", "sec-ch-ua-mobile": "?0"}, "post_data": "{\"selecCategory\":\"1\",\"currentPage\":1,\"pageSize\":10}", "post_data_json": {"selecCategory": "1", "currentPage": 1, "pageSize": 10}, "query_params": {}, "resource_type": "xhr"}, "response_data": {"timestamp": "2025-07-08T19:51:05.672904", "url": "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "status": 200, "status_text": "", "headers": {"transfer-encoding": "chunked", "location_gray": "location_rest_v1", "date": "<PERSON><PERSON>, 08 Jul 2025 11:51:02 GMT", "content-type": "application/json;charset=UTF-8", "server": "nginx", "connection": "keep-alive"}, "body": "{\"busiDate\":\"2025-07-08 19:51:02\",\"code\":\"000000\",\"message\":null,\"resultBody\":{\"total\":1445,\"size\":10,\"current\":1,\"records\":[{\"projectMsgId\":\"1942422593200898048\",\"workOrderMsgId\":\"GD76020250708111442151796\",\"shutOrderMsgId\":null,\"selectMsgId\":null,\"selectApplyId\":null,\"projectName\":\"中山市坤鹏电子科技有限公司信息化建设项目\",\"selectName\":\"中山移动某智慧园区项目\",\"count\":\"0\",\"projectNo\":\"CMGDZSICT20250707037\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"54\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2025-07-10 00:00:00\",\"selectStatus\":\"1001\",\"selectStatusValue\":\"审核通过\",\"initiateDepartment\":null,\"createTime\":\"2025-07-08 11:16:49\",\"isFixedSoftness\":\"0\",\"createStaff\":\"liuhuanxu\",\"createStaffValue\":\"刘桓旭\",\"nextTodoHandler\":\"liuhuanxu\",\"nextTodoHandlerValue\":\"刘桓旭\",\"isOperable\":\"0\",\"changeType1\":null,\"changeType2\":null,\"isTerminable\":\"0\",\"isAllowSecond\":null,\"selectCategory\":\"1\",\"selectCategoryValue\":\"项目甄选\",\"dpcsSelectSecondNegotiate\":null},{\"projectMsgId\":\"1942116921179553792\",\"workOrderMsgId\":\"GD76020250707145959860730\",\"shutOrderMsgId\":null,\"selectMsgId\":\"1942400615916158976\",\"selectApplyId\":null,\"projectName\":\"中山市火炬开发区第一幼儿园2025年智慧安防进校园门禁系统维护项目\",\"selectName\":\"中山移动校园门禁项目\",\"count\":\"1\",\"projectNo\":\"CMGDZSICT20250514031\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"54\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2023-03-07 00:00:00\",\"selectStatus\":\"1002\",\"selectStatusValue\":\"审核通过(已制定方案)\",\"initiateDepartment\":null,\"createTime\":\"2025-07-07 15:02:11\",\"isFixedSoftness\":\"0\",\"createStaff\":\"zhongyuan01\",\"createStaffValue\":\"钟源\",\"nextTodoHandler\":\"zhongyuan01\",\"nextTodoHandlerValue\":\"钟源\",\"isOperable\":\"0\",\"changeType1\":null,\"changeType2\":null,\"isTerminable\":\"0\",\"isAllowSecond\":null,\"selectCategory\":\"1\",\"selectCategoryValue\":\"项目甄选\",\"dpcsSelectSecondNegotiate\":null},{\"projectMsgId\":\"1942069083955445760\",\"workOrderMsgId\":\"GD76020250707114834742565\",\"shutOrderMsgId\":null,\"selectMsgId\":\"1942114146337079296\",\"selectApplyId\":\"1942141579274665984\",\"projectName\":\"横栏消防支队LCD+AP+报警项目\",\"selectName\":\"中山移动横栏消防救援支队LCD+报警项目\",\"count\":\"1\",\"projectNo\":\"CMGDZSICT20250603017\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"30\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2025-06-16 00:00:00\",\"selectStatus\":\"1002\",\"selectStatusValue\":\"审核通过(已制定方案)\",\"initiateDepartment\":null,\"createTime\":\"2025-07-07 11:52:06\",\"isFixedSoftness\":\"0\",\"createStaff\":\"huangyijun2\",\"createStaffValue\":\"黄奕俊\",\"nextTodoHandler\":\"huangyijun2\",\"nextTodoHandlerValue\":\"黄奕俊\",\"isOperable\":\"0\",\"changeType1\":null,\"changeType2\":null,\"isTerminable\":\"0\",\"isAllowSecond\":null,\"selectCategory\":\"1\",\"selectCategoryValue\":\"项目甄选\",\"dpcsSelectSecondNegotiate\":null},{\"projectMsgId\":\"1942023416348327936\",\"workOrderMsgId\":\"GD76020250707084708189250\",\"shutOrderMsgId\":null,\"selectMsgId\":\"1942387861012529152\",\"selectApplyId\":\"1942490547577864192\",\"projectName\":\"社会保险业务智能稽核复审项目\",\"selectName\":\"中山移动业务智能稽核复审项目\",\"count\":\"1\",\"projectNo\":\"CMGDZSICT20250707001\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"54\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2025-07-07 00:00:00\",\"selectStatus\":\"1002\",\"selectStatusValue\":\"审核通过(已制定方案)\",\"initiateDepartment\":null,\"createTime\":\"2025-07-07 08:50:38\",\"isFixedSoftness\":\"0\",\"createStaff\":\"oujiahui3\",\"createStaffValue\":\"欧嘉慧\",\"nextTodoHandler\":\"oujiahui3\",\"nextTodoHandlerValue\":\"欧嘉慧\",\"isOperable\":\"0\",\"changeType1\":null,\"changeType2\":null,\"isTerminable\":\"0\",\"isAllowSecond\":null,\"selectCategory\":\"1\",\"selectCategoryValue\":\"项目甄选\",\"dpcsSelectSecondNegotiate\":null},{\"projectMsgId\":\"1940961734821855232\",\"workOrderMsgId\":\"GD76020250704102830314437\",\"shutOrderMsgId\":null,\"selectMsgId\":\"1942029594583285760\",\"selectApplyId\":\"1942138444196003840\",\"projectName\":\"嘉钦工业园区智能化项目\",\"selectName\":\"中山移动嘉钦工业园区智能化项目\",\"count\":\"1\",\"projectNo\":\"CMGDZSICT20250616063\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"10\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2025-07-04 00:00:00\",\"selectStatus\":\"1002\",\"selectStatusValue\":\"审核通过(已制定方案)\",\"initiateDepartment\":null,\"createTime\":\"2025-07-04 10:31:53\",\"isFixedSoftness\":\"0\",\"createStaff\":\"hehaoming\",\"createStaffValue\":\"何浩明\",\"nextTodoHandler\":\"hehaoming\",\"nextTodoHandlerValue\":\"何浩明\",\"isOperable\":\"0\",\"changeType1\":null,\"changeType2\":null,\"isTerminable\":\"0\",\"isAllowSecond\":null,\"selectCategory\":\"1\",\"selectCategoryValue\":\"项目甄选\",\"dpcsSelectSecondNegotiate\":null},{\"projectMsgId\":\"1940953867796529152\",\"workOrderMsgId\":\"GD76020250704095622517846\",\"shutOrderMsgId\":null,\"selectMsgId\":\"1942032646556008448\",\"selectApplyId\":\"1942138704502898688\",\"projectName\":\"绩东一德原北路三线下地整治工程\",\"selectName\":\"中山移动小榄镇某村信息化项目（第二次）\",\"count\":\"1\",\"projectNo\":\"CMGDZSICT20250604017\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"54\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2025-07-18 00:00:00\",\"selectStatus\":\"1002\",\"selectStatusValue\":\"审核通过(已制定方案)\",\"initiateDepartmen", "body_json": {"busiDate": "2025-07-08 19:51:02", "code": "000000", "message": null, "resultBody": {"total": 1445, "size": 10, "current": 1, "records": [{"projectMsgId": "1942422593200898048", "workOrderMsgId": "GD76020250708111442151796", "shutOrderMsgId": null, "selectMsgId": null, "selectApplyId": null, "projectName": "中山市坤鹏电子科技有限公司信息化建设项目", "selectName": "中山移动某智慧园区项目", "count": "0", "projectNo": "CMGDZSICT20250707037", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-10 00:00:00", "selectStatus": "1001", "selectStatusValue": "审核通过", "initiateDepartment": null, "createTime": "2025-07-08 11:16:49", "isFixedSoftness": "0", "createStaff": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createStaffValue": "刘桓旭", "nextTodoHandler": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nextTodoHandlerValue": "刘桓旭", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1942116921179553792", "workOrderMsgId": "GD76020250707145959860730", "shutOrderMsgId": null, "selectMsgId": "1942400615916158976", "selectApplyId": null, "projectName": "中山市火炬开发区第一幼儿园2025年智慧安防进校园门禁系统维护项目", "selectName": "中山移动校园门禁项目", "count": "1", "projectNo": "CMGDZSICT20250514031", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2023-03-07 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-07 15:02:11", "isFixedSoftness": "0", "createStaff": "zhongyuan01", "createStaffValue": "钟源", "nextTodoHandler": "zhongyuan01", "nextTodoHandlerValue": "钟源", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1942069083955445760", "workOrderMsgId": "GD76020250707114834742565", "shutOrderMsgId": null, "selectMsgId": "1942114146337079296", "selectApplyId": "1942141579274665984", "projectName": "横栏消防支队LCD+AP+报警项目", "selectName": "中山移动横栏消防救援支队LCD+报警项目", "count": "1", "projectNo": "CMGDZSICT20250603017", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "30", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-06-16 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-07 11:52:06", "isFixedSoftness": "0", "createStaff": "huangyijun2", "createStaffValue": "黄奕俊", "nextTodoHandler": "huangyijun2", "nextTodoHandlerValue": "黄奕俊", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1942023416348327936", "workOrderMsgId": "GD76020250707084708189250", "shutOrderMsgId": null, "selectMsgId": "1942387861012529152", "selectApplyId": "1942490547577864192", "projectName": "社会保险业务智能稽核复审项目", "selectName": "中山移动业务智能稽核复审项目", "count": "1", "projectNo": "CMGDZSICT20250707001", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-07 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-07 08:50:38", "isFixedSoftness": "0", "createStaff": "oujiahui3", "createStaffValue": "欧嘉慧", "nextTodoHandler": "oujiahui3", "nextTodoHandlerValue": "欧嘉慧", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940961734821855232", "workOrderMsgId": "GD76020250704102830314437", "shutOrderMsgId": null, "selectMsgId": "1942029594583285760", "selectApplyId": "1942138444196003840", "projectName": "嘉钦工业园区智能化项目", "selectName": "中山移动嘉钦工业园区智能化项目", "count": "1", "projectNo": "CMGDZSICT20250616063", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "10", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-04 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-04 10:31:53", "isFixedSoftness": "0", "createStaff": "<PERSON><PERSON><PERSON>", "createStaffValue": "何浩明", "nextTodoHandler": "<PERSON><PERSON><PERSON>", "nextTodoHandlerValue": "何浩明", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940953867796529152", "workOrderMsgId": "GD76020250704095622517846", "shutOrderMsgId": null, "selectMsgId": "1942032646556008448", "selectApplyId": "1942138704502898688", "projectName": "绩东一德原北路三线下地整治工程", "selectName": "中山移动小榄镇某村信息化项目（第二次）", "count": "1", "projectNo": "CMGDZSICT20250604017", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-18 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-04 10:00:37", "isFixedSoftness": "0", "createStaff": "<PERSON><PERSON><PERSON>", "createStaffValue": "何浩明", "nextTodoHandler": "<PERSON><PERSON><PERSON>", "nextTodoHandlerValue": "何浩明", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940705441884323840", "workOrderMsgId": "GD76020250703173321491482", "shutOrderMsgId": null, "selectMsgId": "1942149984940900352", "selectApplyId": null, "projectName": "中山市南朗医院信息应急系统建设服务项目", "selectName": "中山移动C镇区信息应急系统建设服务项目", "count": "1", "projectNo": "CMGDZSICT20250626070", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-07 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-03 17:33:28", "isFixedSoftness": "0", "createStaff": "<PERSON><PERSON><PERSON><PERSON>", "createStaffValue": "李远锋", "nextTodoHandler": "<PERSON><PERSON><PERSON><PERSON>", "nextTodoHandlerValue": "李远锋", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940668636770254848", "workOrderMsgId": "GD76020250703150544590539", "shutOrderMsgId": null, "selectMsgId": "1940700852573356032", "selectApplyId": "1941088269851607040", "projectName": "中山市第一职业技术学校智慧课程建设项目", "selectName": "中山移动中山市第一职业技术学校智慧课程建设项目", "count": "1", "projectNo": "CMGDZSICT20250703027", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "30", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-03 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-03 15:07:13", "isFixedSoftness": "0", "createStaff": "huangyijun2", "createStaffValue": "黄奕俊", "nextTodoHandler": "huangyijun2", "nextTodoHandlerValue": "黄奕俊", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940620473380290560", "workOrderMsgId": "GD76020250703113310790860", "shutOrderMsgId": null, "selectMsgId": "1941038380107087872", "selectApplyId": "1941098200453529600", "projectName": "中山市第一中学教育装备、医务室设备等物资采购项目", "selectName": "中山市第一中学教育装备、医务室设备等物资采购项目", "count": "1", "projectNo": "CMGDZSICT20250625079", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-03 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-03 11:55:50", "isFixedSoftness": "0", "createStaff": "wangxiaohua2", "createStaffValue": "汪晓华", "nextTodoHandler": "wangxiaohua2", "nextTodoHandlerValue": "汪晓华", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940221567911772160", "workOrderMsgId": "GD76020250702092609860107", "shutOrderMsgId": null, "selectMsgId": "1940229628432924672", "selectApplyId": "1940957015177084928", "projectName": "中山市第二人民医院智能发光药筐系统采购项目", "selectName": "2025智能发光药筐系统项目（第二次）", "count": "1", "projectNo": "CMGDZSICT20250619024", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-04 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-02 09:30:44", "isFixedSoftness": "0", "createStaff": "sucaiping", "createStaffValue": "苏彩萍", "nextTodoHandler": "sucaiping", "nextTodoHandlerValue": "苏彩萍", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}], "pages": 145}}, "content_type": "application/json;charset=UTF-8"}, "parsed_request": {"url_path": "/partner/materialManage/pnrSelectProject/querySelectProjectList", "query_parameters": {}, "body_parameters": {"selecCategory": "1", "currentPage": 1, "pageSize": 10}, "headers": {"content-type": "application/json;charset=UTF-8"}}, "parsed_response": {"status_code": 200, "content_type": "application/json;charset=UTF-8", "headers": {"content-type": "application/json;charset=UTF-8"}, "body_structure": {"busiDate": {"type": "str", "path": "busiDate"}, "code": {"type": "str", "path": "code"}, "message": {"type": "NoneType", "path": "message"}, "resultBody": {"type": "dict", "path": "resultBody"}}, "data_fields": [{"field_name": "busiDate", "field_path": "busiDate", "data_type": "str", "sample_value": "2025-07-08 19:51:02"}, {"field_name": "code", "field_path": "code", "data_type": "str", "sample_value": "000000"}, {"field_name": "message", "field_path": "message", "data_type": "NoneType", "sample_value": null}, {"field_name": "resultBody", "field_path": "resultBody", "data_type": "dict", "sample_value": "{'total': 1445, 'size': 10, 'current': 1, 'records': [{'projectMsgId': '1942422593200898048', 'workO"}, {"field_name": "total", "field_path": "resultBody.total", "data_type": "int", "sample_value": "1445"}, {"field_name": "size", "field_path": "resultBody.size", "data_type": "int", "sample_value": "10"}, {"field_name": "current", "field_path": "resultBody.current", "data_type": "int", "sample_value": "1"}, {"field_name": "records", "field_path": "resultBody.records", "data_type": "list", "sample_value": "[{'projectMsgId': '1942422593200898048', 'workOrderMsgId': 'GD76020250708111442151796', 'shutOrderMs"}, {"field_name": "projectMsgId", "field_path": "resultBody.records[0].projectMsgId", "data_type": "str", "sample_value": "1942422593200898048"}, {"field_name": "workOrderMsgId", "field_path": "resultBody.records[0].workOrderMsgId", "data_type": "str", "sample_value": "GD76020250708111442151796"}, {"field_name": "shutOrderMsgId", "field_path": "resultBody.records[0].shutOrderMsgId", "data_type": "NoneType", "sample_value": null}, {"field_name": "selectMsgId", "field_path": "resultBody.records[0].selectMsgId", "data_type": "NoneType", "sample_value": null}, {"field_name": "selectApplyId", "field_path": "resultBody.records[0].selectApplyId", "data_type": "NoneType", "sample_value": null}, {"field_name": "projectName", "field_path": "resultBody.records[0].projectName", "data_type": "str", "sample_value": "中山市坤鹏电子科技有限公司信息化建设项目"}, {"field_name": "selectName", "field_path": "resultBody.records[0].selectName", "data_type": "str", "sample_value": "中山移动某智慧园区项目"}, {"field_name": "count", "field_path": "resultBody.records[0].count", "data_type": "str", "sample_value": "0"}, {"field_name": "projectNo", "field_path": "resultBody.records[0].projectNo", "data_type": "str", "sample_value": "CMGDZSICT20250707037"}, {"field_name": "selectType", "field_path": "resultBody.records[0].selectType", "data_type": "NoneType", "sample_value": null}, {"field_name": "selectTypeValue", "field_path": "resultBody.records[0].selectTypeValue", "data_type": "NoneType", "sample_value": null}, {"field_name": "projectType", "field_path": "resultBody.records[0].projectType", "data_type": "str", "sample_value": "10"}, {"field_name": "projectLabel", "field_path": "resultBody.records[0].projectLabel", "data_type": "str", "sample_value": "54"}, {"field_name": "businessArea", "field_path": "resultBody.records[0].businessArea", "data_type": "str", "sample_value": "760"}, {"field_name": "businessAreaValue", "field_path": "resultBody.records[0].businessAreaValue", "data_type": "str", "sample_value": "中山"}, {"field_name": "startTime", "field_path": "resultBody.records[0].startTime", "data_type": "str", "sample_value": "2025-07-10 00:00:00"}, {"field_name": "selectStatus", "field_path": "resultBody.records[0].selectStatus", "data_type": "str", "sample_value": "1001"}, {"field_name": "selectStatusValue", "field_path": "resultBody.records[0].selectStatusValue", "data_type": "str", "sample_value": "审核通过"}, {"field_name": "initiateDepartment", "field_path": "resultBody.records[0].initiateDepartment", "data_type": "NoneType", "sample_value": null}, {"field_name": "createTime", "field_path": "resultBody.records[0].createTime", "data_type": "str", "sample_value": "2025-07-08 11:16:49"}, {"field_name": "isFixedSoftness", "field_path": "resultBody.records[0].isFixedSoftness", "data_type": "str", "sample_value": "0"}, {"field_name": "createStaff", "field_path": "resultBody.records[0].createStaff", "data_type": "str", "sample_value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"field_name": "createStaffValue", "field_path": "resultBody.records[0].createStaffValue", "data_type": "str", "sample_value": "刘桓旭"}, {"field_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "field_path": "resultBody.records[0].nextTodoHandler", "data_type": "str", "sample_value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"field_name": "nextTodoHandlerValue", "field_path": "resultBody.records[0].nextTodoHandlerValue", "data_type": "str", "sample_value": "刘桓旭"}, {"field_name": "isOperable", "field_path": "resultBody.records[0].isOperable", "data_type": "str", "sample_value": "0"}, {"field_name": "changeType1", "field_path": "resultBody.records[0].changeType1", "data_type": "NoneType", "sample_value": null}, {"field_name": "changeType2", "field_path": "resultBody.records[0].changeType2", "data_type": "NoneType", "sample_value": null}, {"field_name": "isTerminable", "field_path": "resultBody.records[0].isTerminable", "data_type": "str", "sample_value": "0"}, {"field_name": "isAllowSecond", "field_path": "resultBody.records[0].isAllowSecond", "data_type": "NoneType", "sample_value": null}, {"field_name": "selectCategory", "field_path": "resultBody.records[0].selectCategory", "data_type": "str", "sample_value": "1"}, {"field_name": "selectCategoryValue", "field_path": "resultBody.records[0].selectCategoryValue", "data_type": "str", "sample_value": "项目甄选"}, {"field_name": "dpcsSelectSecondNegotiate", "field_path": "resultBody.records[0].dpcsSelectSecondNegotiate", "data_type": "NoneType", "sample_value": null}, {"field_name": "pages", "field_path": "resultBody.pages", "data_type": "int", "sample_value": "145"}]}}, {"index": 7, "url": "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "method": "POST", "request_data": {"timestamp": "2025-07-08T19:51:03.306585", "url": "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "method": "POST", "headers": {"sec-ch-ua-platform": "\"Windows\"", "referer": "https://dict.gmcc.net:30722/ptn/main/selectDemand", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\"", "content-type": "application/json;charset=UTF-8", "sec-ch-ua-mobile": "?0"}, "post_data": "{\"selecCategory\":\"1\",\"currentPage\":1,\"pageSize\":10}", "post_data_json": {"selecCategory": "1", "currentPage": 1, "pageSize": 10}, "query_params": {}, "resource_type": "xhr"}, "response_data": {"timestamp": "2025-07-08T19:51:05.672904", "url": "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "status": 200, "status_text": "", "headers": {"transfer-encoding": "chunked", "location_gray": "location_rest_v1", "date": "<PERSON><PERSON>, 08 Jul 2025 11:51:02 GMT", "content-type": "application/json;charset=UTF-8", "server": "nginx", "connection": "keep-alive"}, "body": "{\"busiDate\":\"2025-07-08 19:51:02\",\"code\":\"000000\",\"message\":null,\"resultBody\":{\"total\":1445,\"size\":10,\"current\":1,\"records\":[{\"projectMsgId\":\"1942422593200898048\",\"workOrderMsgId\":\"GD76020250708111442151796\",\"shutOrderMsgId\":null,\"selectMsgId\":null,\"selectApplyId\":null,\"projectName\":\"中山市坤鹏电子科技有限公司信息化建设项目\",\"selectName\":\"中山移动某智慧园区项目\",\"count\":\"0\",\"projectNo\":\"CMGDZSICT20250707037\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"54\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2025-07-10 00:00:00\",\"selectStatus\":\"1001\",\"selectStatusValue\":\"审核通过\",\"initiateDepartment\":null,\"createTime\":\"2025-07-08 11:16:49\",\"isFixedSoftness\":\"0\",\"createStaff\":\"liuhuanxu\",\"createStaffValue\":\"刘桓旭\",\"nextTodoHandler\":\"liuhuanxu\",\"nextTodoHandlerValue\":\"刘桓旭\",\"isOperable\":\"0\",\"changeType1\":null,\"changeType2\":null,\"isTerminable\":\"0\",\"isAllowSecond\":null,\"selectCategory\":\"1\",\"selectCategoryValue\":\"项目甄选\",\"dpcsSelectSecondNegotiate\":null},{\"projectMsgId\":\"1942116921179553792\",\"workOrderMsgId\":\"GD76020250707145959860730\",\"shutOrderMsgId\":null,\"selectMsgId\":\"1942400615916158976\",\"selectApplyId\":null,\"projectName\":\"中山市火炬开发区第一幼儿园2025年智慧安防进校园门禁系统维护项目\",\"selectName\":\"中山移动校园门禁项目\",\"count\":\"1\",\"projectNo\":\"CMGDZSICT20250514031\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"54\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2023-03-07 00:00:00\",\"selectStatus\":\"1002\",\"selectStatusValue\":\"审核通过(已制定方案)\",\"initiateDepartment\":null,\"createTime\":\"2025-07-07 15:02:11\",\"isFixedSoftness\":\"0\",\"createStaff\":\"zhongyuan01\",\"createStaffValue\":\"钟源\",\"nextTodoHandler\":\"zhongyuan01\",\"nextTodoHandlerValue\":\"钟源\",\"isOperable\":\"0\",\"changeType1\":null,\"changeType2\":null,\"isTerminable\":\"0\",\"isAllowSecond\":null,\"selectCategory\":\"1\",\"selectCategoryValue\":\"项目甄选\",\"dpcsSelectSecondNegotiate\":null},{\"projectMsgId\":\"1942069083955445760\",\"workOrderMsgId\":\"GD76020250707114834742565\",\"shutOrderMsgId\":null,\"selectMsgId\":\"1942114146337079296\",\"selectApplyId\":\"1942141579274665984\",\"projectName\":\"横栏消防支队LCD+AP+报警项目\",\"selectName\":\"中山移动横栏消防救援支队LCD+报警项目\",\"count\":\"1\",\"projectNo\":\"CMGDZSICT20250603017\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"30\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2025-06-16 00:00:00\",\"selectStatus\":\"1002\",\"selectStatusValue\":\"审核通过(已制定方案)\",\"initiateDepartment\":null,\"createTime\":\"2025-07-07 11:52:06\",\"isFixedSoftness\":\"0\",\"createStaff\":\"huangyijun2\",\"createStaffValue\":\"黄奕俊\",\"nextTodoHandler\":\"huangyijun2\",\"nextTodoHandlerValue\":\"黄奕俊\",\"isOperable\":\"0\",\"changeType1\":null,\"changeType2\":null,\"isTerminable\":\"0\",\"isAllowSecond\":null,\"selectCategory\":\"1\",\"selectCategoryValue\":\"项目甄选\",\"dpcsSelectSecondNegotiate\":null},{\"projectMsgId\":\"1942023416348327936\",\"workOrderMsgId\":\"GD76020250707084708189250\",\"shutOrderMsgId\":null,\"selectMsgId\":\"1942387861012529152\",\"selectApplyId\":\"1942490547577864192\",\"projectName\":\"社会保险业务智能稽核复审项目\",\"selectName\":\"中山移动业务智能稽核复审项目\",\"count\":\"1\",\"projectNo\":\"CMGDZSICT20250707001\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"54\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2025-07-07 00:00:00\",\"selectStatus\":\"1002\",\"selectStatusValue\":\"审核通过(已制定方案)\",\"initiateDepartment\":null,\"createTime\":\"2025-07-07 08:50:38\",\"isFixedSoftness\":\"0\",\"createStaff\":\"oujiahui3\",\"createStaffValue\":\"欧嘉慧\",\"nextTodoHandler\":\"oujiahui3\",\"nextTodoHandlerValue\":\"欧嘉慧\",\"isOperable\":\"0\",\"changeType1\":null,\"changeType2\":null,\"isTerminable\":\"0\",\"isAllowSecond\":null,\"selectCategory\":\"1\",\"selectCategoryValue\":\"项目甄选\",\"dpcsSelectSecondNegotiate\":null},{\"projectMsgId\":\"1940961734821855232\",\"workOrderMsgId\":\"GD76020250704102830314437\",\"shutOrderMsgId\":null,\"selectMsgId\":\"1942029594583285760\",\"selectApplyId\":\"1942138444196003840\",\"projectName\":\"嘉钦工业园区智能化项目\",\"selectName\":\"中山移动嘉钦工业园区智能化项目\",\"count\":\"1\",\"projectNo\":\"CMGDZSICT20250616063\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"10\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2025-07-04 00:00:00\",\"selectStatus\":\"1002\",\"selectStatusValue\":\"审核通过(已制定方案)\",\"initiateDepartment\":null,\"createTime\":\"2025-07-04 10:31:53\",\"isFixedSoftness\":\"0\",\"createStaff\":\"hehaoming\",\"createStaffValue\":\"何浩明\",\"nextTodoHandler\":\"hehaoming\",\"nextTodoHandlerValue\":\"何浩明\",\"isOperable\":\"0\",\"changeType1\":null,\"changeType2\":null,\"isTerminable\":\"0\",\"isAllowSecond\":null,\"selectCategory\":\"1\",\"selectCategoryValue\":\"项目甄选\",\"dpcsSelectSecondNegotiate\":null},{\"projectMsgId\":\"1940953867796529152\",\"workOrderMsgId\":\"GD76020250704095622517846\",\"shutOrderMsgId\":null,\"selectMsgId\":\"1942032646556008448\",\"selectApplyId\":\"1942138704502898688\",\"projectName\":\"绩东一德原北路三线下地整治工程\",\"selectName\":\"中山移动小榄镇某村信息化项目（第二次）\",\"count\":\"1\",\"projectNo\":\"CMGDZSICT20250604017\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"54\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2025-07-18 00:00:00\",\"selectStatus\":\"1002\",\"selectStatusValue\":\"审核通过(已制定方案)\",\"initiateDepartmen", "body_json": {"busiDate": "2025-07-08 19:51:02", "code": "000000", "message": null, "resultBody": {"total": 1445, "size": 10, "current": 1, "records": [{"projectMsgId": "1942422593200898048", "workOrderMsgId": "GD76020250708111442151796", "shutOrderMsgId": null, "selectMsgId": null, "selectApplyId": null, "projectName": "中山市坤鹏电子科技有限公司信息化建设项目", "selectName": "中山移动某智慧园区项目", "count": "0", "projectNo": "CMGDZSICT20250707037", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-10 00:00:00", "selectStatus": "1001", "selectStatusValue": "审核通过", "initiateDepartment": null, "createTime": "2025-07-08 11:16:49", "isFixedSoftness": "0", "createStaff": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createStaffValue": "刘桓旭", "nextTodoHandler": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nextTodoHandlerValue": "刘桓旭", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1942116921179553792", "workOrderMsgId": "GD76020250707145959860730", "shutOrderMsgId": null, "selectMsgId": "1942400615916158976", "selectApplyId": null, "projectName": "中山市火炬开发区第一幼儿园2025年智慧安防进校园门禁系统维护项目", "selectName": "中山移动校园门禁项目", "count": "1", "projectNo": "CMGDZSICT20250514031", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2023-03-07 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-07 15:02:11", "isFixedSoftness": "0", "createStaff": "zhongyuan01", "createStaffValue": "钟源", "nextTodoHandler": "zhongyuan01", "nextTodoHandlerValue": "钟源", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1942069083955445760", "workOrderMsgId": "GD76020250707114834742565", "shutOrderMsgId": null, "selectMsgId": "1942114146337079296", "selectApplyId": "1942141579274665984", "projectName": "横栏消防支队LCD+AP+报警项目", "selectName": "中山移动横栏消防救援支队LCD+报警项目", "count": "1", "projectNo": "CMGDZSICT20250603017", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "30", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-06-16 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-07 11:52:06", "isFixedSoftness": "0", "createStaff": "huangyijun2", "createStaffValue": "黄奕俊", "nextTodoHandler": "huangyijun2", "nextTodoHandlerValue": "黄奕俊", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1942023416348327936", "workOrderMsgId": "GD76020250707084708189250", "shutOrderMsgId": null, "selectMsgId": "1942387861012529152", "selectApplyId": "1942490547577864192", "projectName": "社会保险业务智能稽核复审项目", "selectName": "中山移动业务智能稽核复审项目", "count": "1", "projectNo": "CMGDZSICT20250707001", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-07 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-07 08:50:38", "isFixedSoftness": "0", "createStaff": "oujiahui3", "createStaffValue": "欧嘉慧", "nextTodoHandler": "oujiahui3", "nextTodoHandlerValue": "欧嘉慧", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940961734821855232", "workOrderMsgId": "GD76020250704102830314437", "shutOrderMsgId": null, "selectMsgId": "1942029594583285760", "selectApplyId": "1942138444196003840", "projectName": "嘉钦工业园区智能化项目", "selectName": "中山移动嘉钦工业园区智能化项目", "count": "1", "projectNo": "CMGDZSICT20250616063", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "10", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-04 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-04 10:31:53", "isFixedSoftness": "0", "createStaff": "<PERSON><PERSON><PERSON>", "createStaffValue": "何浩明", "nextTodoHandler": "<PERSON><PERSON><PERSON>", "nextTodoHandlerValue": "何浩明", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940953867796529152", "workOrderMsgId": "GD76020250704095622517846", "shutOrderMsgId": null, "selectMsgId": "1942032646556008448", "selectApplyId": "1942138704502898688", "projectName": "绩东一德原北路三线下地整治工程", "selectName": "中山移动小榄镇某村信息化项目（第二次）", "count": "1", "projectNo": "CMGDZSICT20250604017", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-18 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-04 10:00:37", "isFixedSoftness": "0", "createStaff": "<PERSON><PERSON><PERSON>", "createStaffValue": "何浩明", "nextTodoHandler": "<PERSON><PERSON><PERSON>", "nextTodoHandlerValue": "何浩明", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940705441884323840", "workOrderMsgId": "GD76020250703173321491482", "shutOrderMsgId": null, "selectMsgId": "1942149984940900352", "selectApplyId": null, "projectName": "中山市南朗医院信息应急系统建设服务项目", "selectName": "中山移动C镇区信息应急系统建设服务项目", "count": "1", "projectNo": "CMGDZSICT20250626070", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-07 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-03 17:33:28", "isFixedSoftness": "0", "createStaff": "<PERSON><PERSON><PERSON><PERSON>", "createStaffValue": "李远锋", "nextTodoHandler": "<PERSON><PERSON><PERSON><PERSON>", "nextTodoHandlerValue": "李远锋", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940668636770254848", "workOrderMsgId": "GD76020250703150544590539", "shutOrderMsgId": null, "selectMsgId": "1940700852573356032", "selectApplyId": "1941088269851607040", "projectName": "中山市第一职业技术学校智慧课程建设项目", "selectName": "中山移动中山市第一职业技术学校智慧课程建设项目", "count": "1", "projectNo": "CMGDZSICT20250703027", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "30", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-03 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-03 15:07:13", "isFixedSoftness": "0", "createStaff": "huangyijun2", "createStaffValue": "黄奕俊", "nextTodoHandler": "huangyijun2", "nextTodoHandlerValue": "黄奕俊", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940620473380290560", "workOrderMsgId": "GD76020250703113310790860", "shutOrderMsgId": null, "selectMsgId": "1941038380107087872", "selectApplyId": "1941098200453529600", "projectName": "中山市第一中学教育装备、医务室设备等物资采购项目", "selectName": "中山市第一中学教育装备、医务室设备等物资采购项目", "count": "1", "projectNo": "CMGDZSICT20250625079", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-03 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-03 11:55:50", "isFixedSoftness": "0", "createStaff": "wangxiaohua2", "createStaffValue": "汪晓华", "nextTodoHandler": "wangxiaohua2", "nextTodoHandlerValue": "汪晓华", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940221567911772160", "workOrderMsgId": "GD76020250702092609860107", "shutOrderMsgId": null, "selectMsgId": "1940229628432924672", "selectApplyId": "1940957015177084928", "projectName": "中山市第二人民医院智能发光药筐系统采购项目", "selectName": "2025智能发光药筐系统项目（第二次）", "count": "1", "projectNo": "CMGDZSICT20250619024", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-04 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-02 09:30:44", "isFixedSoftness": "0", "createStaff": "sucaiping", "createStaffValue": "苏彩萍", "nextTodoHandler": "sucaiping", "nextTodoHandlerValue": "苏彩萍", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}], "pages": 145}}, "content_type": "application/json;charset=UTF-8"}, "parsed_request": {"url_path": "/partner/materialManage/pnrSelectProject/querySelectProjectList", "query_parameters": {}, "body_parameters": {"selecCategory": "1", "currentPage": 1, "pageSize": 10}, "headers": {"content-type": "application/json;charset=UTF-8"}}, "parsed_response": {"status_code": 200, "content_type": "application/json;charset=UTF-8", "headers": {"content-type": "application/json;charset=UTF-8"}, "body_structure": {"busiDate": {"type": "str", "path": "busiDate"}, "code": {"type": "str", "path": "code"}, "message": {"type": "NoneType", "path": "message"}, "resultBody": {"type": "dict", "path": "resultBody"}}, "data_fields": [{"field_name": "busiDate", "field_path": "busiDate", "data_type": "str", "sample_value": "2025-07-08 19:51:02"}, {"field_name": "code", "field_path": "code", "data_type": "str", "sample_value": "000000"}, {"field_name": "message", "field_path": "message", "data_type": "NoneType", "sample_value": null}, {"field_name": "resultBody", "field_path": "resultBody", "data_type": "dict", "sample_value": "{'total': 1445, 'size': 10, 'current': 1, 'records': [{'projectMsgId': '1942422593200898048', 'workO"}, {"field_name": "total", "field_path": "resultBody.total", "data_type": "int", "sample_value": "1445"}, {"field_name": "size", "field_path": "resultBody.size", "data_type": "int", "sample_value": "10"}, {"field_name": "current", "field_path": "resultBody.current", "data_type": "int", "sample_value": "1"}, {"field_name": "records", "field_path": "resultBody.records", "data_type": "list", "sample_value": "[{'projectMsgId': '1942422593200898048', 'workOrderMsgId': 'GD76020250708111442151796', 'shutOrderMs"}, {"field_name": "projectMsgId", "field_path": "resultBody.records[0].projectMsgId", "data_type": "str", "sample_value": "1942422593200898048"}, {"field_name": "workOrderMsgId", "field_path": "resultBody.records[0].workOrderMsgId", "data_type": "str", "sample_value": "GD76020250708111442151796"}, {"field_name": "shutOrderMsgId", "field_path": "resultBody.records[0].shutOrderMsgId", "data_type": "NoneType", "sample_value": null}, {"field_name": "selectMsgId", "field_path": "resultBody.records[0].selectMsgId", "data_type": "NoneType", "sample_value": null}, {"field_name": "selectApplyId", "field_path": "resultBody.records[0].selectApplyId", "data_type": "NoneType", "sample_value": null}, {"field_name": "projectName", "field_path": "resultBody.records[0].projectName", "data_type": "str", "sample_value": "中山市坤鹏电子科技有限公司信息化建设项目"}, {"field_name": "selectName", "field_path": "resultBody.records[0].selectName", "data_type": "str", "sample_value": "中山移动某智慧园区项目"}, {"field_name": "count", "field_path": "resultBody.records[0].count", "data_type": "str", "sample_value": "0"}, {"field_name": "projectNo", "field_path": "resultBody.records[0].projectNo", "data_type": "str", "sample_value": "CMGDZSICT20250707037"}, {"field_name": "selectType", "field_path": "resultBody.records[0].selectType", "data_type": "NoneType", "sample_value": null}, {"field_name": "selectTypeValue", "field_path": "resultBody.records[0].selectTypeValue", "data_type": "NoneType", "sample_value": null}, {"field_name": "projectType", "field_path": "resultBody.records[0].projectType", "data_type": "str", "sample_value": "10"}, {"field_name": "projectLabel", "field_path": "resultBody.records[0].projectLabel", "data_type": "str", "sample_value": "54"}, {"field_name": "businessArea", "field_path": "resultBody.records[0].businessArea", "data_type": "str", "sample_value": "760"}, {"field_name": "businessAreaValue", "field_path": "resultBody.records[0].businessAreaValue", "data_type": "str", "sample_value": "中山"}, {"field_name": "startTime", "field_path": "resultBody.records[0].startTime", "data_type": "str", "sample_value": "2025-07-10 00:00:00"}, {"field_name": "selectStatus", "field_path": "resultBody.records[0].selectStatus", "data_type": "str", "sample_value": "1001"}, {"field_name": "selectStatusValue", "field_path": "resultBody.records[0].selectStatusValue", "data_type": "str", "sample_value": "审核通过"}, {"field_name": "initiateDepartment", "field_path": "resultBody.records[0].initiateDepartment", "data_type": "NoneType", "sample_value": null}, {"field_name": "createTime", "field_path": "resultBody.records[0].createTime", "data_type": "str", "sample_value": "2025-07-08 11:16:49"}, {"field_name": "isFixedSoftness", "field_path": "resultBody.records[0].isFixedSoftness", "data_type": "str", "sample_value": "0"}, {"field_name": "createStaff", "field_path": "resultBody.records[0].createStaff", "data_type": "str", "sample_value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"field_name": "createStaffValue", "field_path": "resultBody.records[0].createStaffValue", "data_type": "str", "sample_value": "刘桓旭"}, {"field_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "field_path": "resultBody.records[0].nextTodoHandler", "data_type": "str", "sample_value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"field_name": "nextTodoHandlerValue", "field_path": "resultBody.records[0].nextTodoHandlerValue", "data_type": "str", "sample_value": "刘桓旭"}, {"field_name": "isOperable", "field_path": "resultBody.records[0].isOperable", "data_type": "str", "sample_value": "0"}, {"field_name": "changeType1", "field_path": "resultBody.records[0].changeType1", "data_type": "NoneType", "sample_value": null}, {"field_name": "changeType2", "field_path": "resultBody.records[0].changeType2", "data_type": "NoneType", "sample_value": null}, {"field_name": "isTerminable", "field_path": "resultBody.records[0].isTerminable", "data_type": "str", "sample_value": "0"}, {"field_name": "isAllowSecond", "field_path": "resultBody.records[0].isAllowSecond", "data_type": "NoneType", "sample_value": null}, {"field_name": "selectCategory", "field_path": "resultBody.records[0].selectCategory", "data_type": "str", "sample_value": "1"}, {"field_name": "selectCategoryValue", "field_path": "resultBody.records[0].selectCategoryValue", "data_type": "str", "sample_value": "项目甄选"}, {"field_name": "dpcsSelectSecondNegotiate", "field_path": "resultBody.records[0].dpcsSelectSecondNegotiate", "data_type": "NoneType", "sample_value": null}, {"field_name": "pages", "field_path": "resultBody.pages", "data_type": "int", "sample_value": "145"}]}}, {"index": 8, "url": "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "method": "POST", "request_data": {"timestamp": "2025-07-08T19:51:13.751096", "url": "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "method": "POST", "headers": {"sec-ch-ua-platform": "\"Windows\"", "referer": "https://dict.gmcc.net:30722/ptn/main/selectDemand", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\"", "content-type": "application/json;charset=UTF-8", "sec-ch-ua-mobile": "?0"}, "post_data": "{\"selectCategory\":[\"1\",\"3\"],\"projectName\":\"\",\"projectNo\":\"\",\"selectName\":\"\",\"businessArea\":\"\",\"selectStatus\":\"\",\"projectMsgId\":\"\",\"currentPage\":1,\"pageSize\":10}", "post_data_json": {"selectCategory": ["1", "3"], "projectName": "", "projectNo": "", "selectName": "", "businessArea": "", "selectStatus": "", "projectMsgId": "", "currentPage": 1, "pageSize": 10}, "query_params": {}, "resource_type": "xhr"}, "response_data": {"timestamp": "2025-07-08T19:51:15.734960", "url": "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "status": 200, "status_text": "", "headers": {"transfer-encoding": "chunked", "location_gray": "location_rest_v1", "date": "<PERSON><PERSON>, 08 Jul 2025 11:51:12 GMT", "content-type": "application/json;charset=UTF-8", "server": "nginx", "connection": "keep-alive"}, "body": "{\"busiDate\":\"2025-07-08 19:51:12\",\"code\":\"000000\",\"message\":null,\"resultBody\":{\"total\":1445,\"size\":10,\"current\":1,\"records\":[{\"projectMsgId\":\"1942422593200898048\",\"workOrderMsgId\":\"GD76020250708111442151796\",\"shutOrderMsgId\":null,\"selectMsgId\":null,\"selectApplyId\":null,\"projectName\":\"中山市坤鹏电子科技有限公司信息化建设项目\",\"selectName\":\"中山移动某智慧园区项目\",\"count\":\"0\",\"projectNo\":\"CMGDZSICT20250707037\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"54\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2025-07-10 00:00:00\",\"selectStatus\":\"1001\",\"selectStatusValue\":\"审核通过\",\"initiateDepartment\":null,\"createTime\":\"2025-07-08 11:16:49\",\"isFixedSoftness\":\"0\",\"createStaff\":\"liuhuanxu\",\"createStaffValue\":\"刘桓旭\",\"nextTodoHandler\":\"liuhuanxu\",\"nextTodoHandlerValue\":\"刘桓旭\",\"isOperable\":\"0\",\"changeType1\":null,\"changeType2\":null,\"isTerminable\":\"0\",\"isAllowSecond\":null,\"selectCategory\":\"1\",\"selectCategoryValue\":\"项目甄选\",\"dpcsSelectSecondNegotiate\":null},{\"projectMsgId\":\"1942116921179553792\",\"workOrderMsgId\":\"GD76020250707145959860730\",\"shutOrderMsgId\":null,\"selectMsgId\":\"1942400615916158976\",\"selectApplyId\":null,\"projectName\":\"中山市火炬开发区第一幼儿园2025年智慧安防进校园门禁系统维护项目\",\"selectName\":\"中山移动校园门禁项目\",\"count\":\"1\",\"projectNo\":\"CMGDZSICT20250514031\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"54\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2023-03-07 00:00:00\",\"selectStatus\":\"1002\",\"selectStatusValue\":\"审核通过(已制定方案)\",\"initiateDepartment\":null,\"createTime\":\"2025-07-07 15:02:11\",\"isFixedSoftness\":\"0\",\"createStaff\":\"zhongyuan01\",\"createStaffValue\":\"钟源\",\"nextTodoHandler\":\"zhongyuan01\",\"nextTodoHandlerValue\":\"钟源\",\"isOperable\":\"0\",\"changeType1\":null,\"changeType2\":null,\"isTerminable\":\"0\",\"isAllowSecond\":null,\"selectCategory\":\"1\",\"selectCategoryValue\":\"项目甄选\",\"dpcsSelectSecondNegotiate\":null},{\"projectMsgId\":\"1942069083955445760\",\"workOrderMsgId\":\"GD76020250707114834742565\",\"shutOrderMsgId\":null,\"selectMsgId\":\"1942114146337079296\",\"selectApplyId\":\"1942141579274665984\",\"projectName\":\"横栏消防支队LCD+AP+报警项目\",\"selectName\":\"中山移动横栏消防救援支队LCD+报警项目\",\"count\":\"1\",\"projectNo\":\"CMGDZSICT20250603017\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"30\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2025-06-16 00:00:00\",\"selectStatus\":\"1002\",\"selectStatusValue\":\"审核通过(已制定方案)\",\"initiateDepartment\":null,\"createTime\":\"2025-07-07 11:52:06\",\"isFixedSoftness\":\"0\",\"createStaff\":\"huangyijun2\",\"createStaffValue\":\"黄奕俊\",\"nextTodoHandler\":\"huangyijun2\",\"nextTodoHandlerValue\":\"黄奕俊\",\"isOperable\":\"0\",\"changeType1\":null,\"changeType2\":null,\"isTerminable\":\"0\",\"isAllowSecond\":null,\"selectCategory\":\"1\",\"selectCategoryValue\":\"项目甄选\",\"dpcsSelectSecondNegotiate\":null},{\"projectMsgId\":\"1942023416348327936\",\"workOrderMsgId\":\"GD76020250707084708189250\",\"shutOrderMsgId\":null,\"selectMsgId\":\"1942387861012529152\",\"selectApplyId\":\"1942490547577864192\",\"projectName\":\"社会保险业务智能稽核复审项目\",\"selectName\":\"中山移动业务智能稽核复审项目\",\"count\":\"1\",\"projectNo\":\"CMGDZSICT20250707001\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"54\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2025-07-07 00:00:00\",\"selectStatus\":\"1002\",\"selectStatusValue\":\"审核通过(已制定方案)\",\"initiateDepartment\":null,\"createTime\":\"2025-07-07 08:50:38\",\"isFixedSoftness\":\"0\",\"createStaff\":\"oujiahui3\",\"createStaffValue\":\"欧嘉慧\",\"nextTodoHandler\":\"oujiahui3\",\"nextTodoHandlerValue\":\"欧嘉慧\",\"isOperable\":\"0\",\"changeType1\":null,\"changeType2\":null,\"isTerminable\":\"0\",\"isAllowSecond\":null,\"selectCategory\":\"1\",\"selectCategoryValue\":\"项目甄选\",\"dpcsSelectSecondNegotiate\":null},{\"projectMsgId\":\"1940961734821855232\",\"workOrderMsgId\":\"GD76020250704102830314437\",\"shutOrderMsgId\":null,\"selectMsgId\":\"1942029594583285760\",\"selectApplyId\":\"1942138444196003840\",\"projectName\":\"嘉钦工业园区智能化项目\",\"selectName\":\"中山移动嘉钦工业园区智能化项目\",\"count\":\"1\",\"projectNo\":\"CMGDZSICT20250616063\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"10\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2025-07-04 00:00:00\",\"selectStatus\":\"1002\",\"selectStatusValue\":\"审核通过(已制定方案)\",\"initiateDepartment\":null,\"createTime\":\"2025-07-04 10:31:53\",\"isFixedSoftness\":\"0\",\"createStaff\":\"hehaoming\",\"createStaffValue\":\"何浩明\",\"nextTodoHandler\":\"hehaoming\",\"nextTodoHandlerValue\":\"何浩明\",\"isOperable\":\"0\",\"changeType1\":null,\"changeType2\":null,\"isTerminable\":\"0\",\"isAllowSecond\":null,\"selectCategory\":\"1\",\"selectCategoryValue\":\"项目甄选\",\"dpcsSelectSecondNegotiate\":null},{\"projectMsgId\":\"1940953867796529152\",\"workOrderMsgId\":\"GD76020250704095622517846\",\"shutOrderMsgId\":null,\"selectMsgId\":\"1942032646556008448\",\"selectApplyId\":\"1942138704502898688\",\"projectName\":\"绩东一德原北路三线下地整治工程\",\"selectName\":\"中山移动小榄镇某村信息化项目（第二次）\",\"count\":\"1\",\"projectNo\":\"CMGDZSICT20250604017\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"54\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2025-07-18 00:00:00\",\"selectStatus\":\"1002\",\"selectStatusValue\":\"审核通过(已制定方案)\",\"initiateDepartmen", "body_json": {"busiDate": "2025-07-08 19:51:12", "code": "000000", "message": null, "resultBody": {"total": 1445, "size": 10, "current": 1, "records": [{"projectMsgId": "1942422593200898048", "workOrderMsgId": "GD76020250708111442151796", "shutOrderMsgId": null, "selectMsgId": null, "selectApplyId": null, "projectName": "中山市坤鹏电子科技有限公司信息化建设项目", "selectName": "中山移动某智慧园区项目", "count": "0", "projectNo": "CMGDZSICT20250707037", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-10 00:00:00", "selectStatus": "1001", "selectStatusValue": "审核通过", "initiateDepartment": null, "createTime": "2025-07-08 11:16:49", "isFixedSoftness": "0", "createStaff": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createStaffValue": "刘桓旭", "nextTodoHandler": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nextTodoHandlerValue": "刘桓旭", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1942116921179553792", "workOrderMsgId": "GD76020250707145959860730", "shutOrderMsgId": null, "selectMsgId": "1942400615916158976", "selectApplyId": null, "projectName": "中山市火炬开发区第一幼儿园2025年智慧安防进校园门禁系统维护项目", "selectName": "中山移动校园门禁项目", "count": "1", "projectNo": "CMGDZSICT20250514031", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2023-03-07 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-07 15:02:11", "isFixedSoftness": "0", "createStaff": "zhongyuan01", "createStaffValue": "钟源", "nextTodoHandler": "zhongyuan01", "nextTodoHandlerValue": "钟源", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1942069083955445760", "workOrderMsgId": "GD76020250707114834742565", "shutOrderMsgId": null, "selectMsgId": "1942114146337079296", "selectApplyId": "1942141579274665984", "projectName": "横栏消防支队LCD+AP+报警项目", "selectName": "中山移动横栏消防救援支队LCD+报警项目", "count": "1", "projectNo": "CMGDZSICT20250603017", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "30", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-06-16 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-07 11:52:06", "isFixedSoftness": "0", "createStaff": "huangyijun2", "createStaffValue": "黄奕俊", "nextTodoHandler": "huangyijun2", "nextTodoHandlerValue": "黄奕俊", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1942023416348327936", "workOrderMsgId": "GD76020250707084708189250", "shutOrderMsgId": null, "selectMsgId": "1942387861012529152", "selectApplyId": "1942490547577864192", "projectName": "社会保险业务智能稽核复审项目", "selectName": "中山移动业务智能稽核复审项目", "count": "1", "projectNo": "CMGDZSICT20250707001", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-07 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-07 08:50:38", "isFixedSoftness": "0", "createStaff": "oujiahui3", "createStaffValue": "欧嘉慧", "nextTodoHandler": "oujiahui3", "nextTodoHandlerValue": "欧嘉慧", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940961734821855232", "workOrderMsgId": "GD76020250704102830314437", "shutOrderMsgId": null, "selectMsgId": "1942029594583285760", "selectApplyId": "1942138444196003840", "projectName": "嘉钦工业园区智能化项目", "selectName": "中山移动嘉钦工业园区智能化项目", "count": "1", "projectNo": "CMGDZSICT20250616063", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "10", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-04 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-04 10:31:53", "isFixedSoftness": "0", "createStaff": "<PERSON><PERSON><PERSON>", "createStaffValue": "何浩明", "nextTodoHandler": "<PERSON><PERSON><PERSON>", "nextTodoHandlerValue": "何浩明", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940953867796529152", "workOrderMsgId": "GD76020250704095622517846", "shutOrderMsgId": null, "selectMsgId": "1942032646556008448", "selectApplyId": "1942138704502898688", "projectName": "绩东一德原北路三线下地整治工程", "selectName": "中山移动小榄镇某村信息化项目（第二次）", "count": "1", "projectNo": "CMGDZSICT20250604017", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-18 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-04 10:00:37", "isFixedSoftness": "0", "createStaff": "<PERSON><PERSON><PERSON>", "createStaffValue": "何浩明", "nextTodoHandler": "<PERSON><PERSON><PERSON>", "nextTodoHandlerValue": "何浩明", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940705441884323840", "workOrderMsgId": "GD76020250703173321491482", "shutOrderMsgId": null, "selectMsgId": "1942149984940900352", "selectApplyId": null, "projectName": "中山市南朗医院信息应急系统建设服务项目", "selectName": "中山移动C镇区信息应急系统建设服务项目", "count": "1", "projectNo": "CMGDZSICT20250626070", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-07 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-03 17:33:28", "isFixedSoftness": "0", "createStaff": "<PERSON><PERSON><PERSON><PERSON>", "createStaffValue": "李远锋", "nextTodoHandler": "<PERSON><PERSON><PERSON><PERSON>", "nextTodoHandlerValue": "李远锋", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940668636770254848", "workOrderMsgId": "GD76020250703150544590539", "shutOrderMsgId": null, "selectMsgId": "1940700852573356032", "selectApplyId": "1941088269851607040", "projectName": "中山市第一职业技术学校智慧课程建设项目", "selectName": "中山移动中山市第一职业技术学校智慧课程建设项目", "count": "1", "projectNo": "CMGDZSICT20250703027", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "30", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-03 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-03 15:07:13", "isFixedSoftness": "0", "createStaff": "huangyijun2", "createStaffValue": "黄奕俊", "nextTodoHandler": "huangyijun2", "nextTodoHandlerValue": "黄奕俊", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940620473380290560", "workOrderMsgId": "GD76020250703113310790860", "shutOrderMsgId": null, "selectMsgId": "1941038380107087872", "selectApplyId": "1941098200453529600", "projectName": "中山市第一中学教育装备、医务室设备等物资采购项目", "selectName": "中山市第一中学教育装备、医务室设备等物资采购项目", "count": "1", "projectNo": "CMGDZSICT20250625079", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-03 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-03 11:55:50", "isFixedSoftness": "0", "createStaff": "wangxiaohua2", "createStaffValue": "汪晓华", "nextTodoHandler": "wangxiaohua2", "nextTodoHandlerValue": "汪晓华", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940221567911772160", "workOrderMsgId": "GD76020250702092609860107", "shutOrderMsgId": null, "selectMsgId": "1940229628432924672", "selectApplyId": "1940957015177084928", "projectName": "中山市第二人民医院智能发光药筐系统采购项目", "selectName": "2025智能发光药筐系统项目（第二次）", "count": "1", "projectNo": "CMGDZSICT20250619024", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-04 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-02 09:30:44", "isFixedSoftness": "0", "createStaff": "sucaiping", "createStaffValue": "苏彩萍", "nextTodoHandler": "sucaiping", "nextTodoHandlerValue": "苏彩萍", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}], "pages": 145}}, "content_type": "application/json;charset=UTF-8"}, "parsed_request": {"url_path": "/partner/materialManage/pnrSelectProject/querySelectProjectList", "query_parameters": {}, "body_parameters": {"selectCategory": ["1", "3"], "projectName": "", "projectNo": "", "selectName": "", "businessArea": "", "selectStatus": "", "projectMsgId": "", "currentPage": 1, "pageSize": 10}, "headers": {"content-type": "application/json;charset=UTF-8"}}, "parsed_response": {"status_code": 200, "content_type": "application/json;charset=UTF-8", "headers": {"content-type": "application/json;charset=UTF-8"}, "body_structure": {"busiDate": {"type": "str", "path": "busiDate"}, "code": {"type": "str", "path": "code"}, "message": {"type": "NoneType", "path": "message"}, "resultBody": {"type": "dict", "path": "resultBody"}}, "data_fields": [{"field_name": "busiDate", "field_path": "busiDate", "data_type": "str", "sample_value": "2025-07-08 19:51:12"}, {"field_name": "code", "field_path": "code", "data_type": "str", "sample_value": "000000"}, {"field_name": "message", "field_path": "message", "data_type": "NoneType", "sample_value": null}, {"field_name": "resultBody", "field_path": "resultBody", "data_type": "dict", "sample_value": "{'total': 1445, 'size': 10, 'current': 1, 'records': [{'projectMsgId': '1942422593200898048', 'workO"}, {"field_name": "total", "field_path": "resultBody.total", "data_type": "int", "sample_value": "1445"}, {"field_name": "size", "field_path": "resultBody.size", "data_type": "int", "sample_value": "10"}, {"field_name": "current", "field_path": "resultBody.current", "data_type": "int", "sample_value": "1"}, {"field_name": "records", "field_path": "resultBody.records", "data_type": "list", "sample_value": "[{'projectMsgId': '1942422593200898048', 'workOrderMsgId': 'GD76020250708111442151796', 'shutOrderMs"}, {"field_name": "projectMsgId", "field_path": "resultBody.records[0].projectMsgId", "data_type": "str", "sample_value": "1942422593200898048"}, {"field_name": "workOrderMsgId", "field_path": "resultBody.records[0].workOrderMsgId", "data_type": "str", "sample_value": "GD76020250708111442151796"}, {"field_name": "shutOrderMsgId", "field_path": "resultBody.records[0].shutOrderMsgId", "data_type": "NoneType", "sample_value": null}, {"field_name": "selectMsgId", "field_path": "resultBody.records[0].selectMsgId", "data_type": "NoneType", "sample_value": null}, {"field_name": "selectApplyId", "field_path": "resultBody.records[0].selectApplyId", "data_type": "NoneType", "sample_value": null}, {"field_name": "projectName", "field_path": "resultBody.records[0].projectName", "data_type": "str", "sample_value": "中山市坤鹏电子科技有限公司信息化建设项目"}, {"field_name": "selectName", "field_path": "resultBody.records[0].selectName", "data_type": "str", "sample_value": "中山移动某智慧园区项目"}, {"field_name": "count", "field_path": "resultBody.records[0].count", "data_type": "str", "sample_value": "0"}, {"field_name": "projectNo", "field_path": "resultBody.records[0].projectNo", "data_type": "str", "sample_value": "CMGDZSICT20250707037"}, {"field_name": "selectType", "field_path": "resultBody.records[0].selectType", "data_type": "NoneType", "sample_value": null}, {"field_name": "selectTypeValue", "field_path": "resultBody.records[0].selectTypeValue", "data_type": "NoneType", "sample_value": null}, {"field_name": "projectType", "field_path": "resultBody.records[0].projectType", "data_type": "str", "sample_value": "10"}, {"field_name": "projectLabel", "field_path": "resultBody.records[0].projectLabel", "data_type": "str", "sample_value": "54"}, {"field_name": "businessArea", "field_path": "resultBody.records[0].businessArea", "data_type": "str", "sample_value": "760"}, {"field_name": "businessAreaValue", "field_path": "resultBody.records[0].businessAreaValue", "data_type": "str", "sample_value": "中山"}, {"field_name": "startTime", "field_path": "resultBody.records[0].startTime", "data_type": "str", "sample_value": "2025-07-10 00:00:00"}, {"field_name": "selectStatus", "field_path": "resultBody.records[0].selectStatus", "data_type": "str", "sample_value": "1001"}, {"field_name": "selectStatusValue", "field_path": "resultBody.records[0].selectStatusValue", "data_type": "str", "sample_value": "审核通过"}, {"field_name": "initiateDepartment", "field_path": "resultBody.records[0].initiateDepartment", "data_type": "NoneType", "sample_value": null}, {"field_name": "createTime", "field_path": "resultBody.records[0].createTime", "data_type": "str", "sample_value": "2025-07-08 11:16:49"}, {"field_name": "isFixedSoftness", "field_path": "resultBody.records[0].isFixedSoftness", "data_type": "str", "sample_value": "0"}, {"field_name": "createStaff", "field_path": "resultBody.records[0].createStaff", "data_type": "str", "sample_value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"field_name": "createStaffValue", "field_path": "resultBody.records[0].createStaffValue", "data_type": "str", "sample_value": "刘桓旭"}, {"field_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "field_path": "resultBody.records[0].nextTodoHandler", "data_type": "str", "sample_value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"field_name": "nextTodoHandlerValue", "field_path": "resultBody.records[0].nextTodoHandlerValue", "data_type": "str", "sample_value": "刘桓旭"}, {"field_name": "isOperable", "field_path": "resultBody.records[0].isOperable", "data_type": "str", "sample_value": "0"}, {"field_name": "changeType1", "field_path": "resultBody.records[0].changeType1", "data_type": "NoneType", "sample_value": null}, {"field_name": "changeType2", "field_path": "resultBody.records[0].changeType2", "data_type": "NoneType", "sample_value": null}, {"field_name": "isTerminable", "field_path": "resultBody.records[0].isTerminable", "data_type": "str", "sample_value": "0"}, {"field_name": "isAllowSecond", "field_path": "resultBody.records[0].isAllowSecond", "data_type": "NoneType", "sample_value": null}, {"field_name": "selectCategory", "field_path": "resultBody.records[0].selectCategory", "data_type": "str", "sample_value": "1"}, {"field_name": "selectCategoryValue", "field_path": "resultBody.records[0].selectCategoryValue", "data_type": "str", "sample_value": "项目甄选"}, {"field_name": "dpcsSelectSecondNegotiate", "field_path": "resultBody.records[0].dpcsSelectSecondNegotiate", "data_type": "NoneType", "sample_value": null}, {"field_name": "pages", "field_path": "resultBody.pages", "data_type": "int", "sample_value": "145"}]}}, {"index": 9, "url": "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "method": "POST", "request_data": {"timestamp": "2025-07-08T19:51:18.459126", "url": "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "method": "POST", "headers": {"sec-ch-ua-platform": "\"Windows\"", "referer": "https://dict.gmcc.net:30722/ptn/main/selectDemand", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\"", "content-type": "application/json;charset=UTF-8", "sec-ch-ua-mobile": "?0"}, "post_data": "{\"selectCategory\":[\"1\",\"3\"],\"projectName\":\"测试项目\",\"projectNo\":\"\",\"selectName\":\"\",\"businessArea\":\"\",\"selectStatus\":\"\",\"projectMsgId\":\"\",\"currentPage\":1,\"pageSize\":10}", "post_data_json": {"selectCategory": ["1", "3"], "projectName": "测试项目", "projectNo": "", "selectName": "", "businessArea": "", "selectStatus": "", "projectMsgId": "", "currentPage": 1, "pageSize": 10}, "query_params": {}, "resource_type": "xhr"}, "response_data": {"timestamp": "2025-07-08T19:51:15.734960", "url": "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "status": 200, "status_text": "", "headers": {"transfer-encoding": "chunked", "location_gray": "location_rest_v1", "date": "<PERSON><PERSON>, 08 Jul 2025 11:51:12 GMT", "content-type": "application/json;charset=UTF-8", "server": "nginx", "connection": "keep-alive"}, "body": "{\"busiDate\":\"2025-07-08 19:51:12\",\"code\":\"000000\",\"message\":null,\"resultBody\":{\"total\":1445,\"size\":10,\"current\":1,\"records\":[{\"projectMsgId\":\"1942422593200898048\",\"workOrderMsgId\":\"GD76020250708111442151796\",\"shutOrderMsgId\":null,\"selectMsgId\":null,\"selectApplyId\":null,\"projectName\":\"中山市坤鹏电子科技有限公司信息化建设项目\",\"selectName\":\"中山移动某智慧园区项目\",\"count\":\"0\",\"projectNo\":\"CMGDZSICT20250707037\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"54\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2025-07-10 00:00:00\",\"selectStatus\":\"1001\",\"selectStatusValue\":\"审核通过\",\"initiateDepartment\":null,\"createTime\":\"2025-07-08 11:16:49\",\"isFixedSoftness\":\"0\",\"createStaff\":\"liuhuanxu\",\"createStaffValue\":\"刘桓旭\",\"nextTodoHandler\":\"liuhuanxu\",\"nextTodoHandlerValue\":\"刘桓旭\",\"isOperable\":\"0\",\"changeType1\":null,\"changeType2\":null,\"isTerminable\":\"0\",\"isAllowSecond\":null,\"selectCategory\":\"1\",\"selectCategoryValue\":\"项目甄选\",\"dpcsSelectSecondNegotiate\":null},{\"projectMsgId\":\"1942116921179553792\",\"workOrderMsgId\":\"GD76020250707145959860730\",\"shutOrderMsgId\":null,\"selectMsgId\":\"1942400615916158976\",\"selectApplyId\":null,\"projectName\":\"中山市火炬开发区第一幼儿园2025年智慧安防进校园门禁系统维护项目\",\"selectName\":\"中山移动校园门禁项目\",\"count\":\"1\",\"projectNo\":\"CMGDZSICT20250514031\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"54\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2023-03-07 00:00:00\",\"selectStatus\":\"1002\",\"selectStatusValue\":\"审核通过(已制定方案)\",\"initiateDepartment\":null,\"createTime\":\"2025-07-07 15:02:11\",\"isFixedSoftness\":\"0\",\"createStaff\":\"zhongyuan01\",\"createStaffValue\":\"钟源\",\"nextTodoHandler\":\"zhongyuan01\",\"nextTodoHandlerValue\":\"钟源\",\"isOperable\":\"0\",\"changeType1\":null,\"changeType2\":null,\"isTerminable\":\"0\",\"isAllowSecond\":null,\"selectCategory\":\"1\",\"selectCategoryValue\":\"项目甄选\",\"dpcsSelectSecondNegotiate\":null},{\"projectMsgId\":\"1942069083955445760\",\"workOrderMsgId\":\"GD76020250707114834742565\",\"shutOrderMsgId\":null,\"selectMsgId\":\"1942114146337079296\",\"selectApplyId\":\"1942141579274665984\",\"projectName\":\"横栏消防支队LCD+AP+报警项目\",\"selectName\":\"中山移动横栏消防救援支队LCD+报警项目\",\"count\":\"1\",\"projectNo\":\"CMGDZSICT20250603017\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"30\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2025-06-16 00:00:00\",\"selectStatus\":\"1002\",\"selectStatusValue\":\"审核通过(已制定方案)\",\"initiateDepartment\":null,\"createTime\":\"2025-07-07 11:52:06\",\"isFixedSoftness\":\"0\",\"createStaff\":\"huangyijun2\",\"createStaffValue\":\"黄奕俊\",\"nextTodoHandler\":\"huangyijun2\",\"nextTodoHandlerValue\":\"黄奕俊\",\"isOperable\":\"0\",\"changeType1\":null,\"changeType2\":null,\"isTerminable\":\"0\",\"isAllowSecond\":null,\"selectCategory\":\"1\",\"selectCategoryValue\":\"项目甄选\",\"dpcsSelectSecondNegotiate\":null},{\"projectMsgId\":\"1942023416348327936\",\"workOrderMsgId\":\"GD76020250707084708189250\",\"shutOrderMsgId\":null,\"selectMsgId\":\"1942387861012529152\",\"selectApplyId\":\"1942490547577864192\",\"projectName\":\"社会保险业务智能稽核复审项目\",\"selectName\":\"中山移动业务智能稽核复审项目\",\"count\":\"1\",\"projectNo\":\"CMGDZSICT20250707001\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"54\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2025-07-07 00:00:00\",\"selectStatus\":\"1002\",\"selectStatusValue\":\"审核通过(已制定方案)\",\"initiateDepartment\":null,\"createTime\":\"2025-07-07 08:50:38\",\"isFixedSoftness\":\"0\",\"createStaff\":\"oujiahui3\",\"createStaffValue\":\"欧嘉慧\",\"nextTodoHandler\":\"oujiahui3\",\"nextTodoHandlerValue\":\"欧嘉慧\",\"isOperable\":\"0\",\"changeType1\":null,\"changeType2\":null,\"isTerminable\":\"0\",\"isAllowSecond\":null,\"selectCategory\":\"1\",\"selectCategoryValue\":\"项目甄选\",\"dpcsSelectSecondNegotiate\":null},{\"projectMsgId\":\"1940961734821855232\",\"workOrderMsgId\":\"GD76020250704102830314437\",\"shutOrderMsgId\":null,\"selectMsgId\":\"1942029594583285760\",\"selectApplyId\":\"1942138444196003840\",\"projectName\":\"嘉钦工业园区智能化项目\",\"selectName\":\"中山移动嘉钦工业园区智能化项目\",\"count\":\"1\",\"projectNo\":\"CMGDZSICT20250616063\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"10\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2025-07-04 00:00:00\",\"selectStatus\":\"1002\",\"selectStatusValue\":\"审核通过(已制定方案)\",\"initiateDepartment\":null,\"createTime\":\"2025-07-04 10:31:53\",\"isFixedSoftness\":\"0\",\"createStaff\":\"hehaoming\",\"createStaffValue\":\"何浩明\",\"nextTodoHandler\":\"hehaoming\",\"nextTodoHandlerValue\":\"何浩明\",\"isOperable\":\"0\",\"changeType1\":null,\"changeType2\":null,\"isTerminable\":\"0\",\"isAllowSecond\":null,\"selectCategory\":\"1\",\"selectCategoryValue\":\"项目甄选\",\"dpcsSelectSecondNegotiate\":null},{\"projectMsgId\":\"1940953867796529152\",\"workOrderMsgId\":\"GD76020250704095622517846\",\"shutOrderMsgId\":null,\"selectMsgId\":\"1942032646556008448\",\"selectApplyId\":\"1942138704502898688\",\"projectName\":\"绩东一德原北路三线下地整治工程\",\"selectName\":\"中山移动小榄镇某村信息化项目（第二次）\",\"count\":\"1\",\"projectNo\":\"CMGDZSICT20250604017\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"54\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2025-07-18 00:00:00\",\"selectStatus\":\"1002\",\"selectStatusValue\":\"审核通过(已制定方案)\",\"initiateDepartmen", "body_json": {"busiDate": "2025-07-08 19:51:12", "code": "000000", "message": null, "resultBody": {"total": 1445, "size": 10, "current": 1, "records": [{"projectMsgId": "1942422593200898048", "workOrderMsgId": "GD76020250708111442151796", "shutOrderMsgId": null, "selectMsgId": null, "selectApplyId": null, "projectName": "中山市坤鹏电子科技有限公司信息化建设项目", "selectName": "中山移动某智慧园区项目", "count": "0", "projectNo": "CMGDZSICT20250707037", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-10 00:00:00", "selectStatus": "1001", "selectStatusValue": "审核通过", "initiateDepartment": null, "createTime": "2025-07-08 11:16:49", "isFixedSoftness": "0", "createStaff": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createStaffValue": "刘桓旭", "nextTodoHandler": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nextTodoHandlerValue": "刘桓旭", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1942116921179553792", "workOrderMsgId": "GD76020250707145959860730", "shutOrderMsgId": null, "selectMsgId": "1942400615916158976", "selectApplyId": null, "projectName": "中山市火炬开发区第一幼儿园2025年智慧安防进校园门禁系统维护项目", "selectName": "中山移动校园门禁项目", "count": "1", "projectNo": "CMGDZSICT20250514031", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2023-03-07 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-07 15:02:11", "isFixedSoftness": "0", "createStaff": "zhongyuan01", "createStaffValue": "钟源", "nextTodoHandler": "zhongyuan01", "nextTodoHandlerValue": "钟源", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1942069083955445760", "workOrderMsgId": "GD76020250707114834742565", "shutOrderMsgId": null, "selectMsgId": "1942114146337079296", "selectApplyId": "1942141579274665984", "projectName": "横栏消防支队LCD+AP+报警项目", "selectName": "中山移动横栏消防救援支队LCD+报警项目", "count": "1", "projectNo": "CMGDZSICT20250603017", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "30", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-06-16 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-07 11:52:06", "isFixedSoftness": "0", "createStaff": "huangyijun2", "createStaffValue": "黄奕俊", "nextTodoHandler": "huangyijun2", "nextTodoHandlerValue": "黄奕俊", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1942023416348327936", "workOrderMsgId": "GD76020250707084708189250", "shutOrderMsgId": null, "selectMsgId": "1942387861012529152", "selectApplyId": "1942490547577864192", "projectName": "社会保险业务智能稽核复审项目", "selectName": "中山移动业务智能稽核复审项目", "count": "1", "projectNo": "CMGDZSICT20250707001", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-07 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-07 08:50:38", "isFixedSoftness": "0", "createStaff": "oujiahui3", "createStaffValue": "欧嘉慧", "nextTodoHandler": "oujiahui3", "nextTodoHandlerValue": "欧嘉慧", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940961734821855232", "workOrderMsgId": "GD76020250704102830314437", "shutOrderMsgId": null, "selectMsgId": "1942029594583285760", "selectApplyId": "1942138444196003840", "projectName": "嘉钦工业园区智能化项目", "selectName": "中山移动嘉钦工业园区智能化项目", "count": "1", "projectNo": "CMGDZSICT20250616063", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "10", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-04 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-04 10:31:53", "isFixedSoftness": "0", "createStaff": "<PERSON><PERSON><PERSON>", "createStaffValue": "何浩明", "nextTodoHandler": "<PERSON><PERSON><PERSON>", "nextTodoHandlerValue": "何浩明", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940953867796529152", "workOrderMsgId": "GD76020250704095622517846", "shutOrderMsgId": null, "selectMsgId": "1942032646556008448", "selectApplyId": "1942138704502898688", "projectName": "绩东一德原北路三线下地整治工程", "selectName": "中山移动小榄镇某村信息化项目（第二次）", "count": "1", "projectNo": "CMGDZSICT20250604017", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-18 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-04 10:00:37", "isFixedSoftness": "0", "createStaff": "<PERSON><PERSON><PERSON>", "createStaffValue": "何浩明", "nextTodoHandler": "<PERSON><PERSON><PERSON>", "nextTodoHandlerValue": "何浩明", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940705441884323840", "workOrderMsgId": "GD76020250703173321491482", "shutOrderMsgId": null, "selectMsgId": "1942149984940900352", "selectApplyId": null, "projectName": "中山市南朗医院信息应急系统建设服务项目", "selectName": "中山移动C镇区信息应急系统建设服务项目", "count": "1", "projectNo": "CMGDZSICT20250626070", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-07 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-03 17:33:28", "isFixedSoftness": "0", "createStaff": "<PERSON><PERSON><PERSON><PERSON>", "createStaffValue": "李远锋", "nextTodoHandler": "<PERSON><PERSON><PERSON><PERSON>", "nextTodoHandlerValue": "李远锋", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940668636770254848", "workOrderMsgId": "GD76020250703150544590539", "shutOrderMsgId": null, "selectMsgId": "1940700852573356032", "selectApplyId": "1941088269851607040", "projectName": "中山市第一职业技术学校智慧课程建设项目", "selectName": "中山移动中山市第一职业技术学校智慧课程建设项目", "count": "1", "projectNo": "CMGDZSICT20250703027", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "30", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-03 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-03 15:07:13", "isFixedSoftness": "0", "createStaff": "huangyijun2", "createStaffValue": "黄奕俊", "nextTodoHandler": "huangyijun2", "nextTodoHandlerValue": "黄奕俊", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940620473380290560", "workOrderMsgId": "GD76020250703113310790860", "shutOrderMsgId": null, "selectMsgId": "1941038380107087872", "selectApplyId": "1941098200453529600", "projectName": "中山市第一中学教育装备、医务室设备等物资采购项目", "selectName": "中山市第一中学教育装备、医务室设备等物资采购项目", "count": "1", "projectNo": "CMGDZSICT20250625079", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-03 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-03 11:55:50", "isFixedSoftness": "0", "createStaff": "wangxiaohua2", "createStaffValue": "汪晓华", "nextTodoHandler": "wangxiaohua2", "nextTodoHandlerValue": "汪晓华", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}, {"projectMsgId": "1940221567911772160", "workOrderMsgId": "GD76020250702092609860107", "shutOrderMsgId": null, "selectMsgId": "1940229628432924672", "selectApplyId": "1940957015177084928", "projectName": "中山市第二人民医院智能发光药筐系统采购项目", "selectName": "2025智能发光药筐系统项目（第二次）", "count": "1", "projectNo": "CMGDZSICT20250619024", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2025-07-04 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2025-07-02 09:30:44", "isFixedSoftness": "0", "createStaff": "sucaiping", "createStaffValue": "苏彩萍", "nextTodoHandler": "sucaiping", "nextTodoHandlerValue": "苏彩萍", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}], "pages": 145}}, "content_type": "application/json;charset=UTF-8"}, "parsed_request": {"url_path": "/partner/materialManage/pnrSelectProject/querySelectProjectList", "query_parameters": {}, "body_parameters": {"selectCategory": ["1", "3"], "projectName": "测试项目", "projectNo": "", "selectName": "", "businessArea": "", "selectStatus": "", "projectMsgId": "", "currentPage": 1, "pageSize": 10}, "headers": {"content-type": "application/json;charset=UTF-8"}}, "parsed_response": {"status_code": 200, "content_type": "application/json;charset=UTF-8", "headers": {"content-type": "application/json;charset=UTF-8"}, "body_structure": {"busiDate": {"type": "str", "path": "busiDate"}, "code": {"type": "str", "path": "code"}, "message": {"type": "NoneType", "path": "message"}, "resultBody": {"type": "dict", "path": "resultBody"}}, "data_fields": [{"field_name": "busiDate", "field_path": "busiDate", "data_type": "str", "sample_value": "2025-07-08 19:51:12"}, {"field_name": "code", "field_path": "code", "data_type": "str", "sample_value": "000000"}, {"field_name": "message", "field_path": "message", "data_type": "NoneType", "sample_value": null}, {"field_name": "resultBody", "field_path": "resultBody", "data_type": "dict", "sample_value": "{'total': 1445, 'size': 10, 'current': 1, 'records': [{'projectMsgId': '1942422593200898048', 'workO"}, {"field_name": "total", "field_path": "resultBody.total", "data_type": "int", "sample_value": "1445"}, {"field_name": "size", "field_path": "resultBody.size", "data_type": "int", "sample_value": "10"}, {"field_name": "current", "field_path": "resultBody.current", "data_type": "int", "sample_value": "1"}, {"field_name": "records", "field_path": "resultBody.records", "data_type": "list", "sample_value": "[{'projectMsgId': '1942422593200898048', 'workOrderMsgId': 'GD76020250708111442151796', 'shutOrderMs"}, {"field_name": "projectMsgId", "field_path": "resultBody.records[0].projectMsgId", "data_type": "str", "sample_value": "1942422593200898048"}, {"field_name": "workOrderMsgId", "field_path": "resultBody.records[0].workOrderMsgId", "data_type": "str", "sample_value": "GD76020250708111442151796"}, {"field_name": "shutOrderMsgId", "field_path": "resultBody.records[0].shutOrderMsgId", "data_type": "NoneType", "sample_value": null}, {"field_name": "selectMsgId", "field_path": "resultBody.records[0].selectMsgId", "data_type": "NoneType", "sample_value": null}, {"field_name": "selectApplyId", "field_path": "resultBody.records[0].selectApplyId", "data_type": "NoneType", "sample_value": null}, {"field_name": "projectName", "field_path": "resultBody.records[0].projectName", "data_type": "str", "sample_value": "中山市坤鹏电子科技有限公司信息化建设项目"}, {"field_name": "selectName", "field_path": "resultBody.records[0].selectName", "data_type": "str", "sample_value": "中山移动某智慧园区项目"}, {"field_name": "count", "field_path": "resultBody.records[0].count", "data_type": "str", "sample_value": "0"}, {"field_name": "projectNo", "field_path": "resultBody.records[0].projectNo", "data_type": "str", "sample_value": "CMGDZSICT20250707037"}, {"field_name": "selectType", "field_path": "resultBody.records[0].selectType", "data_type": "NoneType", "sample_value": null}, {"field_name": "selectTypeValue", "field_path": "resultBody.records[0].selectTypeValue", "data_type": "NoneType", "sample_value": null}, {"field_name": "projectType", "field_path": "resultBody.records[0].projectType", "data_type": "str", "sample_value": "10"}, {"field_name": "projectLabel", "field_path": "resultBody.records[0].projectLabel", "data_type": "str", "sample_value": "54"}, {"field_name": "businessArea", "field_path": "resultBody.records[0].businessArea", "data_type": "str", "sample_value": "760"}, {"field_name": "businessAreaValue", "field_path": "resultBody.records[0].businessAreaValue", "data_type": "str", "sample_value": "中山"}, {"field_name": "startTime", "field_path": "resultBody.records[0].startTime", "data_type": "str", "sample_value": "2025-07-10 00:00:00"}, {"field_name": "selectStatus", "field_path": "resultBody.records[0].selectStatus", "data_type": "str", "sample_value": "1001"}, {"field_name": "selectStatusValue", "field_path": "resultBody.records[0].selectStatusValue", "data_type": "str", "sample_value": "审核通过"}, {"field_name": "initiateDepartment", "field_path": "resultBody.records[0].initiateDepartment", "data_type": "NoneType", "sample_value": null}, {"field_name": "createTime", "field_path": "resultBody.records[0].createTime", "data_type": "str", "sample_value": "2025-07-08 11:16:49"}, {"field_name": "isFixedSoftness", "field_path": "resultBody.records[0].isFixedSoftness", "data_type": "str", "sample_value": "0"}, {"field_name": "createStaff", "field_path": "resultBody.records[0].createStaff", "data_type": "str", "sample_value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"field_name": "createStaffValue", "field_path": "resultBody.records[0].createStaffValue", "data_type": "str", "sample_value": "刘桓旭"}, {"field_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "field_path": "resultBody.records[0].nextTodoHandler", "data_type": "str", "sample_value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"field_name": "nextTodoHandlerValue", "field_path": "resultBody.records[0].nextTodoHandlerValue", "data_type": "str", "sample_value": "刘桓旭"}, {"field_name": "isOperable", "field_path": "resultBody.records[0].isOperable", "data_type": "str", "sample_value": "0"}, {"field_name": "changeType1", "field_path": "resultBody.records[0].changeType1", "data_type": "NoneType", "sample_value": null}, {"field_name": "changeType2", "field_path": "resultBody.records[0].changeType2", "data_type": "NoneType", "sample_value": null}, {"field_name": "isTerminable", "field_path": "resultBody.records[0].isTerminable", "data_type": "str", "sample_value": "0"}, {"field_name": "isAllowSecond", "field_path": "resultBody.records[0].isAllowSecond", "data_type": "NoneType", "sample_value": null}, {"field_name": "selectCategory", "field_path": "resultBody.records[0].selectCategory", "data_type": "str", "sample_value": "1"}, {"field_name": "selectCategoryValue", "field_path": "resultBody.records[0].selectCategoryValue", "data_type": "str", "sample_value": "项目甄选"}, {"field_name": "dpcsSelectSecondNegotiate", "field_path": "resultBody.records[0].dpcsSelectSecondNegotiate", "data_type": "NoneType", "sample_value": null}, {"field_name": "pages", "field_path": "resultBody.pages", "data_type": "int", "sample_value": "145"}]}}, {"index": 10, "url": "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "method": "POST", "request_data": {"timestamp": "2025-07-08T19:51:21.801401", "url": "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "method": "POST", "headers": {"sec-ch-ua-platform": "\"Windows\"", "referer": "https://dict.gmcc.net:30722/ptn/main/selectDemand", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\"", "content-type": "application/json;charset=UTF-8", "sec-ch-ua-mobile": "?0"}, "post_data": "{\"selectCategory\":[\"1\",\"3\"],\"projectName\":\"\",\"projectNo\":\"\",\"selectName\":\"\",\"businessArea\":\"\",\"selectStatus\":\"\",\"projectMsgId\":\"\",\"currentPage\":1,\"pageSize\":10}", "post_data_json": {"selectCategory": ["1", "3"], "projectName": "", "projectNo": "", "selectName": "", "businessArea": "", "selectStatus": "", "projectMsgId": "", "currentPage": 1, "pageSize": 10}, "query_params": {}, "resource_type": "xhr"}, "response_data": {"timestamp": "2025-07-08T19:51:20.356713", "url": "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "status": 200, "status_text": "", "headers": {"transfer-encoding": "chunked", "location_gray": "location_rest_v1", "date": "<PERSON><PERSON>, 08 Jul 2025 11:51:17 GMT", "content-type": "application/json;charset=UTF-8", "server": "nginx", "connection": "keep-alive"}, "body": "{\"busiDate\":\"2025-07-08 19:51:17\",\"code\":\"000000\",\"message\":null,\"resultBody\":{\"total\":1,\"size\":10,\"current\":1,\"records\":[{\"projectMsgId\":\"1669542762748231680\",\"workOrderMsgId\":null,\"shutOrderMsgId\":null,\"selectMsgId\":\"1671373976840880128\",\"selectApplyId\":null,\"projectName\":\"云安全渗透测试项目\",\"selectName\":\"中山移动云安全渗透测试服务项目\",\"count\":\"1\",\"projectNo\":\"CMGDZSICT20230529015\",\"selectType\":null,\"selectTypeValue\":null,\"projectType\":\"10\",\"projectLabel\":\"30,54\",\"businessArea\":\"760\",\"businessAreaValue\":\"中山\",\"startTime\":\"2023-05-18 00:00:00\",\"selectStatus\":\"1002\",\"selectStatusValue\":\"审核通过(已制定方案)\",\"initiateDepartment\":null,\"createTime\":\"2023-06-16 11:09:47\",\"isFixedSoftness\":\"0\",\"createStaff\":\"zhangweizhan\",\"createStaffValue\":\"张维展\",\"nextTodoHandler\":\"tangxiaofang\",\"nextTodoHandlerValue\":\"唐晓芳\",\"isOperable\":\"0\",\"changeType1\":null,\"changeType2\":null,\"isTerminable\":\"0\",\"isAllowSecond\":null,\"selectCategory\":\"1\",\"selectCategoryValue\":\"项目甄选\",\"dpcsSelectSecondNegotiate\":null}],\"pages\":1}}", "body_json": {"busiDate": "2025-07-08 19:51:17", "code": "000000", "message": null, "resultBody": {"total": 1, "size": 10, "current": 1, "records": [{"projectMsgId": "1669542762748231680", "workOrderMsgId": null, "shutOrderMsgId": null, "selectMsgId": "1671373976840880128", "selectApplyId": null, "projectName": "云安全渗透测试项目", "selectName": "中山移动云安全渗透测试服务项目", "count": "1", "projectNo": "CMGDZSICT20230529015", "selectType": null, "selectTypeValue": null, "projectType": "10", "projectLabel": "30,54", "businessArea": "760", "businessAreaValue": "中山", "startTime": "2023-05-18 00:00:00", "selectStatus": "1002", "selectStatusValue": "审核通过(已制定方案)", "initiateDepartment": null, "createTime": "2023-06-16 11:09:47", "isFixedSoftness": "0", "createStaff": "zhangweizhan", "createStaffValue": "张维展", "nextTodoHandler": "tang<PERSON><PERSON><PERSON>", "nextTodoHandlerValue": "唐晓芳", "isOperable": "0", "changeType1": null, "changeType2": null, "isTerminable": "0", "isAllowSecond": null, "selectCategory": "1", "selectCategoryValue": "项目甄选", "dpcsSelectSecondNegotiate": null}], "pages": 1}}, "content_type": "application/json;charset=UTF-8"}, "parsed_request": {"url_path": "/partner/materialManage/pnrSelectProject/querySelectProjectList", "query_parameters": {}, "body_parameters": {"selectCategory": ["1", "3"], "projectName": "", "projectNo": "", "selectName": "", "businessArea": "", "selectStatus": "", "projectMsgId": "", "currentPage": 1, "pageSize": 10}, "headers": {"content-type": "application/json;charset=UTF-8"}}, "parsed_response": {"status_code": 200, "content_type": "application/json;charset=UTF-8", "headers": {"content-type": "application/json;charset=UTF-8"}, "body_structure": {"busiDate": {"type": "str", "path": "busiDate"}, "code": {"type": "str", "path": "code"}, "message": {"type": "NoneType", "path": "message"}, "resultBody": {"type": "dict", "path": "resultBody", "nested": {"total": {"type": "int", "path": "resultBody.total"}, "size": {"type": "int", "path": "resultBody.size"}, "current": {"type": "int", "path": "resultBody.current"}, "records": {"type": "list", "path": "resultBody.records", "nested": {"type": "array", "length": 1, "item_structure": {"projectMsgId": {"type": "str", "path": "resultBody.records[0].projectMsgId"}, "workOrderMsgId": {"type": "NoneType", "path": "resultBody.records[0].workOrderMsgId"}, "shutOrderMsgId": {"type": "NoneType", "path": "resultBody.records[0].shutOrderMsgId"}, "selectMsgId": {"type": "str", "path": "resultBody.records[0].selectMsgId"}, "selectApplyId": {"type": "NoneType", "path": "resultBody.records[0].selectApplyId"}, "projectName": {"type": "str", "path": "resultBody.records[0].projectName"}, "selectName": {"type": "str", "path": "resultBody.records[0].selectName"}, "count": {"type": "str", "path": "resultBody.records[0].count"}, "projectNo": {"type": "str", "path": "resultBody.records[0].projectNo"}, "selectType": {"type": "NoneType", "path": "resultBody.records[0].selectType"}, "selectTypeValue": {"type": "NoneType", "path": "resultBody.records[0].selectTypeValue"}, "projectType": {"type": "str", "path": "resultBody.records[0].projectType"}, "projectLabel": {"type": "str", "path": "resultBody.records[0].projectLabel"}, "businessArea": {"type": "str", "path": "resultBody.records[0].businessArea"}, "businessAreaValue": {"type": "str", "path": "resultBody.records[0].businessAreaValue"}, "startTime": {"type": "str", "path": "resultBody.records[0].startTime"}, "selectStatus": {"type": "str", "path": "resultBody.records[0].selectStatus"}, "selectStatusValue": {"type": "str", "path": "resultBody.records[0].selectStatusValue"}, "initiateDepartment": {"type": "NoneType", "path": "resultBody.records[0].initiateDepartment"}, "createTime": {"type": "str", "path": "resultBody.records[0].createTime"}, "isFixedSoftness": {"type": "str", "path": "resultBody.records[0].isFixedSoftness"}, "createStaff": {"type": "str", "path": "resultBody.records[0].createStaff"}, "createStaffValue": {"type": "str", "path": "resultBody.records[0].createStaffValue"}, "nextTodoHandler": {"type": "str", "path": "resultBody.records[0].nextTodoHandler"}, "nextTodoHandlerValue": {"type": "str", "path": "resultBody.records[0].nextTodoHandlerValue"}, "isOperable": {"type": "str", "path": "resultBody.records[0].isOperable"}, "changeType1": {"type": "NoneType", "path": "resultBody.records[0].changeType1"}, "changeType2": {"type": "NoneType", "path": "resultBody.records[0].changeType2"}, "isTerminable": {"type": "str", "path": "resultBody.records[0].isTerminable"}, "isAllowSecond": {"type": "NoneType", "path": "resultBody.records[0].isAllowSecond"}, "selectCategory": {"type": "str", "path": "resultBody.records[0].selectCategory"}, "selectCategoryValue": {"type": "str", "path": "resultBody.records[0].selectCategoryValue"}, "dpcsSelectSecondNegotiate": {"type": "NoneType", "path": "resultBody.records[0].dpcsSelectSecondNegotiate"}}}}, "pages": {"type": "int", "path": "resultBody.pages"}}}}, "data_fields": [{"field_name": "busiDate", "field_path": "busiDate", "data_type": "str", "sample_value": "2025-07-08 19:51:17"}, {"field_name": "code", "field_path": "code", "data_type": "str", "sample_value": "000000"}, {"field_name": "message", "field_path": "message", "data_type": "NoneType", "sample_value": null}, {"field_name": "resultBody", "field_path": "resultBody", "data_type": "dict", "sample_value": "{'total': 1, 'size': 10, 'current': 1, 'records': [{'projectMsgId': '1669542762748231680', 'workOrde"}, {"field_name": "total", "field_path": "resultBody.total", "data_type": "int", "sample_value": "1"}, {"field_name": "size", "field_path": "resultBody.size", "data_type": "int", "sample_value": "10"}, {"field_name": "current", "field_path": "resultBody.current", "data_type": "int", "sample_value": "1"}, {"field_name": "records", "field_path": "resultBody.records", "data_type": "list", "sample_value": "[{'projectMsgId': '1669542762748231680', 'workOrderMsgId': None, 'shutOrderMsgId': None, 'selectMsgI"}, {"field_name": "projectMsgId", "field_path": "resultBody.records[0].projectMsgId", "data_type": "str", "sample_value": "1669542762748231680"}, {"field_name": "workOrderMsgId", "field_path": "resultBody.records[0].workOrderMsgId", "data_type": "NoneType", "sample_value": null}, {"field_name": "shutOrderMsgId", "field_path": "resultBody.records[0].shutOrderMsgId", "data_type": "NoneType", "sample_value": null}, {"field_name": "selectMsgId", "field_path": "resultBody.records[0].selectMsgId", "data_type": "str", "sample_value": "1671373976840880128"}, {"field_name": "selectApplyId", "field_path": "resultBody.records[0].selectApplyId", "data_type": "NoneType", "sample_value": null}, {"field_name": "projectName", "field_path": "resultBody.records[0].projectName", "data_type": "str", "sample_value": "云安全渗透测试项目"}, {"field_name": "selectName", "field_path": "resultBody.records[0].selectName", "data_type": "str", "sample_value": "中山移动云安全渗透测试服务项目"}, {"field_name": "count", "field_path": "resultBody.records[0].count", "data_type": "str", "sample_value": "1"}, {"field_name": "projectNo", "field_path": "resultBody.records[0].projectNo", "data_type": "str", "sample_value": "CMGDZSICT20230529015"}, {"field_name": "selectType", "field_path": "resultBody.records[0].selectType", "data_type": "NoneType", "sample_value": null}, {"field_name": "selectTypeValue", "field_path": "resultBody.records[0].selectTypeValue", "data_type": "NoneType", "sample_value": null}, {"field_name": "projectType", "field_path": "resultBody.records[0].projectType", "data_type": "str", "sample_value": "10"}, {"field_name": "projectLabel", "field_path": "resultBody.records[0].projectLabel", "data_type": "str", "sample_value": "30,54"}, {"field_name": "businessArea", "field_path": "resultBody.records[0].businessArea", "data_type": "str", "sample_value": "760"}, {"field_name": "businessAreaValue", "field_path": "resultBody.records[0].businessAreaValue", "data_type": "str", "sample_value": "中山"}, {"field_name": "startTime", "field_path": "resultBody.records[0].startTime", "data_type": "str", "sample_value": "2023-05-18 00:00:00"}, {"field_name": "selectStatus", "field_path": "resultBody.records[0].selectStatus", "data_type": "str", "sample_value": "1002"}, {"field_name": "selectStatusValue", "field_path": "resultBody.records[0].selectStatusValue", "data_type": "str", "sample_value": "审核通过(已制定方案)"}, {"field_name": "initiateDepartment", "field_path": "resultBody.records[0].initiateDepartment", "data_type": "NoneType", "sample_value": null}, {"field_name": "createTime", "field_path": "resultBody.records[0].createTime", "data_type": "str", "sample_value": "2023-06-16 11:09:47"}, {"field_name": "isFixedSoftness", "field_path": "resultBody.records[0].isFixedSoftness", "data_type": "str", "sample_value": "0"}, {"field_name": "createStaff", "field_path": "resultBody.records[0].createStaff", "data_type": "str", "sample_value": "zhangweizhan"}, {"field_name": "createStaffValue", "field_path": "resultBody.records[0].createStaffValue", "data_type": "str", "sample_value": "张维展"}, {"field_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "field_path": "resultBody.records[0].nextTodoHandler", "data_type": "str", "sample_value": "tang<PERSON><PERSON><PERSON>"}, {"field_name": "nextTodoHandlerValue", "field_path": "resultBody.records[0].nextTodoHandlerValue", "data_type": "str", "sample_value": "唐晓芳"}, {"field_name": "isOperable", "field_path": "resultBody.records[0].isOperable", "data_type": "str", "sample_value": "0"}, {"field_name": "changeType1", "field_path": "resultBody.records[0].changeType1", "data_type": "NoneType", "sample_value": null}, {"field_name": "changeType2", "field_path": "resultBody.records[0].changeType2", "data_type": "NoneType", "sample_value": null}, {"field_name": "isTerminable", "field_path": "resultBody.records[0].isTerminable", "data_type": "str", "sample_value": "0"}, {"field_name": "isAllowSecond", "field_path": "resultBody.records[0].isAllowSecond", "data_type": "NoneType", "sample_value": null}, {"field_name": "selectCategory", "field_path": "resultBody.records[0].selectCategory", "data_type": "str", "sample_value": "1"}, {"field_name": "selectCategoryValue", "field_path": "resultBody.records[0].selectCategoryValue", "data_type": "str", "sample_value": "项目甄选"}, {"field_name": "dpcsSelectSecondNegotiate", "field_path": "resultBody.records[0].dpcsSelectSecondNegotiate", "data_type": "NoneType", "sample_value": null}, {"field_name": "pages", "field_path": "resultBody.pages", "data_type": "int", "sample_value": "1"}]}}], "data_structures": {"request_fields": {"groupId": {"type": "str", "sample_values": ["200025", "2000222"], "param_type": "query_parameters", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/sys/rCache/getDictListByGroupId2?groupId=200025", "https://dict.gmcc.net:30722/partner/materialManage/sys/rCache/getDictListByGroupId2?groupId=2000222"]}, "opType": {"type": "str", "sample_values": ["1"], "param_type": "query_parameters", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/dataManage/partnerRecruitController/getCityListByType?opType=1"]}, "selecCategory": {"type": "str", "sample_values": ["1", "1"], "param_type": "body_parameters", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "currentPage": {"type": "int", "sample_values": ["1", "1", "1", "1", "1"], "param_type": "body_parameters", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "pageSize": {"type": "int", "sample_values": ["10", "10", "10", "10", "10"], "param_type": "body_parameters", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "selectCategory": {"type": "list", "sample_values": ["['1', '3']", "['1', '3']", "['1', '3']"], "param_type": "body_parameters", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "projectName": {"type": "str", "sample_values": ["", "测试项目", ""], "param_type": "body_parameters", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "projectNo": {"type": "str", "sample_values": ["", "", ""], "param_type": "body_parameters", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "selectName": {"type": "str", "sample_values": ["", "", ""], "param_type": "body_parameters", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "businessArea": {"type": "str", "sample_values": ["", "", ""], "param_type": "body_parameters", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "selectStatus": {"type": "str", "sample_values": ["", "", ""], "param_type": "body_parameters", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "projectMsgId": {"type": "str", "sample_values": ["", "", ""], "param_type": "body_parameters", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}}, "response_fields": {"busiDate": {"type": "str", "sample_values": ["2025-07-08 19:51:02", "2025-07-08 19:51:02", "2025-07-08 19:51:12", "2025-07-08 19:51:12", "2025-07-08 19:51:17"], "field_path": "busiDate", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "code": {"type": "str", "sample_values": ["000000", "000000", "000000", "000000", "000000"], "field_path": "code", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "message": {"type": "NoneType", "sample_values": [], "field_path": "message", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "resultBody": {"type": "dict", "sample_values": ["{'total': 1445, 'size': 10, 'current': 1, 'records': [{'projectMsgId': '1942422593200898048', 'workO", "{'total': 1445, 'size': 10, 'current': 1, 'records': [{'projectMsgId': '1942422593200898048', 'workO", "{'total': 1445, 'size': 10, 'current': 1, 'records': [{'projectMsgId': '1942422593200898048', 'workO", "{'total': 1445, 'size': 10, 'current': 1, 'records': [{'projectMsgId': '1942422593200898048', 'workO", "{'total': 1, 'size': 10, 'current': 1, 'records': [{'projectMsgId': '1669542762748231680', 'workOrde"], "field_path": "resultBody", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "total": {"type": "int", "sample_values": ["1445", "1445", "1445", "1445", "1"], "field_path": "resultBody.total", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "size": {"type": "int", "sample_values": ["10", "10", "10", "10", "10"], "field_path": "resultBody.size", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "current": {"type": "int", "sample_values": ["1", "1", "1", "1", "1"], "field_path": "resultBody.current", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "records": {"type": "list", "sample_values": ["[{'projectMsgId': '1942422593200898048', 'workOrderMsgId': 'GD76020250708111442151796', 'shutOrderMs", "[{'projectMsgId': '1942422593200898048', 'workOrderMsgId': 'GD76020250708111442151796', 'shutOrderMs", "[{'projectMsgId': '1942422593200898048', 'workOrderMsgId': 'GD76020250708111442151796', 'shutOrderMs", "[{'projectMsgId': '1942422593200898048', 'workOrderMsgId': 'GD76020250708111442151796', 'shutOrderMs", "[{'projectMsgId': '1669542762748231680', 'workOrderMsgId': None, 'shutOrderMsgId': None, 'selectMsgI"], "field_path": "resultBody.records", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "projectMsgId": {"type": "str", "sample_values": ["1942422593200898048", "1942422593200898048", "1942422593200898048", "1942422593200898048", "1669542762748231680"], "field_path": "resultBody.records[0].projectMsgId", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "workOrderMsgId": {"type": "str", "sample_values": ["GD76020250708111442151796", "GD76020250708111442151796", "GD76020250708111442151796", "GD76020250708111442151796"], "field_path": "resultBody.records[0].workOrderMsgId", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "shutOrderMsgId": {"type": "NoneType", "sample_values": [], "field_path": "resultBody.records[0].shutOrderMsgId", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "selectMsgId": {"type": "NoneType", "sample_values": ["1671373976840880128"], "field_path": "resultBody.records[0].selectMsgId", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "selectApplyId": {"type": "NoneType", "sample_values": [], "field_path": "resultBody.records[0].selectApplyId", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "projectName": {"type": "str", "sample_values": ["中山市坤鹏电子科技有限公司信息化建设项目", "中山市坤鹏电子科技有限公司信息化建设项目", "中山市坤鹏电子科技有限公司信息化建设项目", "中山市坤鹏电子科技有限公司信息化建设项目", "云安全渗透测试项目"], "field_path": "resultBody.records[0].projectName", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "selectName": {"type": "str", "sample_values": ["中山移动某智慧园区项目", "中山移动某智慧园区项目", "中山移动某智慧园区项目", "中山移动某智慧园区项目", "中山移动云安全渗透测试服务项目"], "field_path": "resultBody.records[0].selectName", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "count": {"type": "str", "sample_values": ["0", "0", "0", "0", "1"], "field_path": "resultBody.records[0].count", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "projectNo": {"type": "str", "sample_values": ["CMGDZSICT20250707037", "CMGDZSICT20250707037", "CMGDZSICT20250707037", "CMGDZSICT20250707037", "CMGDZSICT20230529015"], "field_path": "resultBody.records[0].projectNo", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "selectType": {"type": "NoneType", "sample_values": [], "field_path": "resultBody.records[0].selectType", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "selectTypeValue": {"type": "NoneType", "sample_values": [], "field_path": "resultBody.records[0].selectTypeValue", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "projectType": {"type": "str", "sample_values": ["10", "10", "10", "10", "10"], "field_path": "resultBody.records[0].projectType", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "projectLabel": {"type": "str", "sample_values": ["54", "54", "54", "54", "30,54"], "field_path": "resultBody.records[0].projectLabel", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "businessArea": {"type": "str", "sample_values": ["760", "760", "760", "760", "760"], "field_path": "resultBody.records[0].businessArea", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "businessAreaValue": {"type": "str", "sample_values": ["中山", "中山", "中山", "中山", "中山"], "field_path": "resultBody.records[0].businessAreaValue", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "startTime": {"type": "str", "sample_values": ["2025-07-10 00:00:00", "2025-07-10 00:00:00", "2025-07-10 00:00:00", "2025-07-10 00:00:00", "2023-05-18 00:00:00"], "field_path": "resultBody.records[0].startTime", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "selectStatus": {"type": "str", "sample_values": ["1001", "1001", "1001", "1001", "1002"], "field_path": "resultBody.records[0].selectStatus", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "selectStatusValue": {"type": "str", "sample_values": ["审核通过", "审核通过", "审核通过", "审核通过", "审核通过(已制定方案)"], "field_path": "resultBody.records[0].selectStatusValue", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "initiateDepartment": {"type": "NoneType", "sample_values": [], "field_path": "resultBody.records[0].initiateDepartment", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "createTime": {"type": "str", "sample_values": ["2025-07-08 11:16:49", "2025-07-08 11:16:49", "2025-07-08 11:16:49", "2025-07-08 11:16:49", "2023-06-16 11:09:47"], "field_path": "resultBody.records[0].createTime", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "isFixedSoftness": {"type": "str", "sample_values": ["0", "0", "0", "0", "0"], "field_path": "resultBody.records[0].isFixedSoftness", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "createStaff": {"type": "str", "sample_values": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zhangweizhan"], "field_path": "resultBody.records[0].createStaff", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "createStaffValue": {"type": "str", "sample_values": ["刘桓旭", "刘桓旭", "刘桓旭", "刘桓旭", "张维展"], "field_path": "resultBody.records[0].createStaffValue", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "nextTodoHandler": {"type": "str", "sample_values": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tang<PERSON><PERSON><PERSON>"], "field_path": "resultBody.records[0].nextTodoHandler", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "nextTodoHandlerValue": {"type": "str", "sample_values": ["刘桓旭", "刘桓旭", "刘桓旭", "刘桓旭", "唐晓芳"], "field_path": "resultBody.records[0].nextTodoHandlerValue", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "isOperable": {"type": "str", "sample_values": ["0", "0", "0", "0", "0"], "field_path": "resultBody.records[0].isOperable", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "changeType1": {"type": "NoneType", "sample_values": [], "field_path": "resultBody.records[0].changeType1", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "changeType2": {"type": "NoneType", "sample_values": [], "field_path": "resultBody.records[0].changeType2", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "isTerminable": {"type": "str", "sample_values": ["0", "0", "0", "0", "0"], "field_path": "resultBody.records[0].isTerminable", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "isAllowSecond": {"type": "NoneType", "sample_values": [], "field_path": "resultBody.records[0].isAllowSecond", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "selectCategory": {"type": "str", "sample_values": ["1", "1", "1", "1", "1"], "field_path": "resultBody.records[0].selectCategory", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "selectCategoryValue": {"type": "str", "sample_values": ["项目甄选", "项目甄选", "项目甄选", "项目甄选", "项目甄选"], "field_path": "resultBody.records[0].selectCategoryValue", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "dpcsSelectSecondNegotiate": {"type": "NoneType", "sample_values": [], "field_path": "resultBody.records[0].dpcsSelectSecondNegotiate", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}, "pages": {"type": "int", "sample_values": ["145", "145", "145", "145", "1"], "field_path": "resultBody.pages", "interfaces": ["https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList", "https://dict.gmcc.net:30722/partner/materialManage/pnrSelectProject/querySelectProjectList"]}}}}