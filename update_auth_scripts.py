#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量更新脚本认证系统
将所有数据获取脚本更新为使用 AuthLoader
"""

import os
import re
import sys
from pathlib import Path

def update_script_auth(script_path: str) -> bool:
    """
    更新单个脚本的认证系统
    
    Args:
        script_path: 脚本文件路径
        
    Returns:
        bool: 更新是否成功
    """
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 1. 添加 AuthLoader 导入
        if 'from auth_loader import AuthLoader' not in content:
            # 查找数据库导入行
            db_import_pattern = r'(from database\.db_config import.*)'
            if re.search(db_import_pattern, content):
                content = re.sub(
                    db_import_pattern,
                    r'\1\nfrom auth_loader import AuthLoader',
                    content
                )
            else:
                # 如果没有找到数据库导入，在其他导入后添加
                import_pattern = r'(import.*\n)(class|def|\n\n)'
                content = re.sub(
                    import_pattern,
                    r'\1from auth_loader import AuthLoader\n\n\2',
                    content
                )
        
        # 2. 更新 __init__ 方法
        # 查找类的 __init__ 方法
        init_pattern = r'def __init__\(self[^)]*\):'
        if re.search(init_pattern, content):
            # 替换硬编码的认证逻辑
            
            # 移除硬编码的 Bearer token
            content = re.sub(
                r"'Authorization': \"Bearer [^\"]+\"",
                "",
                content
            )
            
            # 移除硬编码的 headers 设置
            content = re.sub(
                r'# 默认请求头\s*self\.headers = \{[^}]+\}',
                "",
                content,
                flags=re.DOTALL
            )
            
            # 移除 Cookie 相关的初始化
            content = re.sub(
                r'# 默认Cookie\s*self\.cookies = \{[^}]+\}',
                "",
                content,
                flags=re.DOTALL
            )
            
            # 移除 load_cookies_from_file 调用
            content = re.sub(
                r'self\.load_cookies_from_file\([^)]*\)',
                "",
                content
            )
            
            # 添加 AuthLoader 逻辑
            auth_loader_code = '''
        # 使用 AuthLoader 加载认证信息
        auth_loader = AuthLoader(cookie_file_path)
        if auth_loader.load_auth_data():
            # 更新session的认证信息
            auth_loader.update_session(self.session)
            logger.info("✅ 认证信息加载成功")
        else:
            logger.error("❌ 认证信息加载失败")
            
        # 设置基本请求头（AuthLoader会自动添加认证相关的headers）
        self.session.headers.update({
            'Host': "dict.gmcc.net:30722",
            'Origin': "http://dict.gmcc.net:30722",
            'Referer': "http://dict.gmcc.net:30722/ptn/main/selectDemand",
        })'''
            
            # 在 session 创建后添加认证逻辑
            session_pattern = r'(self\.session = requests\.Session\(\))'
            if re.search(session_pattern, content):
                content = re.sub(
                    session_pattern,
                    r'\1' + auth_loader_code,
                    content
                )
        
        # 3. 移除不需要的方法
        methods_to_remove = [
            'load_cookies_from_file',
            '_set_default_cookies',
            'update_cookies',
            'get_cookie_string'
        ]
        
        for method in methods_to_remove:
            # 移除整个方法定义
            method_pattern = rf'def {method}\(self[^:]*\):.*?(?=\n    def|\n\nclass|\nif __name__|\Z)'
            content = re.sub(method_pattern, '', content, flags=re.DOTALL)
        
        # 4. 更新请求调用
        # 替换 requests.post/get 调用
        content = re.sub(
            r'response = requests\.(post|get)\(\s*url,\s*([^)]*headers=self\.headers[^)]*)\)',
            r'response = self.session.\1(url, json=payload, timeout=30)',
            content
        )
        
        content = re.sub(
            r'response = self\.session\.(post|get)\(\s*url,\s*([^)]*headers=self\.headers[^)]*)\)',
            r'response = self.session.\1(url, json=payload, timeout=30)',
            content
        )
        
        # 替换 data=json.dumps(payload) 为 json=payload
        content = re.sub(
            r'data=json\.dumps\(payload\)',
            'json=payload',
            content
        )
        
        # 移除 cookies 参数
        content = re.sub(
            r',\s*cookies=self\.cookies',
            '',
            content
        )
        
        # 5. 清理多余的空行
        content = re.sub(r'\n\n\n+', '\n\n', content)
        
        # 只有内容发生变化时才写入文件
        if content != original_content:
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 更新成功: {script_path}")
            return True
        else:
            print(f"⏭️ 无需更新: {script_path}")
            return False
            
    except Exception as e:
        print(f"❌ 更新失败: {script_path} - {e}")
        return False

def main():
    """主函数"""
    print("🔧 批量更新脚本认证系统")
    print("="*50)
    
    # 需要更新的脚本列表
    scripts_to_update = [
        "scripts/fetch_queryLocalAuditTrackHistory.py",
        "scripts/fetch_queryLocalAuditTrackHistory_ksm.py", 
        "scripts/fetch_queryPartnerSelectDetail.py",
        "scripts/fetch_querySelectAuditTrackHistory.py",
        "scripts/fetch_querySelectProjectDetail.py",
        "scripts/fetch_querySelectStage.py",
    ]
    
    success_count = 0
    total_count = len(scripts_to_update)
    
    for script_path in scripts_to_update:
        if os.path.exists(script_path):
            if update_script_auth(script_path):
                success_count += 1
        else:
            print(f"⚠️ 文件不存在: {script_path}")
    
    print(f"\n📊 更新完成: {success_count}/{total_count} 个脚本更新成功")
    
    if success_count == total_count:
        print("🎉 所有脚本更新成功！")
    else:
        print("⚠️ 部分脚本更新失败，请手动检查")

if __name__ == "__main__":
    main()
