#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
执行创建视图的SQL脚本
"""

import sys
import os
from database.db_config import DatabaseManager, ZHENXUAN_DB_CONFIG

def execute_create_view():
    """执行创建视图的SQL"""
    db_manager = DatabaseManager(ZHENXUAN_DB_CONFIG)
    
    if not db_manager.connect():
        print("❌ 数据库连接失败")
        return False
    
    try:
        # 读取SQL文件
        with open('create_view_zhenxuan_queryselectapplydetail.sql', 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        print("📋 开始执行创建视图SQL...")
        
        # 分割SQL语句（按分号分割）
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        with db_manager.get_cursor() as cursor:
            for i, sql in enumerate(sql_statements, 1):
                if sql:
                    print(f"🔄 执行第 {i} 条SQL语句...")
                    print(f"   {sql[:100]}...")
                    cursor.execute(sql)
                    print(f"✅ 第 {i} 条SQL执行成功")
            
            # 提交事务
            db_manager.connection.commit()
            print("✅ 所有SQL语句执行完成，事务已提交")
        
        # 验证视图是否创建成功
        print("\n📋 验证视图创建结果...")
        with db_manager.get_cursor() as cursor:
            # 检查视图是否存在
            cursor.execute("SHOW TABLES LIKE 'v_zhenxuan_queryselectapplydetail'")
            result = cursor.fetchone()
            
            if result:
                print("✅ 视图 v_zhenxuan_queryselectapplydetail 创建成功")
                
                # 查看视图结构
                print("\n📋 视图字段结构:")
                cursor.execute("DESCRIBE v_zhenxuan_queryselectapplydetail")
                columns = cursor.fetchall()
                for col in columns:
                    print(f"  {col['Field']} - {col['Type']} - {col['Null']}")
                
                # 查询几条示例数据
                print(f"\n📋 视图数据示例（前3条）:")
                cursor.execute("""
                SELECT 
                    select_apply_id, project_name, customer_name, project_code,
                    apply_status_value, rating, create_time, realEndTime
                FROM v_zhenxuan_queryselectapplydetail 
                LIMIT 3
                """)
                
                rows = cursor.fetchall()
                for i, row in enumerate(rows, 1):
                    print(f"  {i}. 申请ID: {row['select_apply_id']}")
                    print(f"     项目名称: {row['project_name']}")
                    print(f"     客户名称: {row['customer_name']}")
                    print(f"     项目编号: {row['project_code']}")
                    print(f"     申请状态: {row['apply_status_value']}")
                    print(f"     评级: {row['rating']}")
                    print(f"     创建时间: {row['create_time']}")
                    print(f"     实际结束时间: {row['realEndTime']}")
                    print()
                
                # 统计视图记录数
                cursor.execute("SELECT COUNT(*) as total FROM v_zhenxuan_queryselectapplydetail")
                count_result = cursor.fetchone()
                print(f"📊 视图总记录数: {count_result['total']}")
                
            else:
                print("❌ 视图创建失败")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    success = execute_create_view()
    if success:
        print("\n🎉 视图创建完成！")
    else:
        print("\n❌ 视图创建失败！")
