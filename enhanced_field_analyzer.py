"""
增强的字段分析器
深度分析页面HTML元素，提取中文字段对应的原始英文名称
只提取真实存在的英文属性，不进行翻译
"""

import asyncio
import json
import os
from datetime import datetime
from playwright.async_api import async_playwright


class EnhancedFieldAnalyzer:
    def __init__(self):
        self.target_url = "https://dict.gmcc.net:30722/ptn/main/selectDemand"
        self.cookie_dir = "cookies"
        self.analysis_dir = "field_analysis"
        self.cookies = None
        
        # 创建分析结果目录
        if not os.path.exists(self.analysis_dir):
            os.makedirs(self.analysis_dir)
        
    def load_cookies(self):
        """加载cookies"""
        try:
            latest_cookie_file = os.path.join(self.cookie_dir, "cookies_dict_zhenxuan.json")
            
            if os.path.exists(latest_cookie_file):
                with open(latest_cookie_file, 'r', encoding='utf-8') as f:
                    self.cookies = json.load(f)
                print(f"✅ 成功加载 {len(self.cookies)} 个cookie")
                return True
            else:
                print("❌ 未找到cookies文件")
                return False
                
        except Exception as e:
            print(f"❌ 加载cookies失败: {e}")
            return False
    
    async def analyze_field_mappings(self):
        """分析字段的中英文映射关系"""
        print("🔍 开始分析字段的中英文映射关系...")
        
        if not self.load_cookies():
            return False
            
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False, slow_mo=500)
            context = await browser.new_context(ignore_https_errors=True)
            
            try:
                await context.add_cookies(self.cookies)
                page = await context.new_page()
                
                print(f"🌐 访问页面: {self.target_url}")
                await page.goto(self.target_url, wait_until='networkidle')
                await asyncio.sleep(3)
                
                # 分析结果
                field_mappings = {
                    "table_fields": [],
                    "form_fields": [],
                    "button_fields": [],
                    "api_fields": [],
                    "css_classes": [],
                    "data_attributes": []
                }
                
                # 1. 分析表格字段
                field_mappings["table_fields"] = await self.analyze_table_fields(page)
                
                # 2. 分析表单字段
                field_mappings["form_fields"] = await self.analyze_form_fields(page)
                
                # 3. 分析按钮字段
                field_mappings["button_fields"] = await self.analyze_button_fields(page)
                
                # 4. 分析API相关字段
                field_mappings["api_fields"] = await self.analyze_api_fields(page)
                
                # 5. 分析CSS类名
                field_mappings["css_classes"] = await self.analyze_css_classes(page)
                
                # 6. 分析数据属性
                field_mappings["data_attributes"] = await self.analyze_data_attributes(page)
                
                # 保存分析结果
                await self.save_field_mappings(field_mappings)
                
                # 生成字段映射文档
                await self.generate_field_mapping_doc(field_mappings)
                
                print("✅ 字段映射分析完成")
                await asyncio.sleep(5)
                return True
                
            except Exception as e:
                print(f"❌ 分析过程中出错: {e}")
                return False
                
            finally:
                await browser.close()
    
    async def analyze_table_fields(self, page):
        """分析表格字段的中英文映射"""
        print("📊 分析表格字段...")
        
        table_fields = []
        tables = await page.query_selector_all('table')
        
        for table_index, table in enumerate(tables):
            print(f"  分析表格 {table_index + 1}...")
            
            # 获取表头单元格
            header_cells = await table.query_selector_all('th, thead td, tr:first-child td')
            
            for cell_index, cell in enumerate(header_cells):
                try:
                    # 获取显示文本
                    display_text = await cell.inner_text()
                    display_text = display_text.strip()
                    
                    if not display_text:
                        continue
                    
                    # 获取HTML属性
                    cell_html = await cell.inner_html()
                    
                    # 提取各种可能的英文标识
                    field_info = {
                        "table_index": table_index + 1,
                        "cell_index": cell_index + 1,
                        "display_text": display_text,
                        "id": await cell.get_attribute('id') or "",
                        "class": await cell.get_attribute('class') or "",
                        "data_field": await cell.get_attribute('data-field') or "",
                        "data_column": await cell.get_attribute('data-column') or "",
                        "data_key": await cell.get_attribute('data-key') or "",
                        "name": await cell.get_attribute('name') or "",
                        "title": await cell.get_attribute('title') or "",
                        "aria_label": await cell.get_attribute('aria-label') or "",
                        "inner_html": cell_html
                    }
                    
                    # 查找内部元素的属性
                    inner_elements = await cell.query_selector_all('*')
                    inner_attributes = []
                    
                    for element in inner_elements:
                        element_attrs = {}
                        for attr in ['id', 'class', 'name', 'data-field', 'data-column', 'data-key', 'ng-model', 'v-model']:
                            attr_value = await element.get_attribute(attr)
                            if attr_value:
                                element_attrs[attr] = attr_value
                        
                        if element_attrs:
                            inner_attributes.append(element_attrs)
                    
                    field_info["inner_attributes"] = inner_attributes
                    
                    # 只保留有意义的字段信息
                    if display_text and (field_info["id"] or field_info["data_field"] or 
                                       field_info["data_column"] or field_info["name"] or 
                                       inner_attributes):
                        table_fields.append(field_info)
                
                except Exception as e:
                    print(f"    ⚠️ 分析单元格时出错: {e}")
        
        return table_fields
    
    async def analyze_form_fields(self, page):
        """分析表单字段的中英文映射"""
        print("📝 分析表单字段...")
        
        form_fields = []
        
        # 分析输入框
        inputs = await page.query_selector_all('input, select, textarea')
        
        for input_index, input_elem in enumerate(inputs):
            try:
                field_info = {
                    "element_type": await input_elem.evaluate('el => el.tagName.toLowerCase()'),
                    "input_type": await input_elem.get_attribute('type') or "",
                    "id": await input_elem.get_attribute('id') or "",
                    "name": await input_elem.get_attribute('name') or "",
                    "class": await input_elem.get_attribute('class') or "",
                    "placeholder": await input_elem.get_attribute('placeholder') or "",
                    "data_field": await input_elem.get_attribute('data-field') or "",
                    "data_column": await input_elem.get_attribute('data-column') or "",
                    "ng_model": await input_elem.get_attribute('ng-model') or "",
                    "v_model": await input_elem.get_attribute('v-model') or "",
                    "aria_label": await input_elem.get_attribute('aria-label') or "",
                    "title": await input_elem.get_attribute('title') or ""
                }
                
                # 查找关联的label
                label_text = ""
                try:
                    # 通过for属性查找label
                    if field_info["id"]:
                        label = await page.query_selector(f'label[for="{field_info["id"]}"]')
                        if label:
                            label_text = await label.inner_text()
                    
                    # 查找父级或兄弟元素中的label
                    if not label_text:
                        parent = await input_elem.query_selector('xpath=..')
                        if parent:
                            label = await parent.query_selector('label')
                            if label:
                                label_text = await label.inner_text()
                
                except:
                    pass
                
                field_info["label_text"] = label_text.strip()
                
                # 只保留有意义的字段
                if (field_info["placeholder"] or field_info["label_text"] or 
                    field_info["name"] or field_info["id"]):
                    form_fields.append(field_info)
            
            except Exception as e:
                print(f"    ⚠️ 分析输入框时出错: {e}")
        
        return form_fields
    
    async def analyze_button_fields(self, page):
        """分析按钮字段"""
        print("🔘 分析按钮字段...")
        
        button_fields = []
        buttons = await page.query_selector_all('button, input[type="button"], input[type="submit"]')
        
        for button in buttons:
            try:
                field_info = {
                    "display_text": await button.inner_text(),
                    "id": await button.get_attribute('id') or "",
                    "class": await button.get_attribute('class') or "",
                    "name": await button.get_attribute('name') or "",
                    "data_action": await button.get_attribute('data-action') or "",
                    "onclick": await button.get_attribute('onclick') or "",
                    "ng_click": await button.get_attribute('ng-click') or "",
                    "v_on_click": await button.get_attribute('@click') or ""
                }
                
                if field_info["display_text"].strip():
                    button_fields.append(field_info)
            
            except Exception as e:
                print(f"    ⚠️ 分析按钮时出错: {e}")
        
        return button_fields
    
    async def analyze_api_fields(self, page):
        """分析API相关字段"""
        print("🌐 分析API字段...")
        
        api_fields = []
        
        try:
            # 监听网络请求
            requests = []
            
            def handle_request(request):
                if request.url.startswith('http'):
                    requests.append({
                        "url": request.url,
                        "method": request.method,
                        "headers": dict(request.headers),
                        "post_data": request.post_data
                    })
            
            page.on("request", handle_request)
            
            # 触发一些操作来捕获API请求
            try:
                # 尝试点击查询按钮
                query_button = await page.query_selector('button:has-text("查询")')
                if query_button:
                    await query_button.click()
                    await asyncio.sleep(2)
            except:
                pass
            
            # 分析捕获的请求
            for request in requests:
                if 'selectDemand' in request["url"] or 'api' in request["url"]:
                    api_fields.append(request)
        
        except Exception as e:
            print(f"    ⚠️ 分析API时出错: {e}")
        
        return api_fields
    
    async def analyze_css_classes(self, page):
        """分析CSS类名中的英文字段"""
        print("🎨 分析CSS类名...")
        
        css_classes = []
        
        try:
            # 获取页面中所有元素的class属性
            all_classes = await page.evaluate('''
                () => {
                    const classes = new Set();
                    document.querySelectorAll('*').forEach(el => {
                        if (el.className && typeof el.className === 'string') {
                            el.className.split(' ').forEach(cls => {
                                if (cls.trim()) classes.add(cls.trim());
                            });
                        }
                    });
                    return Array.from(classes);
                }
            ''')
            
            # 过滤出可能与字段相关的类名
            field_related_classes = []
            for cls in all_classes:
                # 查找包含常见字段关键词的类名
                if any(keyword in cls.lower() for keyword in 
                      ['field', 'column', 'input', 'select', 'table', 'form', 'data', 'name', 'code', 'status', 'time']):
                    field_related_classes.append(cls)
            
            css_classes = field_related_classes
        
        except Exception as e:
            print(f"    ⚠️ 分析CSS类名时出错: {e}")
        
        return css_classes
    
    async def analyze_data_attributes(self, page):
        """分析data-*属性"""
        print("📋 分析data属性...")
        
        data_attributes = []
        
        try:
            # 获取所有data-*属性
            data_attrs = await page.evaluate('''
                () => {
                    const dataAttrs = [];
                    document.querySelectorAll('*').forEach(el => {
                        for (let attr of el.attributes) {
                            if (attr.name.startsWith('data-')) {
                                dataAttrs.push({
                                    element: el.tagName.toLowerCase(),
                                    attribute: attr.name,
                                    value: attr.value,
                                    text: el.textContent ? el.textContent.trim().substring(0, 50) : ''
                                });
                            }
                        }
                    });
                    return dataAttrs;
                }
            ''')
            
            data_attributes = data_attrs
        
        except Exception as e:
            print(f"    ⚠️ 分析data属性时出错: {e}")
        
        return data_attributes
    
    async def save_field_mappings(self, field_mappings):
        """保存字段映射结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = f"{self.analysis_dir}/field_mappings_{timestamp}.json"
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(field_mappings, f, indent=2, ensure_ascii=False)
        
        print(f"💾 字段映射结果已保存: {result_file}")
    
    async def generate_field_mapping_doc(self, field_mappings):
        """生成字段映射文档"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        doc_file = f"{self.analysis_dir}/字段映射分析_{timestamp}.md"
        
        with open(doc_file, 'w', encoding='utf-8') as f:
            f.write("# 甄选需求管理页面字段映射分析\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 表格字段映射
            f.write("## 📊 表格字段映射\n\n")
            f.write("| 中文显示名 | 英文标识 | 属性类型 | 属性值 | 表格位置 |\n")
            f.write("|-----------|----------|----------|--------|----------|\n")
            
            for field in field_mappings["table_fields"]:
                english_id = ""
                attr_type = ""
                
                if field["data_field"]:
                    english_id = field["data_field"]
                    attr_type = "data-field"
                elif field["data_column"]:
                    english_id = field["data_column"]
                    attr_type = "data-column"
                elif field["id"]:
                    english_id = field["id"]
                    attr_type = "id"
                elif field["name"]:
                    english_id = field["name"]
                    attr_type = "name"
                elif field["inner_attributes"]:
                    for attr in field["inner_attributes"]:
                        if attr.get("data-field"):
                            english_id = attr["data-field"]
                            attr_type = "inner data-field"
                            break
                        elif attr.get("name"):
                            english_id = attr["name"]
                            attr_type = "inner name"
                            break
                
                if english_id:
                    f.write(f"| {field['display_text']} | {english_id} | {attr_type} | {english_id} | 表格{field['table_index']} |\n")
            
            f.write("\n")
            
            # 表单字段映射
            f.write("## 📝 表单字段映射\n\n")
            f.write("| 中文显示名 | 英文标识 | 属性类型 | 元素类型 | 占位符 |\n")
            f.write("|-----------|----------|----------|----------|--------|\n")
            
            for field in field_mappings["form_fields"]:
                display_name = field["label_text"] or field["placeholder"]
                english_id = ""
                attr_type = ""
                
                if field["name"]:
                    english_id = field["name"]
                    attr_type = "name"
                elif field["id"]:
                    english_id = field["id"]
                    attr_type = "id"
                elif field["ng_model"]:
                    english_id = field["ng_model"]
                    attr_type = "ng-model"
                elif field["v_model"]:
                    english_id = field["v_model"]
                    attr_type = "v-model"
                elif field["data_field"]:
                    english_id = field["data_field"]
                    attr_type = "data-field"
                
                if display_name and english_id:
                    f.write(f"| {display_name} | {english_id} | {attr_type} | {field['element_type']} | {field['placeholder']} |\n")
            
            f.write("\n")
            
            # 按钮映射
            f.write("## 🔘 按钮功能映射\n\n")
            f.write("| 中文按钮名 | 英文标识 | 属性类型 | 事件处理 |\n")
            f.write("|-----------|----------|----------|----------|\n")
            
            for field in field_mappings["button_fields"]:
                english_id = ""
                attr_type = ""
                event_handler = ""
                
                if field["id"]:
                    english_id = field["id"]
                    attr_type = "id"
                elif field["name"]:
                    english_id = field["name"]
                    attr_type = "name"
                elif field["data_action"]:
                    english_id = field["data_action"]
                    attr_type = "data-action"
                
                if field["onclick"]:
                    event_handler = "onclick"
                elif field["ng_click"]:
                    event_handler = "ng-click"
                elif field["v_on_click"]:
                    event_handler = "@click"
                
                if field["display_text"]:
                    f.write(f"| {field['display_text']} | {english_id} | {attr_type} | {event_handler} |\n")
            
            f.write("\n")
            
            # API字段
            if field_mappings["api_fields"]:
                f.write("## 🌐 API接口字段\n\n")
                for api in field_mappings["api_fields"]:
                    f.write(f"### {api['method']} {api['url']}\n\n")
                    if api.get("post_data"):
                        f.write(f"**请求数据**: {api['post_data']}\n\n")
            
            # 相关CSS类名
            if field_mappings["css_classes"]:
                f.write("## 🎨 相关CSS类名\n\n")
                for cls in field_mappings["css_classes"][:20]:  # 只显示前20个
                    f.write(f"- `{cls}`\n")
                f.write("\n")
            
            # Data属性
            if field_mappings["data_attributes"]:
                f.write("## 📋 Data属性映射\n\n")
                f.write("| 元素 | 属性名 | 属性值 | 元素文本 |\n")
                f.write("|------|--------|--------|----------|\n")
                
                for attr in field_mappings["data_attributes"][:20]:  # 只显示前20个
                    f.write(f"| {attr['element']} | {attr['attribute']} | {attr['value']} | {attr['text'][:30]}... |\n")
        
        print(f"📚 字段映射文档已生成: {doc_file}")


async def main():
    """主函数"""
    analyzer = EnhancedFieldAnalyzer()
    success = await analyzer.analyze_field_mappings()
    
    if success:
        print("🎉 字段映射分析完成！")
    else:
        print("❌ 分析失败")


if __name__ == "__main__":
    print("🔍 增强的字段映射分析器")
    print("📊 深度分析页面HTML元素，提取中文字段对应的原始英文名称")
    print("⚠️ 只提取真实存在的英文属性，不进行翻译")
    print("=" * 70)
    asyncio.run(main())
