"""
验证码识别测试脚本
用于测试ddddocr对保存的验证码图片的识别准确性
"""

import os
import ddddocr
from PIL import Image


def test_captcha_recognition():
    """测试验证码识别"""
    ocr = ddddocr.DdddOcr()
    
    captcha_dir = "captcha_images"
    if not os.path.exists(captcha_dir):
        print("❌ 验证码图片目录不存在")
        return
    
    # 获取所有验证码图片
    captcha_files = [f for f in os.listdir(captcha_dir) if f.endswith('.png')]
    
    if not captcha_files:
        print("❌ 没有找到验证码图片")
        return
    
    print(f"📁 找到 {len(captcha_files)} 个验证码图片")
    
    for filename in sorted(captcha_files):
        filepath = os.path.join(captcha_dir, filename)
        print(f"\n🔍 分析图片: {filename}")
        
        try:
            # 读取图片
            with open(filepath, 'rb') as f:
                img_bytes = f.read()
            
            # 使用ddddocr识别
            result = ocr.classification(img_bytes)
            print(f"🔤 OCR识别结果: '{result}' (长度: {len(result)})")
            
            # 显示图片信息
            try:
                img = Image.open(filepath)
                print(f"📏 图片尺寸: {img.size}")
                print(f"🎨 图片模式: {img.mode}")
            except Exception as e:
                print(f"⚠️ 无法读取图片信息: {e}")
            
            # 让用户手动验证
            print("👀 请查看图片并输入正确的验证码 (直接回车跳过):")
            user_input = input("正确的验证码是: ").strip()
            
            if user_input:
                if user_input.lower() == result.lower():
                    print("✅ OCR识别正确！")
                else:
                    print(f"❌ OCR识别错误！正确答案: {user_input}, OCR结果: {result}")
            else:
                print("⏭️ 跳过验证")
                
        except Exception as e:
            print(f"❌ 处理图片时出错: {e}")


def test_ocr_with_different_settings():
    """测试不同OCR设置"""
    captcha_dir = "captcha_images"
    captcha_files = [f for f in os.listdir(captcha_dir) if f.endswith('.png')]
    
    if not captcha_files:
        print("❌ 没有找到验证码图片")
        return
    
    # 使用最新的图片进行测试
    latest_file = sorted(captcha_files)[-1]
    filepath = os.path.join(captcha_dir, latest_file)
    
    print(f"🧪 使用图片进行OCR测试: {latest_file}")
    
    with open(filepath, 'rb') as f:
        img_bytes = f.read()
    
    # 测试不同的OCR配置
    configs = [
        {"beta": False, "show_ad": False},
        {"beta": True, "show_ad": False},
    ]
    
    for i, config in enumerate(configs):
        try:
            print(f"\n🔧 配置 {i+1}: {config}")
            ocr = ddddocr.DdddOcr(**config)
            result = ocr.classification(img_bytes)
            print(f"🔤 识别结果: '{result}' (长度: {len(result)})")
        except Exception as e:
            print(f"❌ 配置 {i+1} 失败: {e}")


if __name__ == "__main__":
    print("🧪 验证码识别测试程序")
    print("=" * 50)
    
    print("\n1️⃣ 基础识别测试")
    test_captcha_recognition()
    
    print("\n2️⃣ 不同配置测试")
    test_ocr_with_different_settings()
