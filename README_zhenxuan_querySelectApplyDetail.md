# 甄选申请详情数据获取程序

## 📋 程序概述

`zhenxuan_querySelectApplyDetail.py` 是用于获取和同步甄选申请详情数据的程序，基于 `querySelectApplyDetail` API 接口。

## 🎯 核心功能

### 数据来源
- **源表**: `zhenxuan_querySelectProjectList`
- **源字段**: `select_apply_id` 
- **API参数**: 将 `select_apply_id` 作为 `selectApplyId` 参数传递给API

### 数据目标
- **目标表**: `zhenxuan_querySelectApplyDetail`
- **数据内容**: 甄选申请的详细信息，包括项目信息、状态、评级等

## 🚀 使用方法

### 1. 全量数据同步（推荐）
```bash
python zhenxuan_querySelectApplyDetail.py --all
```
- 自动从 `zhenxuan_querySelectProjectList` 表获取所有 `select_apply_id`
- 逐个调用API获取详情数据
- 自动入库到 `zhenxuan_querySelectApplyDetail` 表

### 2. 单个申请ID处理
```bash
python zhenxuan_querySelectApplyDetail.py --select-apply-id 1904468953890996224
```
- 处理指定的单个 `select_apply_id`
- 适用于测试或补充特定数据

### 3. 查询已同步数据
```bash
python zhenxuan_querySelectApplyDetail.py --query --limit 10
```
- 查看数据库中已同步的数据
- 可指定查询记录数量

### 4. Cookie管理
```bash
# 使用指定Cookie文件
python zhenxuan_querySelectApplyDetail.py --cookie-file /path/to/cookies.json --all

# 更新Cookie字符串
python zhenxuan_querySelectApplyDetail.py --cookie "key1=value1; key2=value2" --all
```

## 🔧 Cookie更新流程

当遇到401错误或Cookie过期时：

1. **更新Cookie**:
   ```bash
   python login2zhenxuan_cookie.py
   ```

2. **继续数据同步**:
   ```bash
   python zhenxuan_querySelectApplyDetail.py --all
   ```

## 📊 数据统计

### 当前数据状态
- **源表记录数**: 1,533条
- **有效select_apply_id**: 1,086个
- **已同步记录**: 206条
- **同步进度**: 约19%

### 数据字段映射

| API字段 | 数据库字段 | 说明 |
|---------|------------|------|
| selectApplyId | select_apply_id | 甄选申请ID（主键） |
| projectName | project_name | 项目名称 |
| customerName | customer_name | 客户名称 |
| projectCode | project_code | 项目代码 |
| rating | rating | 评级（A/B/C/D） |
| applyStatusValue | apply_status_value | 申请状态 |
| actionRemark | action_remark | 操作备注 |

## 🛡️ 错误处理

### 常见错误及解决方案

1. **401 Unauthorized**
   - 原因：Cookie过期
   - 解决：运行 `python login2zhenxuan_cookie.py` 更新Cookie

2. **SSL证书错误**
   - 程序已自动处理（`verify=False`）

3. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 确认数据库配置正确

4. **网络超时**
   - 程序会自动重试
   - 可调整请求间隔（当前1秒）

## 📈 性能特性

- **请求间隔**: 1秒（避免请求过快）
- **错误恢复**: 单条记录失败不影响整体同步
- **数据完整性**: 保存原始JSON数据
- **增量更新**: 支持重复运行，自动更新已存在记录

## 🔍 日志监控

程序运行时会生成详细日志：
- **文件日志**: `zhenxuan_querySelectApplyDetail_fetch.log`
- **控制台日志**: 实时显示进度和状态
- **错误追踪**: 详细的错误信息和堆栈

## 📝 数据表结构

### 核心字段
- `select_apply_id`: 甄选申请ID（唯一键）
- `project_name`: 项目名称
- `customer_name`: 客户名称
- `rating`: 评级
- `apply_status_value`: 申请状态
- `action_remark`: 操作备注
- `raw_data`: 原始JSON数据

### 索引优化
- 主键索引：`select_apply_id`
- 复合索引：状态+区域、时间+评级
- 单字段索引：项目代码、工单ID等

## 🎯 最佳实践

1. **定期同步**: 建议每日运行全量同步
2. **监控日志**: 关注错误日志和同步进度
3. **Cookie维护**: 定期更新Cookie避免认证失败
4. **数据备份**: 定期备份数据库
5. **性能监控**: 关注同步速度和成功率

## 📞 技术支持

如遇问题，请检查：
1. 数据库连接状态
2. Cookie有效性
3. 网络连接
4. 日志文件中的错误信息
