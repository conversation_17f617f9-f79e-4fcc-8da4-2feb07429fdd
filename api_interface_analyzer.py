"""
API接口分析器
深度分析页面加载时的所有网络请求，包括入参和返回结果的字段结构
"""

import asyncio
import json
import os
import re
from datetime import datetime
from playwright.async_api import async_playwright
from urllib.parse import urlparse, parse_qs


class APIInterfaceAnalyzer:
    def __init__(self):
        self.target_url = "https://dict.gmcc.net:30722/ptn/main/selectDemand"
        self.cookie_dir = "cookies"
        self.api_analysis_dir = "api_analysis"
        self.cookies = None
        self.network_requests = []
        self.network_responses = []
        
        # 创建分析结果目录
        if not os.path.exists(self.api_analysis_dir):
            os.makedirs(self.api_analysis_dir)
        
    def load_cookies(self):
        """加载cookies"""
        try:
            latest_cookie_file = os.path.join(self.cookie_dir, "cookies_dict_zhenxuan.json")
            
            if os.path.exists(latest_cookie_file):
                with open(latest_cookie_file, 'r', encoding='utf-8') as f:
                    self.cookies = json.load(f)
                print(f"✅ 成功加载 {len(self.cookies)} 个cookie")
                return True
            else:
                print("❌ 未找到cookies文件")
                return False
                
        except Exception as e:
            print(f"❌ 加载cookies失败: {e}")
            return False
    
    async def analyze_api_interfaces(self):
        """分析API接口"""
        print("🌐 开始分析API接口...")
        
        if not self.load_cookies():
            return False
            
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False, slow_mo=300)
            context = await browser.new_context(ignore_https_errors=True)
            
            try:
                await context.add_cookies(self.cookies)
                page = await context.new_page()
                
                # 设置网络监听
                await self.setup_network_listeners(page)
                
                print(f"🌐 访问页面: {self.target_url}")
                
                # 清空之前的请求记录
                self.network_requests = []
                self.network_responses = []
                
                # 访问页面并等待加载完成
                await page.goto(self.target_url, wait_until='networkidle')
                await asyncio.sleep(5)  # 等待所有异步请求完成
                
                print("📊 触发更多API请求...")
                
                # 触发查询操作
                await self.trigger_search_operations(page)
                
                # 等待所有请求完成
                await asyncio.sleep(3)
                
                # 分析收集到的网络请求
                api_analysis = await self.analyze_collected_requests()
                
                # 保存分析结果
                await self.save_api_analysis(api_analysis)
                
                # 生成API文档
                await self.generate_api_documentation(api_analysis)
                
                print("✅ API接口分析完成")
                await asyncio.sleep(5)
                return True
                
            except Exception as e:
                print(f"❌ API分析过程中出错: {e}")
                return False
                
            finally:
                await browser.close()
    
    async def setup_network_listeners(self, page):
        """设置网络监听器"""
        print("🔍 设置网络监听器...")
        
        async def handle_request(request):
            try:
                # 过滤掉静态资源
                if any(ext in request.url for ext in ['.css', '.js', '.png', '.jpg', '.ico', '.woff']):
                    return
                
                request_data = {
                    "timestamp": datetime.now().isoformat(),
                    "url": request.url,
                    "method": request.method,
                    "headers": dict(request.headers),
                    "post_data": None,
                    "post_data_json": None,
                    "query_params": {},
                    "resource_type": request.resource_type
                }
                
                # 解析URL参数
                parsed_url = urlparse(request.url)
                if parsed_url.query:
                    request_data["query_params"] = parse_qs(parsed_url.query)
                
                # 获取POST数据
                if request.method in ['POST', 'PUT', 'PATCH']:
                    post_data = request.post_data
                    if post_data:
                        request_data["post_data"] = post_data
                        try:
                            # 尝试解析JSON
                            if post_data.startswith('{') or post_data.startswith('['):
                                request_data["post_data_json"] = json.loads(post_data)
                        except:
                            pass
                
                self.network_requests.append(request_data)
                print(f"📤 请求: {request.method} {request.url}")
                
            except Exception as e:
                print(f"⚠️ 处理请求时出错: {e}")
        
        async def handle_response(response):
            try:
                # 过滤掉静态资源
                if any(ext in response.url for ext in ['.css', '.js', '.png', '.jpg', '.ico', '.woff']):
                    return
                
                response_data = {
                    "timestamp": datetime.now().isoformat(),
                    "url": response.url,
                    "status": response.status,
                    "status_text": response.status_text,
                    "headers": dict(response.headers),
                    "body": None,
                    "body_json": None,
                    "content_type": response.headers.get('content-type', '')
                }
                
                # 获取响应体
                try:
                    if response.status < 400:  # 只获取成功响应的内容
                        body = await response.body()
                        if body:
                            body_text = body.decode('utf-8', errors='ignore')
                            response_data["body"] = body_text[:5000]  # 限制长度
                            
                            # 尝试解析JSON
                            if 'application/json' in response_data["content_type"]:
                                try:
                                    response_data["body_json"] = json.loads(body_text)
                                except:
                                    pass
                except Exception as e:
                    print(f"⚠️ 获取响应体失败: {e}")
                
                self.network_responses.append(response_data)
                print(f"📥 响应: {response.status} {response.url}")
                
            except Exception as e:
                print(f"⚠️ 处理响应时出错: {e}")
        
        page.on("request", handle_request)
        page.on("response", handle_response)
    
    async def trigger_search_operations(self, page):
        """触发搜索操作以获取更多API请求"""
        try:
            # 等待页面完全加载
            await asyncio.sleep(2)
            
            # 尝试点击查询按钮
            print("🔍 触发查询操作...")
            query_button = await page.query_selector('button:has-text("查询")')
            if query_button:
                await query_button.click()
                await asyncio.sleep(3)
                print("✅ 查询按钮已点击")
            
            # 尝试输入搜索条件并查询
            print("📝 输入搜索条件...")
            project_name_input = await page.query_selector('input[placeholder="项目名称"]')
            if project_name_input:
                await project_name_input.fill("测试项目")
                await asyncio.sleep(1)
                
                if query_button:
                    await query_button.click()
                    await asyncio.sleep(3)
                    print("✅ 带条件查询已执行")
            
            # 尝试清空条件
            reset_button = await page.query_selector('button:has-text("重置")')
            if reset_button:
                await reset_button.click()
                await asyncio.sleep(2)
                print("✅ 重置按钮已点击")
            
            # 尝试点击详情链接
            print("🔗 尝试点击详情链接...")
            detail_links = await page.query_selector_all('a:has-text("查看详情")')
            if detail_links and len(detail_links) > 0:
                await detail_links[0].click()
                await asyncio.sleep(3)
                print("✅ 详情链接已点击")
                
                # 返回主页面
                await page.go_back()
                await asyncio.sleep(2)
        
        except Exception as e:
            print(f"⚠️ 触发操作时出错: {e}")
    
    async def analyze_collected_requests(self):
        """分析收集到的网络请求"""
        print("📊 分析收集到的网络请求...")
        
        api_analysis = {
            "summary": {
                "total_requests": len(self.network_requests),
                "total_responses": len(self.network_responses),
                "api_requests": [],
                "static_requests": []
            },
            "api_interfaces": [],
            "data_structures": {
                "request_fields": {},
                "response_fields": {}
            }
        }
        
        # 分析每个请求
        for i, request in enumerate(self.network_requests):
            # 查找对应的响应
            response = None
            for resp in self.network_responses:
                if resp["url"] == request["url"] and abs(
                    datetime.fromisoformat(resp["timestamp"]) - 
                    datetime.fromisoformat(request["timestamp"])
                ).total_seconds() < 5:
                    response = resp
                    break
            
            # 判断是否为API请求
            if self.is_api_request(request):
                api_interface = {
                    "index": i + 1,
                    "url": request["url"],
                    "method": request["method"],
                    "request_data": request,
                    "response_data": response,
                    "parsed_request": self.parse_request_structure(request),
                    "parsed_response": self.parse_response_structure(response) if response else None
                }
                
                api_analysis["api_interfaces"].append(api_interface)
                api_analysis["summary"]["api_requests"].append({
                    "method": request["method"],
                    "url": request["url"],
                    "status": response["status"] if response else "无响应"
                })
            else:
                api_analysis["summary"]["static_requests"].append({
                    "method": request["method"],
                    "url": request["url"]
                })
        
        # 分析数据结构
        api_analysis["data_structures"] = self.analyze_data_structures(api_analysis["api_interfaces"])
        
        return api_analysis
    
    def is_api_request(self, request):
        """判断是否为API请求"""
        url = request["url"]
        
        # API请求的特征
        api_indicators = [
            '/api/',
            '/ptn/',
            'selectDemand',
            '.json',
            'application/json' in request["headers"].get('content-type', ''),
            request["method"] in ['POST', 'PUT', 'DELETE'],
            'ajax' in url.lower()
        ]
        
        # 排除静态资源
        static_indicators = [
            '.css', '.js', '.png', '.jpg', '.ico', '.woff', '.ttf', '.svg',
            '/static/', '/assets/', '/css/', '/js/', '/img/'
        ]
        
        # 如果是静态资源，不是API
        if any(indicator in url for indicator in static_indicators):
            return False
        
        # 如果有API特征，是API
        if any(indicator in url or indicator for indicator in api_indicators):
            return True
        
        # 如果是主页面HTML，不是API
        if url == self.target_url:
            return False
        
        return False
    
    def parse_request_structure(self, request):
        """解析请求结构"""
        parsed = {
            "url_path": urlparse(request["url"]).path,
            "query_parameters": {},
            "body_parameters": {},
            "headers": {}
        }
        
        # 解析查询参数
        if request["query_params"]:
            for key, values in request["query_params"].items():
                parsed["query_parameters"][key] = values[0] if len(values) == 1 else values
        
        # 解析请求体参数
        if request["post_data_json"]:
            parsed["body_parameters"] = request["post_data_json"]
        elif request["post_data"]:
            # 尝试解析表单数据
            if '=' in request["post_data"]:
                pairs = request["post_data"].split('&')
                for pair in pairs:
                    if '=' in pair:
                        key, value = pair.split('=', 1)
                        parsed["body_parameters"][key] = value
        
        # 重要的请求头
        important_headers = ['content-type', 'authorization', 'x-requested-with']
        for header in important_headers:
            if header in request["headers"]:
                parsed["headers"][header] = request["headers"][header]
        
        return parsed
    
    def parse_response_structure(self, response):
        """解析响应结构"""
        if not response:
            return None
        
        parsed = {
            "status_code": response["status"],
            "content_type": response["content_type"],
            "headers": {},
            "body_structure": {},
            "data_fields": []
        }
        
        # 重要的响应头
        important_headers = ['content-type', 'content-length', 'set-cookie']
        for header in important_headers:
            if header in response["headers"]:
                parsed["headers"][header] = response["headers"][header]
        
        # 解析响应体结构
        if response["body_json"]:
            parsed["body_structure"] = self.analyze_json_structure(response["body_json"])
            parsed["data_fields"] = self.extract_data_fields(response["body_json"])
        
        return parsed
    
    def analyze_json_structure(self, json_data, path=""):
        """分析JSON结构"""
        structure = {}
        
        if isinstance(json_data, dict):
            for key, value in json_data.items():
                current_path = f"{path}.{key}" if path else key
                structure[key] = {
                    "type": type(value).__name__,
                    "path": current_path
                }
                
                if isinstance(value, (dict, list)) and len(str(value)) < 1000:
                    structure[key]["nested"] = self.analyze_json_structure(value, current_path)
        
        elif isinstance(json_data, list) and json_data:
            structure = {
                "type": "array",
                "length": len(json_data),
                "item_structure": self.analyze_json_structure(json_data[0], f"{path}[0]") if json_data else {}
            }
        
        return structure
    
    def extract_data_fields(self, json_data, prefix=""):
        """提取数据字段"""
        fields = []
        
        if isinstance(json_data, dict):
            for key, value in json_data.items():
                field_path = f"{prefix}.{key}" if prefix else key
                fields.append({
                    "field_name": key,
                    "field_path": field_path,
                    "data_type": type(value).__name__,
                    "sample_value": str(value)[:100] if value is not None else None
                })
                
                if isinstance(value, dict):
                    fields.extend(self.extract_data_fields(value, field_path))
                elif isinstance(value, list) and value and isinstance(value[0], dict):
                    fields.extend(self.extract_data_fields(value[0], f"{field_path}[0]"))
        
        return fields
    
    def analyze_data_structures(self, api_interfaces):
        """分析数据结构"""
        request_fields = {}
        response_fields = {}
        
        for interface in api_interfaces:
            # 分析请求字段
            if interface["parsed_request"]:
                for param_type in ["query_parameters", "body_parameters"]:
                    params = interface["parsed_request"].get(param_type, {})
                    for key, value in params.items():
                        if key not in request_fields:
                            request_fields[key] = {
                                "type": type(value).__name__,
                                "sample_values": [],
                                "param_type": param_type,
                                "interfaces": []
                            }
                        request_fields[key]["sample_values"].append(str(value))
                        request_fields[key]["interfaces"].append(interface["url"])
            
            # 分析响应字段
            if interface["parsed_response"] and interface["parsed_response"]["data_fields"]:
                for field in interface["parsed_response"]["data_fields"]:
                    field_name = field["field_name"]
                    if field_name not in response_fields:
                        response_fields[field_name] = {
                            "type": field["data_type"],
                            "sample_values": [],
                            "field_path": field["field_path"],
                            "interfaces": []
                        }
                    if field["sample_value"]:
                        response_fields[field_name]["sample_values"].append(field["sample_value"])
                    response_fields[field_name]["interfaces"].append(interface["url"])
        
        return {
            "request_fields": request_fields,
            "response_fields": response_fields
        }
    
    async def save_api_analysis(self, api_analysis):
        """保存API分析结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = f"{self.api_analysis_dir}/api_analysis_{timestamp}.json"
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(api_analysis, f, indent=2, ensure_ascii=False)
        
        print(f"💾 API分析结果已保存: {result_file}")
    
    async def generate_api_documentation(self, api_analysis):
        """生成API文档"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        doc_file = f"{self.api_analysis_dir}/API接口文档_{timestamp}.md"
        
        with open(doc_file, 'w', encoding='utf-8') as f:
            f.write("# 甄选需求管理页面API接口分析\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 概览
            f.write("## 📊 接口概览\n\n")
            summary = api_analysis["summary"]
            f.write(f"- **总请求数**: {summary['total_requests']}\n")
            f.write(f"- **总响应数**: {summary['total_responses']}\n")
            f.write(f"- **API请求数**: {len(summary['api_requests'])}\n")
            f.write(f"- **静态资源请求数**: {len(summary['static_requests'])}\n\n")
            
            # API接口列表
            f.write("## 🌐 API接口列表\n\n")
            if summary['api_requests']:
                f.write("| 序号 | 方法 | URL | 状态 |\n")
                f.write("|------|------|-----|------|\n")
                for i, api in enumerate(summary['api_requests']):
                    f.write(f"| {i+1} | {api['method']} | {api['url']} | {api['status']} |\n")
            else:
                f.write("未发现API接口调用\n")
            f.write("\n")
            
            # 详细接口分析
            f.write("## 📋 接口详细分析\n\n")
            for interface in api_analysis["api_interfaces"]:
                f.write(f"### 接口 {interface['index']}: {interface['method']} {interface['url']}\n\n")
                
                # 请求信息
                f.write("#### 📤 请求信息\n\n")
                f.write(f"- **URL**: `{interface['url']}`\n")
                f.write(f"- **方法**: `{interface['method']}`\n")
                
                if interface["parsed_request"]:
                    parsed_req = interface["parsed_request"]
                    
                    # 查询参数
                    if parsed_req["query_parameters"]:
                        f.write("- **查询参数**:\n")
                        for key, value in parsed_req["query_parameters"].items():
                            f.write(f"  - `{key}`: {value}\n")
                    
                    # 请求体参数
                    if parsed_req["body_parameters"]:
                        f.write("- **请求体参数**:\n")
                        f.write("```json\n")
                        f.write(json.dumps(parsed_req["body_parameters"], indent=2, ensure_ascii=False))
                        f.write("\n```\n")
                    
                    # 重要请求头
                    if parsed_req["headers"]:
                        f.write("- **请求头**:\n")
                        for key, value in parsed_req["headers"].items():
                            f.write(f"  - `{key}`: {value}\n")
                
                f.write("\n")
                
                # 响应信息
                f.write("#### 📥 响应信息\n\n")
                if interface["parsed_response"]:
                    parsed_resp = interface["parsed_response"]
                    f.write(f"- **状态码**: {parsed_resp['status_code']}\n")
                    f.write(f"- **内容类型**: {parsed_resp['content_type']}\n")
                    
                    # 响应体结构
                    if parsed_resp["body_structure"]:
                        f.write("- **响应体结构**:\n")
                        f.write("```json\n")
                        f.write(json.dumps(parsed_resp["body_structure"], indent=2, ensure_ascii=False))
                        f.write("\n```\n")
                    
                    # 数据字段
                    if parsed_resp["data_fields"]:
                        f.write("- **数据字段**:\n\n")
                        f.write("| 字段名 | 数据类型 | 字段路径 | 样本值 |\n")
                        f.write("|--------|----------|----------|--------|\n")
                        for field in parsed_resp["data_fields"][:20]:  # 限制显示数量
                            sample = field["sample_value"][:50] + "..." if field["sample_value"] and len(field["sample_value"]) > 50 else field["sample_value"]
                            f.write(f"| {field['field_name']} | {field['data_type']} | {field['field_path']} | {sample} |\n")
                else:
                    f.write("无响应数据\n")
                
                f.write("\n---\n\n")
            
            # 数据结构汇总
            f.write("## 📊 数据结构汇总\n\n")
            
            # 请求字段汇总
            f.write("### 📤 请求字段汇总\n\n")
            request_fields = api_analysis["data_structures"]["request_fields"]
            if request_fields:
                f.write("| 字段名 | 数据类型 | 参数类型 | 样本值 | 使用接口 |\n")
                f.write("|--------|----------|----------|--------|----------|\n")
                for field_name, field_info in request_fields.items():
                    sample_values = ", ".join(field_info["sample_values"][:3])
                    interfaces = ", ".join(set(field_info["interfaces"]))[:100]
                    f.write(f"| {field_name} | {field_info['type']} | {field_info['param_type']} | {sample_values} | {interfaces} |\n")
            else:
                f.write("未发现请求字段\n")
            f.write("\n")
            
            # 响应字段汇总
            f.write("### 📥 响应字段汇总\n\n")
            response_fields = api_analysis["data_structures"]["response_fields"]
            if response_fields:
                f.write("| 字段名 | 数据类型 | 字段路径 | 样本值 | 使用接口 |\n")
                f.write("|--------|----------|----------|--------|----------|\n")
                for field_name, field_info in response_fields.items():
                    sample_values = ", ".join(field_info["sample_values"][:3])
                    interfaces = ", ".join(set(field_info["interfaces"]))[:100]
                    f.write(f"| {field_name} | {field_info['type']} | {field_info['field_path']} | {sample_values} | {interfaces} |\n")
            else:
                f.write("未发现响应字段\n")
        
        print(f"📚 API接口文档已生成: {doc_file}")


async def main():
    """主函数"""
    analyzer = APIInterfaceAnalyzer()
    success = await analyzer.analyze_api_interfaces()
    
    if success:
        print("🎉 API接口分析完成！")
    else:
        print("❌ 分析失败")


if __name__ == "__main__":
    print("🌐 API接口分析器")
    print("📊 深度分析页面加载时的所有网络请求")
    print("🔍 包括入参和返回结果的字段结构分析")
    print("=" * 60)
    asyncio.run(main())
