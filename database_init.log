2025-07-08 20:19:52,551 - INFO - 🚀 开始初始化甄选数据库...
2025-07-08 20:19:53,092 - INFO - ✅ 数据库 'zhenxuandb' 创建成功
2025-07-08 20:19:53,107 - INFO - ✅ 执行SQL: USE `zhenxuandb`...
2025-07-08 20:19:53,108 - INFO - ✅ SQL文件 'D:\0回集成\dict爬虫\spider_dict 甄选\database\create_zhenxuan_database.sql' 执行成功
2025-07-08 20:19:53,110 - INFO - ✅ 成功连接到数据库: zhenxuandb
2025-07-08 20:19:53,113 - ERROR - ❌ 插入初始数据失败: (1146, "Table 'zhenxuandb.sync_status' doesn't exist")
2025-07-08 20:20:35,602 - INFO - 🚀 开始初始化甄选数据库...
2025-07-08 20:20:35,615 - INFO - ✅ 数据库 'zhenxuandb' 创建成功
2025-07-08 20:20:35,619 - INFO - ✅ 执行SQL: CREATE DATABASE IF NOT EXISTS `zhenxuandb` DEFAULT...
2025-07-08 20:20:35,619 - INFO - ✅ 执行SQL: USE `zhenxuandb`...
2025-07-08 20:20:35,720 - INFO - ✅ 执行SQL: CREATE TABLE `selection_projects` ( `id` BIGINT AU...
2025-07-08 20:20:35,832 - INFO - ✅ 执行SQL: CREATE TABLE `dict_data` ( `id` BIGINT AUTO_INCREM...
2025-07-08 20:20:36,213 - INFO - ✅ 执行SQL: CREATE TABLE `city_areas` ( `id` BIGINT AUTO_INCRE...
2025-07-08 20:20:36,328 - INFO - ✅ 执行SQL: CREATE TABLE `api_request_logs` ( `id` BIGINT AUTO...
2025-07-08 20:20:36,361 - INFO - ✅ 执行SQL: CREATE TABLE `sync_status` ( `id` BIGINT AUTO_INCR...
2025-07-08 20:20:36,362 - INFO - ✅ SQL文件 'D:\0回集成\dict爬虫\spider_dict 甄选\database\create_zhenxuan_database.sql' 执行成功
2025-07-08 20:20:36,370 - INFO - ✅ 成功连接到数据库: zhenxuandb
2025-07-08 20:20:36,377 - INFO - ✅ 初始数据插入成功
2025-07-08 20:20:36,377 - INFO - 🔌 数据库连接已断开
2025-07-08 20:20:36,381 - INFO - ✅ 成功连接到数据库: zhenxuandb
2025-07-08 20:20:36,421 - INFO - ✅ 创建索引: CREATE INDEX idx_selection_projects_composite ON s...
2025-07-08 20:20:36,449 - INFO - ✅ 创建索引: CREATE INDEX idx_api_logs_time_status ON api_reque...
2025-07-08 20:20:36,476 - INFO - ✅ 创建索引: CREATE INDEX idx_dict_data_active ON dict_data (gr...
2025-07-08 20:20:36,477 - INFO - ✅ 索引创建完成
2025-07-08 20:20:36,477 - INFO - 🔌 数据库连接已断开
2025-07-08 20:20:36,490 - INFO - ✅ 成功连接到数据库: zhenxuandb
2025-07-08 20:20:36,493 - INFO - ✅ 表 'selection_projects' 存在
2025-07-08 20:20:36,493 - INFO - ✅ 表 'dict_data' 存在
2025-07-08 20:20:36,493 - INFO - ✅ 表 'city_areas' 存在
2025-07-08 20:20:36,493 - INFO - ✅ 表 'api_request_logs' 存在
2025-07-08 20:20:36,494 - INFO - ✅ 表 'sync_status' 存在
2025-07-08 20:20:36,500 - INFO - 📋 表 'selection_projects' 有 37 个字段
2025-07-08 20:20:36,504 - INFO - 📋 表 'dict_data' 有 10 个字段
2025-07-08 20:20:36,509 - INFO - 📋 表 'city_areas' 有 11 个字段
2025-07-08 20:20:36,511 - INFO - 📋 表 'api_request_logs' 有 11 个字段
2025-07-08 20:20:36,514 - INFO - 📋 表 'sync_status' 有 8 个字段
2025-07-08 20:20:36,515 - INFO - 🔌 数据库连接已断开
2025-07-08 20:20:36,515 - INFO - ✅ 数据库验证通过
2025-07-08 20:20:36,515 - INFO - 🎉 甄选数据库初始化完成！
